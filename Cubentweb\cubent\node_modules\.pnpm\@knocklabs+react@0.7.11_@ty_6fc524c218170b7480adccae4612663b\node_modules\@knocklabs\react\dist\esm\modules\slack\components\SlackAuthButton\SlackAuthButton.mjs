import e, { use<PERSON>emo as w, useEffect as L } from "react";
import { useTranslations as C, useKnockClient as S, useKnockSlackClient as M, useSlackAuth as N } from "@knocklabs/react-core";
import { openPopupWindow as d } from "../../../core/utils.mjs";
/* empty css               */
import { SlackIcon as s } from "../SlackIcon/SlackIcon.mjs";
/* empty css            */
const B = ({
  slackClientId: p,
  redirectUrl: b,
  onAuthenticationComplete: a,
  scopes: l,
  additionalScopes: i
}) => {
  const {
    t
  } = C(), u = S(), {
    setConnectionStatus: c,
    connectionStatus: n,
    setActionLabel: o,
    actionLabel: m,
    errorLabel: h
  } = M(), E = w(() => ({
    scopes: l,
    additionalScopes: i
  }), [l, i]), {
    buildSlackAuthUrl: k,
    disconnectFromSlack: f
  } = N(p, b, E);
  L(() => {
    const _ = (r) => {
      if (r.origin === u.host)
        try {
          r.data === "authComplete" && c("connected"), r.data === "authFailed" && c("error"), a && a(r.data);
        } catch {
          c("error");
        }
    };
    return window.addEventListener("message", _, !1), () => {
      window.removeEventListener("message", _);
    };
  }, [u.host, a, c]);
  const g = t("slackDisconnect") || null, x = t("slackReconnect") || null;
  return n === "connecting" || n === "disconnecting" ? /* @__PURE__ */ e.createElement("div", { className: "rsk-connect__button rsk-connect__button--loading" }, /* @__PURE__ */ e.createElement(s, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", null, t(n === "connecting" ? "slackConnecting" : "slackDisconnecting"))) : n === "error" ? /* @__PURE__ */ e.createElement("button", { onClick: () => d(k()), className: "rsk-connect__button rsk-connect__button--error", onMouseEnter: () => o(x), onMouseLeave: () => o(null) }, /* @__PURE__ */ e.createElement(s, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", { className: "rsk-connect__button__text--error" }, m || h || t("slackError"))) : n === "disconnected" ? /* @__PURE__ */ e.createElement("button", { onClick: () => d(k()), className: "rsk-connect__button rsk-connect__button--disconnected" }, /* @__PURE__ */ e.createElement(s, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", null, t("slackConnect"))) : /* @__PURE__ */ e.createElement("button", { onClick: f, className: "rsk-connect__button rsk-connect__button--connected", onMouseEnter: () => o(g), onMouseLeave: () => o(null) }, /* @__PURE__ */ e.createElement(s, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", { className: "rsk-connect__button__text--connected" }, m || t("slackConnected")));
};
export {
  B as SlackAuthButton
};
//# sourceMappingURL=SlackAuthButton.mjs.map
