import e from "react";
import { useTranslations as r } from "@knocklabs/react-core";
/* empty css               */
import { SlackIcon as c } from "../SlackIcon/SlackIcon.mjs";
/* empty css            */
const o = ({
  actionButton: t
}) => {
  const {
    t: a
  } = r();
  return /* @__PURE__ */ e.createElement("div", { className: "rsk-auth" }, /* @__PURE__ */ e.createElement("div", { className: "rsk-auth__header" }, /* @__PURE__ */ e.createElement(c, { height: "32px", width: "32px" }), /* @__PURE__ */ e.createElement("div", null, t)), /* @__PURE__ */ e.createElement("div", { className: "rsk-auth__title" }, "Slack"), /* @__PURE__ */ e.createElement("div", { className: "rsk-auth__description" }, a("slackConnectContainerDescription")));
};
export {
  o as SlackAuthContainer
};
//# sourceMappingURL=SlackAuthContainer.mjs.map
