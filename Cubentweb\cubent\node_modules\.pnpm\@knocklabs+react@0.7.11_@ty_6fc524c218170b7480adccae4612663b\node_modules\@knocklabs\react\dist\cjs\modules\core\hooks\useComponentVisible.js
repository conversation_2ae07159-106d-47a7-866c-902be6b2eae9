"use strict";const o=require("react");function i(e,t){return e?e===t||e.contains(t):!1}function d(e,t,s){const r=o.useRef(null),c=n=>{n.key==="Escape"&&t(n)},u=n=>{s.closeOnClickOutside&&!i(r.current,n.target)&&t(n)};return o.useEffect(()=>(e&&(document.addEventListener("keydown",c,!0),document.addEventListener("click",u,!0)),()=>{document.removeEventListener("keydown",c,!0),document.removeEventListener("click",u,!0)}),[e]),{ref:r}}module.exports=d;
//# sourceMappingURL=useComponentVisible.js.map
