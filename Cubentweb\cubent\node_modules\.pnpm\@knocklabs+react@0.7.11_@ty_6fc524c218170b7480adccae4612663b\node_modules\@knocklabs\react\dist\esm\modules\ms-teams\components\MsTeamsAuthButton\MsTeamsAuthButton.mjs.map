{"version": 3, "file": "MsTeamsAuthButton.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockMsTeamsClient,\n  useMsTeamsAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthButtonProps {\n  msTeamsBotId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n}\n\nexport const MsTeamsAuthButton: FunctionComponent<MsTeamsAuthButtonProps> = ({\n  msTeamsBotId,\n  redirectUrl,\n  onAuthenticationComplete,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockMsTeamsClient();\n\n  const { buildMsTeamsAuthUrl, disconnectFromMsTeams } = useMsTeamsAuth(\n    msTeamsBotId,\n    redirectUrl,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        onAuthenticationComplete?.(event.data);\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"msTeamsDisconnect\") || null;\n  const reconnectLabel = t(\"msTeamsReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rtk-connect__button rtk-connect__button--loading\">\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"msTeamsConnecting\")\n            : t(\"msTeamsDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rtk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"msTeamsError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--disconnected\"\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"msTeamsConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromMsTeams}\n      className=\"rtk-connect__button rtk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <MsTeamsIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rtk-connect__button__text--connected\">\n        {actionLabel || t(\"msTeamsConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["MsTeamsAuthButton", "msTeamsBotId", "redirectUrl", "onAuthenticationComplete", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockMsTeamsClient", "buildMsTeamsAuthUrl", "disconnectFromMsTeams", "useMsTeamsAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "MsTeamsIcon", "openPopupWindow"], "mappings": ";;;;;;AAoBO,MAAMA,IAA+DA,CAAC;AAAA,EAC3EC,cAAAA;AAAAA,EACAC,aAAAA;AAAAA,EACAC,0BAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC;AAAAA,MAAMC,EAAgB,GACxBC,IAAQC,EAAe,GAEvB;AAAA,IACJC,qBAAAA;AAAAA,IACAC,kBAAAA;AAAAA,IACAC,gBAAAA;AAAAA,IACAC,aAAAA;AAAAA,IACAC,YAAAA;AAAAA,MACEC,EAAsB,GAEpB;AAAA,IAAEC,qBAAAA;AAAAA,IAAqBC,uBAAAA;AAAAA,EAAAA,IAA0BC,EACrDf,GACAC,CACF;AAEAe,EAAAA,EAAU,MAAM;AACRC,UAAAA,IAAiBA,CAACC,MAAwB;AAC1CA,UAAAA,EAAMC,WAAWd,EAAMe;AAIvB,YAAA;AACEF,UAAAA,EAAMG,SAAS,kBACjBd,EAAoB,WAAW,GAG7BW,EAAMG,SAAS,gBACjBd,EAAoB,OAAO,GAG7BL,KAAAA,QAAAA,EAA2BgB,EAAMG;AAAAA,gBAClB;AACfd,UAAAA,EAAoB,OAAO;AAAA,QAAA;AAAA,IAE/B;AAEOe,kBAAAA,iBAAiB,WAAWL,GAAgB,EAAK,GAGjD,MAAM;AACJM,aAAAA,oBAAoB,WAAWN,CAAc;AAAA,IACtD;AAAA,KACC,CAACZ,EAAMe,MAAMlB,GAA0BK,CAAmB,CAAC;AAExDiB,QAAAA,IAAkBrB,EAAE,mBAAmB,KAAK,MAC5CsB,IAAiBtB,EAAE,kBAAkB,KAAK;AAI9CK,SAAAA,MAAqB,gBACrBA,MAAqB,kBAGnBkB,gBAAAA,EAAA,cAAC,SAAI,WAAU,mDAAA,mCACZC,GAAY,EAAA,QAAO,QAAO,OAAM,OAAM,CAAA,GACtCD,gBAAAA,EAAA,cAAA,QAAA,MAEKvB,EADHK,MAAqB,eAChB,sBACA,sBADmB,CAE3B,CACF,IAKAA,MAAqB,0CAEpB,UACC,EAAA,SAAS,MAAMoB,EAAgBf,GAAqB,GACpD,WAAU,kDACV,cAAc,MAAMJ,EAAegB,CAAc,GACjD,cAAc,MAAMhB,EAAe,IAAI,EAAA,mCAEtCkB,GAAY,EAAA,QAAO,QAAO,OAAM,QAAM,GACvCD,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,sCACbhB,KAAeC,KAAcR,EAAE,cAAc,CAChD,CACF,IAKAK,MAAqB,iBAErBkB,gBAAAA,EAAA,cAAC,YACC,SAAS,MAAME,EAAgBf,EAAoB,CAAC,GACpD,WAAU,wDAEV,GAAAa,gBAAAA,EAAA,cAACC,KAAY,QAAO,QAAO,OAAM,OAAM,CAAA,mCACtC,QAAMxB,MAAAA,EAAE,gBAAgB,CAAE,CAC7B,IAMDuB,gBAAAA,EAAA,cAAA,UAAA,EACC,SAASZ,GACT,WAAU,sDACV,cAAc,MAAML,EAAee,CAAe,GAClD,cAAc,MAAMf,EAAe,IAAI,KAEvCiB,gBAAAA,EAAA,cAACC,GAAY,EAAA,QAAO,QAAO,OAAM,OAAM,CAAA,GACvCD,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,uCACbhB,GAAAA,KAAeP,EAAE,kBAAkB,CACtC,CACF;AAEJ;"}