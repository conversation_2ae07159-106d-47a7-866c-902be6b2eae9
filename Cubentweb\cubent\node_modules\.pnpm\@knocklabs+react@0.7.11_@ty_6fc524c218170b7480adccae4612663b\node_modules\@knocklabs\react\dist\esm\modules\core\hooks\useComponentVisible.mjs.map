{"version": 3, "file": "useComponentVisible.mjs", "sources": ["../../../../../src/modules/core/hooks/useComponentVisible.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\n\nfunction contains(parent: HTMLElement | null, child: HTMLElement) {\n  if (!parent) return false;\n  return parent === child || parent.contains(child);\n}\n\ntype Options = {\n  closeOnClickOutside: boolean;\n};\n\nexport default function useComponentVisible(\n  isVisible: boolean,\n  onClose: (event: Event) => void,\n  options: Options,\n) {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const handleKeydown = (event: KeyboardEvent) => {\n    if (event.key === \"Escape\") {\n      onClose(event);\n    }\n  };\n\n  const handleClickOutside = (event: Event) => {\n    if (\n      options.closeOnClickOutside &&\n      !contains(ref.current, event.target as HTMLElement)\n    ) {\n      onClose(event);\n    }\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      document.addEventListener(\"keydown\", handleKeydown, true);\n      document.addEventListener(\"click\", handleClickOutside, true);\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeydown, true);\n      document.removeEventListener(\"click\", handleClickOutside, true);\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isVisible]);\n\n  return { ref };\n}\n"], "names": ["contains", "parent", "child", "useComponentVisible", "isVisible", "onClose", "options", "ref", "useRef", "handleKeydown", "event", "key", "handleClickOutside", "closeOnClickOutside", "current", "target", "useEffect", "addEventListener", "removeEventListener"], "mappings": ";AAEA,SAASA,EAASC,GAA4BC,GAAoB;AAC5D,SAACD,IACEA,MAAWC,KAASD,EAAOD,SAASE,CAAK,IAD5B;AAEtB;AAMwBC,SAAAA,EACtBC,GACAC,GACAC,GACA;AACMC,QAAAA,IAAMC,EAAuB,IAAI,GAEjCC,IAAgBA,CAACC,MAAyB;AAC1CA,IAAAA,EAAMC,QAAQ,YAChBN,EAAQK,CAAK;AAAA,EAEjB,GAEME,IAAqBA,CAACF,MAAiB;AAEzCJ,IAAAA,EAAQO,uBACR,CAACb,EAASO,EAAIO,SAASJ,EAAMK,MAAqB,KAElDV,EAAQK,CAAK;AAAA,EAEjB;AAEAM,SAAAA,EAAU,OACJZ,MACOa,SAAAA,iBAAiB,WAAWR,GAAe,EAAI,GAC/CQ,SAAAA,iBAAiB,SAASL,GAAoB,EAAI,IAGtD,MAAM;AACFM,aAAAA,oBAAoB,WAAWT,GAAe,EAAI,GAClDS,SAAAA,oBAAoB,SAASN,GAAoB,EAAI;AAAA,EAChE,IAGC,CAACR,CAAS,CAAC,GAEP;AAAA,IAAEG,KAAAA;AAAAA,EAAI;AACf;"}