"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const l=require("react"),u=require("@knocklabs/react-core"),n=require("@telegraph/combobox"),g=require("@telegraph/layout"),p=require("../../utils.js"),T=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},o=T(l),y=({team:e,onTeamChange:m,getChannelCount:d,queryOptions:b})=>{const{connectionStatus:a}=u.useKnockMsTeamsClient(),{data:r,isLoading:i}=u.useMsTeamsTeams({queryOptions:b}),c=l.useMemo(()=>p.sortByDisplayName(r),[r]),f=l.useMemo(()=>a==="disconnected"||a==="error",[a]),C=l.useMemo(()=>a==="connecting"||a==="disconnecting"||i,[a,i]);return o.default.createElement(g.Box,{w:"full",minW:"0"},o.default.createElement(n.Combobox.Root,{value:e==null?void 0:e.id,onValueChange:t=>{const s=c.find(x=>x.id===t);s&&m(s)},placeholder:"Select team",disabled:f||C||c.length===0,modal:!1},o.default.createElement(n.Combobox.Trigger,null),o.default.createElement(n.Combobox.Content,null,o.default.createElement(n.Combobox.Search,{className:"rtk-combobox__search"}),o.default.createElement(n.Combobox.Options,{maxHeight:"36"},c.map(t=>{const s=d(t.id);return o.default.createElement(n.Combobox.Option,{key:t.id,value:t.id},s>0?`${t.displayName} (${s})`:t.displayName)})),o.default.createElement(n.Combobox.Empty,null))))};exports.MsTeamsTeamCombobox=y;
//# sourceMappingURL=MsTeamsTeamCombobox.js.map
