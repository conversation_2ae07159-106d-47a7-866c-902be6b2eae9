{"version": 3, "file": "Button.mjs", "sources": ["../../../../../../src/modules/core/components/Button/Button.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { ButtonSpinner } from \"./ButtonSpinner\";\nimport \"./styles.css\";\n\nexport type ButtonProps = {\n  variant: \"primary\" | \"secondary\";\n  loadingText?: string;\n  isLoading?: boolean;\n  isDisabled?: boolean;\n  isFullWidth?: boolean;\n  onClick: (e: React.MouseEvent) => void;\n};\n\nexport const Button: FunctionComponent<PropsWithChildren<ButtonProps>> = ({\n  variant = \"primary\",\n  loadingText,\n  isLoading = false,\n  isDisabled = false,\n  isFullWidth = false,\n  onClick,\n  children,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  const classNames = [\n    \"rnf-button\",\n    `rnf-button--${variant}`,\n    isFullWidth ? \"rnf-button--full-width\" : \"\",\n    isLoading ? \"rnf-button--is-loading\" : \"\",\n    `rnf-button--${colorMode}`,\n  ].join(\" \");\n\n  // In this case when there's no loading text, we still want to display the original\n  // content of the button, but make it hidden. That allows us to keep the button width\n  // consistent and show the spinner in the middle, meaning no layout shift.\n  const textToShowWhileLoading = loadingText || (\n    <span className=\"rnf-button__button-text-hidden\">{children}</span>\n  );\n\n  return (\n    <button\n      onClick={onClick}\n      className={classNames}\n      disabled={isLoading || isDisabled}\n      type=\"button\"\n    >\n      {isLoading && <ButtonSpinner hasLabel={!!loadingText} />}\n      {isLoading ? textToShowWhileLoading : children}\n    </button>\n  );\n};\n"], "names": ["<PERSON><PERSON>", "variant", "loadingText", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "children", "colorMode", "useKnockFeed", "classNames", "join", "textToShowWhileLoading", "React", "<PERSON>ton<PERSON><PERSON>ner"], "mappings": ";;;;AAgBO,MAAMA,IAA4DA,CAAC;AAAA,EACxEC,SAAAA,IAAU;AAAA,EACVC,aAAAA;AAAAA,EACAC,WAAAA,IAAY;AAAA,EACZC,YAAAA,IAAa;AAAA,EACbC,aAAAA,IAAc;AAAA,EACdC,SAAAA;AAAAA,EACAC,UAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC,WAAAA;AAAAA,MAAcC,EAAa,GAE7BC,IAAa,CACjB,cACA,eAAeT,CAAO,IACtBI,IAAc,2BAA2B,IACzCF,IAAY,2BAA2B,IACvC,eAAeK,CAAS,EAAE,EAC1BG,KAAK,GAAG,GAKJC,IAAyBV,KAC7BW,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,oCAAkCN,CAAS;AAI3D,SAAAM,gBAAAA,EAAA,cAAC,YACC,SAAAP,GACA,WAAWI,GACX,UAAUP,KAAaC,GACvB,MAAK,YAEJD,KAAcU,gBAAAA,EAAA,cAAAC,GAAA,EAAc,UAAU,CAAC,CAACZ,GAAe,GACvDC,IAAYS,IAAyBL,CACxC;AAEJ;"}