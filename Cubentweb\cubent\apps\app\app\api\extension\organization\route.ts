import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@repo/auth/server';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return default organization settings for now
    // This can be expanded later when organization features are implemented
    return NextResponse.json({
      organizationId: null,
      organizationName: null,
      settings: {
        allowedModels: ['claude-3-5-sonnet-20241022', 'gpt-4o', 'gpt-4o-mini'],
        maxTokensPerRequest: 8192,
        enabledFeatures: ['chat', 'codeGeneration', 'fileOperations'],
        restrictions: {
          allowFileOperations: true,
          allowTerminalCommands: true,
          allowWebBrowsing: true,
        },
      },
      permissions: {
        canManageSettings: false,
        canViewAnalytics: true,
        canManageUsers: false,
      },
    });

  } catch (error) {
    console.error('Organization settings error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
