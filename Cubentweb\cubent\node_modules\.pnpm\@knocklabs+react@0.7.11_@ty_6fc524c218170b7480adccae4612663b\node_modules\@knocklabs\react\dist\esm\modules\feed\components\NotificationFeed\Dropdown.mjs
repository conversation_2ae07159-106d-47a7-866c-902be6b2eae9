import { useKnockFeed as a } from "@knocklabs/react-core";
import e from "react";
import { ChevronDown as c } from "../../../core/components/Icons/ChevronDown.mjs";
/* empty css            */
const f = ({
  children: o,
  value: r,
  onChange: t
}) => {
  const {
    colorMode: n
  } = a();
  return /* @__PURE__ */ e.createElement("div", { className: `rnf-dropdown rnf-dropdown--${n}` }, /* @__PURE__ */ e.createElement("select", { "aria-label": "Select notification filter", value: r, onChange: t }, o), /* @__PURE__ */ e.createElement(c, { "aria-hidden": !0 }));
};
export {
  f as Dropdown
};
//# sourceMappingURL=Dropdown.mjs.map
