{"version": 3, "file": "NotificationCell.js", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/NotificationCell.tsx"], "sourcesContent": ["import {\n  <PERSON>Button,\n  ButtonSetContentBlock,\n  ContentBlock,\n  FeedItem,\n  MarkdownContentBlock,\n  TextContentBlock,\n} from \"@knocklabs/client\";\nimport {\n  formatTimestamp,\n  renderNodeOrFallback,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport React, { ReactNode, useMemo } from \"react\";\n\nimport { Button, ButtonGroup } from \"../../../core\";\n\nimport { ArchiveButton } from \"./ArchiveButton\";\nimport { Avatar } from \"./Avatar\";\nimport \"./styles.css\";\n\nexport interface NotificationCellProps {\n  item: FeedItem;\n  // Invoked when the outer container is clicked\n  onItemClick?: (item: FeedItem) => void;\n  // Invoked when a button in the notification cell is clicked\n  onButtonClick?: (item: FeedItem, action: ActionButton) => void;\n  avatar?: ReactNode;\n  children?: ReactNode;\n  archiveButton?: ReactNode;\n}\n\ntype BlockByName = {\n  [name: string]: ContentBlock;\n};\n\nfunction maybeNavigateToUrlWithDelay(url?: string) {\n  if (url && url !== \"\") {\n    setTimeout(() => window.location.assign(url), 200);\n  }\n}\n\nexport const NotificationCell = React.forwardRef<\n  HTMLDivElement,\n  NotificationCellProps\n>(\n  (\n    { item, onItemClick, onButtonClick, avatar, children, archiveButton },\n    ref,\n  ) => {\n    const { feedClient, colorMode } = useKnockFeed();\n    const { locale } = useTranslations();\n\n    const blocksByName: BlockByName = useMemo(() => {\n      return item.blocks.reduce((acc, block) => {\n        return { ...acc, [block.name]: block };\n      }, {});\n    }, [item]);\n\n    const actionUrl = (blocksByName.action_url as TextContentBlock)?.rendered;\n    const buttonSet = blocksByName.actions as ButtonSetContentBlock;\n\n    const onContainerClickHandler = React.useCallback(() => {\n      // Mark as interacted + read once we click the item\n      feedClient.markAsInteracted(item, {\n        type: \"cell_click\",\n        action: actionUrl,\n      });\n\n      if (onItemClick) return onItemClick(item);\n\n      return maybeNavigateToUrlWithDelay(actionUrl);\n    }, [item, actionUrl, onItemClick, feedClient]);\n\n    const onButtonClickHandler = React.useCallback(\n      (_e: React.MouseEvent, button: ActionButton) => {\n        // Record the interaction with the metadata for the button that was clicked\n        feedClient.markAsInteracted(item, {\n          type: \"button_click\",\n          name: button.name,\n          label: button.label,\n          action: button.action,\n        });\n\n        if (onButtonClick) return onButtonClick(item, button);\n\n        return maybeNavigateToUrlWithDelay(button.action);\n      },\n      [onButtonClick, feedClient, item],\n    );\n\n    const onKeyDown = React.useCallback(\n      (ev: React.KeyboardEvent<HTMLDivElement>) => {\n        switch (ev.key) {\n          case \"Enter\": {\n            ev.stopPropagation();\n            onContainerClickHandler();\n            break;\n          }\n          default:\n            break;\n        }\n      },\n      [onContainerClickHandler],\n    );\n\n    const actor = item.actors[0];\n\n    return (\n      // eslint-disable-next-line jsx-a11y/no-static-element-interactions\n      <div\n        ref={ref}\n        className={`rnf-notification-cell rnf-notification-cell--${colorMode}`}\n        onClick={onContainerClickHandler}\n        onKeyDown={onKeyDown}\n        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex\n        tabIndex={0}\n      >\n        <div className=\"rnf-notification-cell__inner\">\n          {!item.read_at && (\n            <div className=\"rnf-notification-cell__unread-dot\" />\n          )}\n\n          {renderNodeOrFallback(\n            avatar,\n            actor && \"name\" in actor && actor.name && (\n              <Avatar name={actor.name} src={actor.avatar} />\n            ),\n          )}\n\n          <div className=\"rnf-notification-cell__content-outer\">\n            {blocksByName.body && (\n              <div\n                className=\"rnf-notification-cell__content\"\n                dangerouslySetInnerHTML={{\n                  __html: (blocksByName.body as MarkdownContentBlock).rendered,\n                }}\n              />\n            )}\n\n            {buttonSet && (\n              <div className=\"rnf-notification-cell__button-group\">\n                <ButtonGroup>\n                  {buttonSet.buttons.map((button, i) => (\n                    <Button\n                      variant={i === 0 ? \"primary\" : \"secondary\"}\n                      key={button.name}\n                      onClick={(e) => onButtonClickHandler(e, button)}\n                    >\n                      {button.label}\n                    </Button>\n                  ))}\n                </ButtonGroup>\n              </div>\n            )}\n\n            {children && (\n              <div className=\"rnf-notification-cell__child-content\">\n                {children}\n              </div>\n            )}\n\n            <span className=\"rnf-notification-cell__timestamp\">\n              {formatTimestamp(item.inserted_at, { locale })}\n            </span>\n          </div>\n\n          {renderNodeOrFallback(archiveButton, <ArchiveButton item={item} />)}\n        </div>\n      </div>\n    );\n  },\n);\n"], "names": ["maybeNavigateToUrlWithDelay", "url", "setTimeout", "window", "location", "assign", "NotificationCell", "React", "forwardRef", "item", "onItemClick", "onButtonClick", "avatar", "children", "archiveButton", "ref", "feedClient", "colorMode", "useKnockFeed", "locale", "useTranslations", "blocksByName", "useMemo", "blocks", "reduce", "acc", "block", "name", "actionUrl", "action_url", "rendered", "buttonSet", "actions", "onContainerClickHandler", "useCallback", "markAsInteracted", "type", "action", "onButtonClickHandler", "_e", "button", "label", "onKeyDown", "ev", "key", "stopPropagation", "actor", "actors", "read_at", "renderNodeOrFallback", "Avatar", "body", "__html", "ButtonGroup", "buttons", "map", "i", "<PERSON><PERSON>", "e", "formatTimestamp", "inserted_at", "ArchiveButton"], "mappings": "ybAqCA,SAASA,EAA4BC,EAAc,CAC7CA,GAAOA,IAAQ,IACjBC,WAAW,IAAMC,OAAOC,SAASC,OAAOJ,CAAG,EAAG,GAAG,CAErD,CAEaK,MAAAA,EAAmBC,EAAAA,QAAMC,WAIpC,CACE,CAAEC,KAAAA,EAAMC,YAAAA,EAAaC,cAAAA,EAAeC,OAAAA,EAAQC,SAAAA,EAAUC,cAAAA,CAAc,EACpEC,IACG,OACG,KAAA,CAAEC,WAAAA,EAAYC,UAAAA,GAAcC,eAAa,EACzC,CAAEC,OAAAA,GAAWC,kBAAgB,EAE7BC,EAA4BC,EAAAA,QAAQ,IACjCb,EAAKc,OAAOC,OAAO,CAACC,EAAKC,KACvB,CAAE,GAAGD,EAAK,CAACC,EAAMC,IAAI,EAAGD,CAAM,GACpC,EAAE,EACJ,CAACjB,CAAI,CAAC,EAEHmB,GAAaP,EAAAA,EAAaQ,aAAbR,YAAAA,EAA8CS,SAC3DC,EAAYV,EAAaW,QAEzBC,EAA0B1B,UAAM2B,YAAY,KAEhDlB,EAAWmB,iBAAiB1B,EAAM,CAChC2B,KAAM,aACNC,OAAQT,CAAAA,CACT,EAEGlB,EAAoBA,EAAYD,CAAI,EAEjCT,EAA4B4B,CAAS,GAC3C,CAACnB,EAAMmB,EAAWlB,EAAaM,CAAU,CAAC,EAEvCsB,EAAuB/B,EAAAA,QAAM2B,YACjC,CAACK,EAAsBC,KAErBxB,EAAWmB,iBAAiB1B,EAAM,CAChC2B,KAAM,eACNT,KAAMa,EAAOb,KACbc,MAAOD,EAAOC,MACdJ,OAAQG,EAAOH,MAAAA,CAChB,EAEG1B,EAAsBA,EAAcF,EAAM+B,CAAM,EAE7CxC,EAA4BwC,EAAOH,MAAM,GAElD,CAAC1B,EAAeK,EAAYP,CAAI,CAClC,EAEMiC,EAAYnC,EAAAA,QAAM2B,YACrBS,GAA4C,CAC3C,OAAQA,EAAGC,IAAG,CACZ,IAAK,QAAS,CACZD,EAAGE,gBAAgB,EACKZ,EAAA,EACxB,KAAA,CAGA,CACJ,EAEF,CAACA,CAAuB,CAC1B,EAEMa,EAAQrC,EAAKsC,OAAO,CAAC,EAE3B,OAEExC,EAAAA,QAAA,cAAC,MAAA,CACC,IAAAQ,EACA,UAAW,gDAAgDE,CAAS,GACpE,QAASgB,EACT,UAAAS,EAEA,SAAU,CAAA,0BAET,MAAI,CAAA,UAAU,gCACZ,CAACjC,EAAKuC,SACLzC,EAAA,QAAA,cAAC,MAAI,CAAA,UAAU,oCAChB,EAEA0C,EAAAA,qBACCrC,EACAkC,GAAS,SAAUA,GAASA,EAAMnB,MAC/BpB,EAAAA,QAAA,cAAA2C,EAAA,OAAA,CAAO,KAAMJ,EAAMnB,KAAM,IAAKmB,EAAMlC,QAEzC,EAEAL,EAAA,QAAA,cAAC,MAAI,CAAA,UAAU,wCACZc,EAAa8B,8BACX,MACC,CAAA,UAAU,iCACV,wBAAyB,CACvBC,OAAS/B,EAAa8B,KAA8BrB,QACtD,CAAA,CAEH,EAEAC,GACCxB,UAAA,cAAC,OAAI,UAAU,qCAAA,EACZA,EAAA,QAAA,cAAA8C,EAAA,YAAA,KACEtB,EAAUuB,QAAQC,IAAI,CAACf,EAAQgB,IAC9BjD,EAAA,QAAA,cAACkD,EACC,OAAA,CAAA,QAASD,IAAM,EAAI,UAAY,YAC/B,IAAKhB,EAAOb,KACZ,QAAgBW,GAAAA,EAAqBoB,EAAGlB,CAAM,CAAA,EAE7CA,EAAOC,KACV,CACD,CACH,CACF,EAGD5B,2BACE,MAAI,CAAA,UAAU,sCACZA,EAAAA,CACH,EAGFN,EAAA,QAAA,cAAC,QAAK,UAAU,kCAAA,EACboD,EAAAA,gBAAgBlD,EAAKmD,YAAa,CAAEzC,OAAAA,CAAAA,CAAQ,CAC/C,CACF,EAEC8B,EAAAA,qBAAqBnC,EAAgBP,EAAAA,QAAA,cAAAsD,EAAA,cAAA,CAAc,KAAApD,GAAc,CACpE,CAAA,CAGN,CACF"}