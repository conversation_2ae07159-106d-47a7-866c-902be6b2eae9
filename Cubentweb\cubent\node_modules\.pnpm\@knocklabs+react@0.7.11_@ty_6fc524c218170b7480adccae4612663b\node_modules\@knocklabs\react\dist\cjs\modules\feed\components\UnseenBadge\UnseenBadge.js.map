{"version": 3, "file": "UnseenBadge.js", "sources": ["../../../../../../src/modules/feed/components/UnseenBadge/UnseenBadge.tsx"], "sourcesContent": ["import { FeedMetadata } from \"@knocklabs/client\";\nimport { formatBadgeCount, useKnockFeed } from \"@knocklabs/react-core\";\nimport React from \"react\";\n\nimport \"./styles.css\";\n\nexport type BadgeCountType = \"unseen\" | \"unread\" | \"all\";\n\nexport type UnseenBadgeProps = {\n  badgeCountType?: BadgeCountType;\n};\n\nfunction selectBadgeCount(\n  badgeCountType: BadgeCountType,\n  metadata: FeedMetadata,\n) {\n  switch (badgeCountType) {\n    case \"all\":\n      return metadata.total_count;\n    case \"unread\":\n      return metadata.unread_count;\n    case \"unseen\":\n      return metadata.unseen_count;\n  }\n}\n\nexport const UnseenBadge: React.FC<UnseenBadgeProps> = ({\n  badgeCountType = \"unseen\",\n}) => {\n  const { useFeedStore } = useKnockFeed();\n  const badgeCountValue = useFeedStore((state) =>\n    selectBadgeCount(badgeCountType, state.metadata),\n  );\n\n  return badgeCountValue !== 0 ? (\n    <div className=\"rnf-unseen-badge\">\n      <span className=\"rnf-unseen-badge__count\">\n        {formatBadgeCount(badgeCountValue)}\n      </span>\n    </div>\n  ) : null;\n};\n"], "names": ["selectBadgeCount", "badgeCountType", "metadata", "total_count", "unread_count", "unseen_count", "UnseenBadge", "useFeedStore", "useKnockFeed", "badgeCountValue", "state", "React", "formatBadgeCount"], "mappings": "4OAYA,SAASA,EACPC,EACAC,EACA,CACA,OAAQD,EAAc,CACpB,IAAK,MACH,OAAOC,EAASC,YAClB,IAAK,SACH,OAAOD,EAASE,aAClB,IAAK,SACH,OAAOF,EAASG,YAAAA,CAEtB,CAEO,MAAMC,EAA0CA,CAAC,CACtDL,eAAAA,EAAiB,QACnB,IAAM,CACE,KAAA,CAAEM,aAAAA,GAAiBC,eAAa,EAChCC,EAAkBF,EAAcG,GACpCV,EAAiBC,EAAgBS,EAAMR,QAAQ,CACjD,EAEA,OAAOO,IAAoB,EACxBE,UAAA,cAAA,MAAA,CAAI,UAAU,oBACbA,EAAAA,QAAA,cAAC,OAAK,CAAA,UAAU,yBACbC,EAAAA,EAAAA,iBAAiBH,CAAe,CACnC,CACF,EACE,IACN"}