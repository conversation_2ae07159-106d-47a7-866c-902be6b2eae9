{"version": 3, "file": "NotificationIconButton.js", "sources": ["../../../../../../src/modules/feed/components/NotificationIconButton/NotificationIconButton.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { SyntheticEvent } from \"react\";\n\nimport { BellIcon } from \"../../../core/components/Icons\";\nimport { BadgeCountType, UnseenBadge } from \"../UnseenBadge\";\n\nimport \"./styles.css\";\n\nexport interface NotificationIconButtonProps {\n  // What value should we use to drive the badge count?\n  badgeCountType?: BadgeCountType;\n  onClick: (e: SyntheticEvent) => void;\n}\n\nexport const NotificationIconButton = React.forwardRef<\n  HTMLButtonElement,\n  NotificationIconButtonProps\n>(({ onClick, badgeCountType }, ref) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <button\n      className={`rnf-notification-icon-button rnf-notification-icon-button--${colorMode}`}\n      aria-label=\"Open notification feed\"\n      ref={ref}\n      onClick={onClick}\n    >\n      <BellIcon aria-hidden />\n      <UnseenBadge badgeCountType={badgeCountType} />\n    </button>\n  );\n});\n"], "names": ["NotificationIconButton", "React", "forwardRef", "onClick", "badgeCountType", "ref", "colorMode", "useKnockFeed", "BellIcon", "UnseenBadge"], "mappings": "2UAcaA,EAAyBC,EAAAA,QAAMC,WAG1C,CAAC,CAAEC,QAAAA,EAASC,eAAAA,CAAe,EAAGC,IAAQ,CAChC,KAAA,CAAEC,UAAAA,GAAcC,eAAa,EAEnC,+BACG,SACC,CAAA,UAAW,8DAA8DD,CAAS,GAClF,aAAW,yBACX,IAAAD,EACA,QAAAF,GAEAF,EAAAA,QAAA,cAACO,YAAS,cAAW,EAAA,CAAA,EACpBP,EAAAA,QAAA,cAAAQ,EAAAA,YAAA,CAAY,eAAAL,CAA+B,CAAA,CAC9C,CAEJ,CAAC"}