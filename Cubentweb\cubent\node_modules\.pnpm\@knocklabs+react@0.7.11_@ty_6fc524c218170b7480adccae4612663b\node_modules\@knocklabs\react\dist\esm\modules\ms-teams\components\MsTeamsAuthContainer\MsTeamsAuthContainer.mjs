import e from "react";
import { useTranslations as r } from "@knocklabs/react-core";
/* empty css               */
import { MsTeamsIcon as m } from "../MsTeamsIcon/MsTeamsIcon.mjs";
/* empty css            */
const l = ({
  actionButton: t
}) => {
  const {
    t: a
  } = r();
  return /* @__PURE__ */ e.createElement("div", { className: "rtk-auth" }, /* @__PURE__ */ e.createElement("div", { className: "rtk-auth__header" }, /* @__PURE__ */ e.createElement(m, { height: "32px", width: "32px" }), /* @__PURE__ */ e.createElement("div", null, t)), /* @__PURE__ */ e.createElement("div", { className: "rtk-auth__title" }, "Microsoft Teams"), /* @__PURE__ */ e.createElement("div", { className: "rtk-auth__description" }, a("msTeamsConnectContainerDescription")));
};
export {
  l as MsTeamsAuthContainer
};
//# sourceMappingURL=MsTeamsAuthContainer.mjs.map
