import { ColorMode } from '@knocklabs/react-core';
import { default as React } from 'react';
import { ButtonContent, ImageContent, TargetButton, TargetButtonWithGuide, TargetImage, TargetImageWithGuide } from '../types';
import * as Dialog from "@radix-ui/react-dialog";
type RootProps = Omit<React.ComponentPropsWithoutRef<typeof Dialog.Root>, "modal"> & React.PropsWithChildren<React.ComponentPropsWithRef<"div">>;
declare const Root: {
    ({ children, onOpenChange, ...props }: RootProps): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
type OverlayProps = React.ComponentPropsWithoutRef<typeof Dialog.Overlay> & React.ComponentPropsWithRef<"div">;
declare const Overlay: React.ForwardRefExoticComponent<Omit<OverlayProps, "ref"> & React.RefAttributes<HTMLDivElement>>;
type ContentProps = React.ComponentPropsWithoutRef<typeof Dialog.Content> & React.ComponentPropsWithRef<"div">;
declare const Content: React.ForwardRefExoticComponent<Omit<ContentProps, "ref"> & React.RefAttributes<HTMLDivElement>>;
type TitleProps = React.ComponentPropsWithoutRef<typeof Dialog.Title> & React.ComponentPropsWithRef<"div"> & {
    title: string;
};
declare const Title: {
    ({ title, className, ...props }: TitleProps): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
declare const Body: React.FC<{
    body: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Img: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"img">>>;
declare const Actions: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const PrimaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
declare const SecondaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
type CloseProps = React.ComponentPropsWithoutRef<typeof Dialog.Close> & React.ComponentPropsWithRef<"button">;
declare const Close: {
    ({ className, ...props }: CloseProps): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
type ModalContent = {
    title: string;
    body: string;
    image?: ImageContent;
    primary_button?: ButtonContent;
    secondary_button?: ButtonContent;
    dismissible?: boolean;
};
declare const DefaultView: React.FC<{
    content: ModalContent;
    colorMode?: ColorMode;
    onOpenChange?: (open: boolean) => void;
    onDismiss?: () => void;
    onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;
    onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;
}>;
type ModalProps = {
    guideKey?: string;
    onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;
    onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;
};
export declare const Modal: React.FC<ModalProps>;
export declare const ModalView: {
    Default: typeof DefaultView;
    Root: typeof Root;
    Overlay: typeof Overlay;
    Content: typeof Content;
    Title: typeof Title;
    Body: typeof Body;
    Img: typeof Img;
    Actions: typeof Actions;
    PrimaryButton: typeof PrimaryButton;
    SecondaryButton: typeof SecondaryButton;
    Close: typeof Close;
};
export {};
//# sourceMappingURL=Modal.d.ts.map