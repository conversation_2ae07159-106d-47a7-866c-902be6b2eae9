{"version": 3, "file": "SlackErrorMessage.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackChannelCombobox/SlackErrorMessage.tsx"], "sourcesContent": ["import { Icon, Lucide } from \"@telegraph/icon\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent } from \"react\";\n\ninterface Props {\n  message?: string;\n}\n\nconst SlackErrorMessage: FunctionComponent<Props> = ({ message }) => {\n  return (\n    <div className=\"rsk-combobox__error\">\n      <span>\n        <Icon icon={Lucide.Info} color=\"black\" size=\"1\" aria-hidden />\n      </span>\n      <Text as=\"div\" color=\"black\" size=\"1\">\n        {message}\n      </Text>\n    </div>\n  );\n};\n\nexport default SlackErrorMessage;\n"], "names": ["SlackErrorMessage", "message", "React", "Icon", "Lucide", "Info", "Text"], "mappings": ";;;AAQA,MAAMA,IAA8CA,CAAC;AAAA,EAAEC,SAAAA;AAAQ,MAE1DC,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,sBAAA,GACZA,gBAAAA,EAAA,cAAA,QAAA,MACEA,gBAAAA,EAAA,cAAAC,GAAA,EAAK,MAAMC,EAAOC,MAAM,OAAM,SAAQ,MAAK,KAAI,eAAW,GAAA,CAAA,CAC7D,GACAH,gBAAAA,EAAA,cAACI,GAAK,EAAA,IAAG,OAAM,OAAM,SAAQ,MAAK,IAC/BL,GAAAA,CACH,CACF;"}