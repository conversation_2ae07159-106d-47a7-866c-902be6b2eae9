{"version": 3, "file": "EmptyFeed.mjs", "sources": ["../../../../../../src/modules/feed/components/EmptyFeed/EmptyFeed.tsx"], "sourcesContent": ["import { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"./styles.css\";\n\nexport const EmptyFeed: FunctionComponent = () => {\n  const { colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  return (\n    <div className={`rnf-empty-feed rnf-empty-feed--${colorMode}`}>\n      <div className=\"rnf-empty-feed__inner\">\n        <h2 className=\"rnf-empty-feed__header\">{t(\"emptyFeedTitle\")}</h2>\n        <p className=\"rnf-empty-feed__body\">{t(\"emptyFeedBody\")}</p>\n      </div>\n    </div>\n  );\n};\n"], "names": ["EmptyFeed", "colorMode", "useKnockFeed", "t", "useTranslations", "React"], "mappings": ";;;AAKO,MAAMA,IAA+BA,MAAM;AAC1C,QAAA;AAAA,IAAEC,WAAAA;AAAAA,MAAcC,EAAa,GAC7B;AAAA,IAAEC;AAAAA,MAAMC,EAAgB;AAG5B,SAAAC,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAW,kCAAkCJ,CAAS,GACzD,GAAAI,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,wBACb,GAAAA,gBAAAA,EAAA,cAAC,MAAG,EAAA,WAAU,4BAA0BF,EAAE,gBAAgB,CAAE,GAC3DE,gBAAAA,EAAA,cAAA,KAAA,EAAE,WAAU,uBAAA,GAAwBF,EAAE,eAAe,CAAE,CAC1D,CACF;AAEJ;"}