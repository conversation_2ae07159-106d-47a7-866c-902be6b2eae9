import { useKnockFeed as i } from "@knocklabs/react-core";
import t from "react";
import { BellIcon as a } from "../../../core/components/Icons/Bell.mjs";
import { UnseenBadge as c } from "../UnseenBadge/UnseenBadge.mjs";
/* empty css            */
const p = t.forwardRef(({
  onClick: o,
  badgeCountType: e
}, n) => {
  const {
    colorMode: r
  } = i();
  return /* @__PURE__ */ t.createElement("button", { className: `rnf-notification-icon-button rnf-notification-icon-button--${r}`, "aria-label": "Open notification feed", ref: n, onClick: o }, /* @__PURE__ */ t.createElement(a, { "aria-hidden": !0 }), /* @__PURE__ */ t.createElement(c, { badgeCountType: e }));
});
export {
  p as NotificationIconButton
};
//# sourceMappingURL=NotificationIconButton.mjs.map
