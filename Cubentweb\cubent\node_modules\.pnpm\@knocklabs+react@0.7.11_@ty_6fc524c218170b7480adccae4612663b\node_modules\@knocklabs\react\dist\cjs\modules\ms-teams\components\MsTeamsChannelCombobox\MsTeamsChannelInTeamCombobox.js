"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const l=require("react"),i=require("@knocklabs/react-core"),o=require("@telegraph/combobox"),E=require("@telegraph/layout"),g=require("../../utils.js"),M=require("./MsTeamsErrorMessage.js"),y=a=>a&&typeof a=="object"&&"default"in a?a:{default:a},t=y(l),S=({teamId:a,msTeamsChannelsRecipientObject:b,queryOptions:h})=>{const{connectionStatus:s}=i.useKnockMsTeamsClient(),{data:m=[]}=i.useMsTeamsChannels({teamId:a,queryOptions:h}),C=l.useMemo(()=>g.sortByDisplayName(m),[m]),{data:n,updateConnectedChannels:_,error:c}=i.useConnectedMsTeamsChannels({msTeamsChannelsRecipientObject:b}),f=l.useMemo(()=>s==="disconnected"||s==="error"||!!c,[s,c]),p=l.useMemo(()=>s==="connecting"||s==="disconnecting",[s]),d=l.useCallback(e=>m.some(u=>u.id===e),[m]),T=l.useMemo(()=>n==null?void 0:n.filter(e=>e.ms_teams_channel_id&&d(e.ms_teams_channel_id)).map(e=>e.ms_teams_channel_id),[n,d]);return t.default.createElement(t.default.Fragment,null,t.default.createElement(E.Box,{w:"full",minW:"0"},t.default.createElement(o.Combobox.Root,{value:T,onValueChange:e=>{const u=e.map(r=>({ms_teams_team_id:a,ms_teams_channel_id:r})),x=[...(n==null?void 0:n.filter(r=>!r.ms_teams_channel_id||!d(r.ms_teams_channel_id)))??[],...u];_(x).catch(console.error)},placeholder:"Select channels",disabled:a===void 0||f||p||m.length===0,closeOnSelect:!1,layout:"wrap",modal:!1},t.default.createElement(o.Combobox.Trigger,null),t.default.createElement(o.Combobox.Content,null,t.default.createElement(o.Combobox.Search,{className:"rtk-combobox__search"}),t.default.createElement(o.Combobox.Options,{maxHeight:"36"},C.map(e=>t.default.createElement(o.Combobox.Option,{key:e.id,value:e.id},e.displayName))),t.default.createElement(o.Combobox.Empty,null)))),!!c&&t.default.createElement(M,{message:c}))};exports.MsTeamsChannelInTeamCombobox=S;
//# sourceMappingURL=MsTeamsChannelInTeamCombobox.js.map
