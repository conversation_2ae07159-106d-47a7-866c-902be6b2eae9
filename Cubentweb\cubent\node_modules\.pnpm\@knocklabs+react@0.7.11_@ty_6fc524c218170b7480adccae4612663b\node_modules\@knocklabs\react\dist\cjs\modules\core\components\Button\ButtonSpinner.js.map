{"version": 3, "file": "ButtonSpinner.js", "sources": ["../../../../../../src/modules/core/components/Button/ButtonSpinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../Spinner\";\n\nimport \"./styles.css\";\n\ntype ButtonSpinnerProps = {\n  hasLabel: boolean;\n};\n\nexport const ButtonSpinner: FunctionComponent<ButtonSpinnerProps> = ({\n  hasLabel,\n}) => (\n  <div\n    className={`rnf-button-spinner rnf-button-spinner--${\n      hasLabel ? \"with-label\" : \"without-label\"\n    }`}\n  >\n    <Spinner />\n  </div>\n);\n"], "names": ["<PERSON>ton<PERSON><PERSON>ner", "<PERSON><PERSON><PERSON><PERSON>", "React", "Spinner"], "mappings": "4OAUaA,EAAuDA,CAAC,CACnEC,SAAAA,CACF,IACEC,EAAAA,QAAA,cAAC,MACC,CAAA,UAAW,0CACTD,EAAW,aAAe,eAAe,EAAA,EAG1CC,EAAA,QAAA,cAAAC,EAAA,QAAA,IAAO,CACV"}