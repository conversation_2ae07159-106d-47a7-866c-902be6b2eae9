{"version": 3, "file": "NotificationFeedContainer.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const NotificationFeedContainer: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-feed-provider\">{children}</div>;\n"], "names": ["NotificationFeedContainer", "children", "React"], "mappings": ";;AAIO,MAAMA,IAERA,CAAC;AAAA,EAAEC,UAAAA;AAAS,MAAOC,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,oBAAA,GAAqBD,CAAS;"}