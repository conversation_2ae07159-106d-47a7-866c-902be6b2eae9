import { ColorMode } from '@knocklabs/react-core';
import { default as React } from 'react';
import { Button<PERSON>ontent, ImageContent, TargetButton, TargetButtonWithGuide, TargetImage, TargetImageWithGuide } from '../types';
declare const Root: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const Content: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const Headline: React.FC<{
    headline: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Title: React.FC<{
    title: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Body: React.FC<{
    body: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Img: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"img">>>;
declare const Actions: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const PrimaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
declare const SecondaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
declare const DismissButton: React.FC<React.ComponentPropsWithRef<"button">>;
type CardContent = {
    headline: string;
    title: string;
    body: string;
    image?: ImageContent;
    primary_button?: ButtonContent;
    secondary_button?: ButtonContent;
    dismissible?: boolean;
};
declare const DefaultView: React.FC<{
    content: CardContent;
    colorMode?: ColorMode;
    onDismiss?: () => void;
    onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;
    onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;
}>;
type CardProps = {
    guideKey?: string;
    onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;
    onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;
};
export declare const Card: React.FC<CardProps>;
export declare const CardView: {
    Default: typeof DefaultView;
    Root: typeof Root;
    Content: typeof Content;
    Headline: typeof Headline;
    Title: typeof Title;
    Body: typeof Body;
    Img: typeof Img;
    Actions: typeof Actions;
    PrimaryButton: typeof PrimaryButton;
    SecondaryButton: typeof SecondaryButton;
    DismissButton: typeof DismissButton;
};
export {};
//# sourceMappingURL=Card.d.ts.map