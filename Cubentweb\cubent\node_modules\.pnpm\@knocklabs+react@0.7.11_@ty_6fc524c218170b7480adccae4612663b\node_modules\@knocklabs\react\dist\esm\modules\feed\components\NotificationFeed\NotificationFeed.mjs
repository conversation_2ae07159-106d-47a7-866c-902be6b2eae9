import { isRequestInFlight as C, NetworkStatus as f } from "@knocklabs/client";
import { FilterStatus as I, useKnockFeed as R, useFeedSettings as B, useTranslations as q } from "@knocklabs/react-core";
import e, { useState as x, useRef as K, useEffect as p, use<PERSON><PERSON>back as H } from "react";
import { Spinner as M } from "../../../core/components/Spinner/Spinner.mjs";
import P from "../../../core/hooks/useOnBottomScroll.mjs";
import { EmptyFeed as z } from "../EmptyFeed/EmptyFeed.mjs";
import { NotificationCell as A } from "../NotificationCell/NotificationCell.mjs";
/* empty css                              */
import { NotificationFeedHeader as L } from "./NotificationFeedHeader.mjs";
/* empty css            */
const O = (t) => /* @__PURE__ */ e.createElement(A, { key: t.item.id, ...t }), T = (t) => /* @__PURE__ */ e.createElement(L, { ...t }), k = ({
  colorMode: t
}) => /* @__PURE__ */ e.createElement("div", { className: "rnf-notification-feed__spinner-container" }, /* @__PURE__ */ e.createElement(M, { thickness: 3, size: "16px", color: t === "dark" ? "rgba(255, 255, 255, 0.65)" : void 0 })), U = "https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed", Z = ({
  EmptyComponent: t = /* @__PURE__ */ e.createElement(z, null),
  renderItem: _ = O,
  onNotificationClick: g,
  onNotificationButtonClick: E,
  onMarkAllAsReadClick: b,
  initialFilterStatus: r = I.All,
  header: N,
  renderHeader: S = T
}) => {
  const [a, m] = x(r), {
    feedClient: n,
    useFeedStore: h,
    colorMode: c
  } = R(), {
    settings: i
  } = B(n), {
    t: F
  } = q(), {
    pageInfo: l,
    items: d,
    networkStatus: o
  } = h(), u = K(null);
  p(() => {
    m(r);
  }, [r]), p(() => {
    n.fetch({
      status: a
    });
  }, [n, a]);
  const v = d.length === 0, s = C(o), w = H(() => {
    !s && l.after && n.fetchNextPage();
  }, [s, l, n]);
  return P({
    ref: u,
    callback: w,
    offset: 70
  }), /* @__PURE__ */ e.createElement("div", { className: `rnf-notification-feed rnf-notification-feed--${c}` }, N || S({
    setFilterStatus: m,
    filterStatus: a,
    onMarkAllAsReadClick: b
  }), /* @__PURE__ */ e.createElement("div", { className: "rnf-notification-feed__container", ref: u }, o === f.loading && /* @__PURE__ */ e.createElement(k, { colorMode: c }), /* @__PURE__ */ e.createElement("div", { className: "rnf-notification-feed__feed-items-container" }, o !== f.loading && d.map((y) => _({
    item: y,
    onItemClick: g,
    onButtonClick: E
  }))), o === f.fetchMore && /* @__PURE__ */ e.createElement(k, { colorMode: c }), !s && v && t), (i == null ? void 0 : i.features.branding_required) && /* @__PURE__ */ e.createElement("div", { className: "rnf-notification-feed__knock-branding" }, /* @__PURE__ */ e.createElement("a", { href: U, target: "_blank" }, F("poweredBy") || "Powered by Knock")));
};
export {
  Z as NotificationFeed
};
//# sourceMappingURL=NotificationFeed.mjs.map
