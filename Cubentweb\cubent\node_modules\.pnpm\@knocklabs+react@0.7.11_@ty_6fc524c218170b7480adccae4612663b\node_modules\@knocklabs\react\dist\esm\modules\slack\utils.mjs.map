{"version": 3, "file": "utils.mjs", "sources": ["../../../../src/modules/slack/utils.ts"], "sourcesContent": ["import { SlackChannel } from \"@knocklabs/client\";\n\nexport const sortSlackChannelsAlphabetically = (\n  channels: readonly SlackChannel[],\n) =>\n  [...channels].sort((channel1, channel2) =>\n    channel1.name.toLowerCase().localeCompare(channel2.name.toLowerCase()),\n  );\n"], "names": ["sortSlackChannelsAlphabetically", "channels", "sort", "channel1", "channel2", "name", "toLowerCase", "localeCompare"], "mappings": "AAEaA,MAAAA,IAAkCA,CAC7CC,MAEA,CAAC,GAAGA,CAAQ,EAAEC,KAAK,CAACC,GAAUC,MAC5BD,EAASE,KAAKC,cAAcC,cAAcH,EAASC,KAAKC,aAAa,CACvE;"}