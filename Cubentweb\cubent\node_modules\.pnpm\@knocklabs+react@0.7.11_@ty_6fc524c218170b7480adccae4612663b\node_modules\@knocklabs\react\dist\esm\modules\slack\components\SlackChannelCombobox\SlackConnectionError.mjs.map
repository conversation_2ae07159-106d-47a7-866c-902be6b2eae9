{"version": 3, "file": "SlackConnectionError.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackChannelCombobox/SlackConnectionError.tsx"], "sourcesContent": ["import { useKnockSlackClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport SlackErrorMessage from \"./SlackErrorMessage\";\n\nconst SlackConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockSlackClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <SlackErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"slackConnectionErrorOccurred\")\n            : t(\"slackConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default SlackConnectionError;\n"], "names": ["SlackConnectionError", "t", "useTranslations", "connectionStatus", "useKnockSlackClient", "React", "SlackErrorMessage"], "mappings": ";;;AAKA,MAAMA,IAA0CA,MAAM;AAC9C,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GACxB;AAAA,IAAEC,kBAAAA;AAAAA,MAAqBC,EAAoB;AAE7CD,SAAAA,MAAqB,kBAAkBA,MAAqB,UAE5DE,gBAAAA,EAAA,cAACC,GACC,EAAA,SAEML,EADJE,MAAqB,iBACf,iCACA,4BAD8B,EAGtC,CAAA,IAIC;AACT;"}