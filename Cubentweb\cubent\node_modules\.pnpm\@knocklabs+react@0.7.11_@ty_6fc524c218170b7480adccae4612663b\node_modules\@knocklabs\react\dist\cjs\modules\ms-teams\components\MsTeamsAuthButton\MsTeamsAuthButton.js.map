{"version": 3, "file": "MsTeamsAuthButton.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsAuthButton/MsTeamsAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockMsTeamsClient,\n  useMsTeamsAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthButtonProps {\n  msTeamsBotId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n}\n\nexport const MsTeamsAuthButton: FunctionComponent<MsTeamsAuthButtonProps> = ({\n  msTeamsBotId,\n  redirectUrl,\n  onAuthenticationComplete,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockMsTeamsClient();\n\n  const { buildMsTeamsAuthUrl, disconnectFromMsTeams } = useMsTeamsAuth(\n    msTeamsBotId,\n    redirectUrl,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        onAuthenticationComplete?.(event.data);\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"msTeamsDisconnect\") || null;\n  const reconnectLabel = t(\"msTeamsReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rtk-connect__button rtk-connect__button--loading\">\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"msTeamsConnecting\")\n            : t(\"msTeamsDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rtk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"msTeamsError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildMsTeamsAuthUrl())}\n        className=\"rtk-connect__button rtk-connect__button--disconnected\"\n      >\n        <MsTeamsIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"msTeamsConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromMsTeams}\n      className=\"rtk-connect__button rtk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <MsTeamsIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rtk-connect__button__text--connected\">\n        {actionLabel || t(\"msTeamsConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["MsTeamsAuthButton", "msTeamsBotId", "redirectUrl", "onAuthenticationComplete", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockMsTeamsClient", "buildMsTeamsAuthUrl", "disconnectFromMsTeams", "useMsTeamsAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "MsTeamsIcon", "openPopupWindow"], "mappings": "+VAoBaA,EAA+DA,CAAC,CAC3EC,aAAAA,EACAC,YAAAA,EACAC,yBAAAA,CACF,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxBC,EAAQC,EAAAA,eAAe,EAEvB,CACJC,oBAAAA,EACAC,iBAAAA,EACAC,eAAAA,EACAC,YAAAA,EACAC,WAAAA,GACEC,wBAAsB,EAEpB,CAAEC,oBAAAA,EAAqBC,sBAAAA,CAAAA,EAA0BC,EACrDf,eAAAA,EACAC,CACF,EAEAe,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAkBC,GAAwB,CAC1CA,GAAAA,EAAMC,SAAWd,EAAMe,KAIvB,GAAA,CACEF,EAAMG,OAAS,gBACjBd,EAAoB,WAAW,EAG7BW,EAAMG,OAAS,cACjBd,EAAoB,OAAO,EAG7BL,GAAAA,MAAAA,EAA2BgB,EAAMG,WAClB,CACfd,EAAoB,OAAO,CAAA,CAE/B,EAEOe,cAAAA,iBAAiB,UAAWL,EAAgB,EAAK,EAGjD,IAAM,CACJM,OAAAA,oBAAoB,UAAWN,CAAc,CACtD,GACC,CAACZ,EAAMe,KAAMlB,EAA0BK,CAAmB,CAAC,EAExDiB,MAAAA,EAAkBrB,EAAE,mBAAmB,GAAK,KAC5CsB,EAAiBtB,EAAE,kBAAkB,GAAK,KAI9CK,OAAAA,IAAqB,cACrBA,IAAqB,gBAGnBkB,EAAA,QAAA,cAAC,OAAI,UAAU,kDAAA,0BACZC,EAAY,YAAA,CAAA,OAAO,OAAO,MAAM,MAAM,CAAA,EACtCD,EAAAA,QAAA,cAAA,OAAA,KAEKvB,EADHK,IAAqB,aAChB,oBACA,sBADmB,CAE3B,CACF,EAKAA,IAAqB,gCAEpB,SACC,CAAA,QAAS,IAAMoB,EAAAA,gBAAgBf,GAAqB,EACpD,UAAU,iDACV,aAAc,IAAMJ,EAAegB,CAAc,EACjD,aAAc,IAAMhB,EAAe,IAAI,CAAA,0BAEtCkB,EAAAA,YAAY,CAAA,OAAO,OAAO,MAAM,OAAM,EACvCD,UAAA,cAAC,OAAK,CAAA,UAAU,oCACbhB,GAAeC,GAAcR,EAAE,cAAc,CAChD,CACF,EAKAK,IAAqB,eAErBkB,EAAA,QAAA,cAAC,UACC,QAAS,IAAME,EAAAA,gBAAgBf,EAAoB,CAAC,EACpD,UAAU,uDAEV,EAAAa,EAAA,QAAA,cAACC,eAAY,OAAO,OAAO,MAAM,MAAM,CAAA,0BACtC,OAAMxB,KAAAA,EAAE,gBAAgB,CAAE,CAC7B,EAMDuB,EAAAA,QAAA,cAAA,SAAA,CACC,QAASZ,EACT,UAAU,qDACV,aAAc,IAAML,EAAee,CAAe,EAClD,aAAc,IAAMf,EAAe,IAAI,GAEvCiB,EAAAA,QAAA,cAACC,EAAAA,YAAY,CAAA,OAAO,OAAO,MAAM,MAAM,CAAA,EACvCD,EAAA,QAAA,cAAC,OAAK,CAAA,UAAU,sCACbhB,EAAAA,GAAeP,EAAE,kBAAkB,CACtC,CACF,CAEJ"}