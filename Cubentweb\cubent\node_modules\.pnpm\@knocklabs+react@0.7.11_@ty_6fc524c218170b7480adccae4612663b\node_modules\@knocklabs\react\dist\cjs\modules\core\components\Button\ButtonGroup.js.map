{"version": 3, "file": "ButtonGroup.js", "sources": ["../../../../../../src/modules/core/components/Button/ButtonGroup.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const ButtonGroup: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-button-group\">{children}</div>;\n"], "names": ["ButtonGroup", "children", "React"], "mappings": "yMAIaA,EAERA,CAAC,CAAEC,SAAAA,CAAS,IAAOC,EAAAA,QAAA,cAAA,MAAA,CAAI,UAAU,kBAAA,EAAoBD,CAAS"}