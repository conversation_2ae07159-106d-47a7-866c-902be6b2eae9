"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const i=require("react");;/* empty css            */const s=t=>t&&typeof t=="object"&&"default"in t?t:{default:t},e=s(i),u=({name:t,src:r})=>{function n(c){const[a,l]=c.split(" ");return a&&l?`${a.charAt(0)}${l.charAt(0)}`:a?a.charAt(0):""}return e.default.createElement("div",{className:"rnf-avatar"},r?e.default.createElement("img",{src:r,alt:t,className:"rnf-avatar__image"}):e.default.createElement("span",{className:"rnf-avatar__initials"},n(t)))};exports.Avatar=u;
//# sourceMappingURL=Avatar.js.map
