{"version": 3, "file": "SlackAuthContainer.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackAuthContainer/SlackAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const SlackAuthContainer: FunctionComponent<SlackAuthContainerProps> = ({\n  actionButton,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rsk-auth\">\n      <div className=\"rsk-auth__header\">\n        <SlackIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rsk-auth__title\">Slack</div>\n      <div className=\"rsk-auth__description\">\n        {t(\"slackConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["SlackAuthContainer", "actionButton", "t", "useTranslations", "React", "SlackIcon"], "mappings": ";;;;;AAYO,MAAMA,IAAiEA,CAAC;AAAA,EAC7EC,cAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB;AAE9B,SACGC,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,8CACZ,OAAI,EAAA,WAAU,mBACb,GAAAA,gBAAAA,EAAA,cAACC,GAAU,EAAA,QAAO,QAAO,OAAM,QAAM,GACrCD,gBAAAA,EAAA,cAAC,OAAKH,MAAAA,CAAa,CACrB,GACCG,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,kBAAkB,GAAA,OAAK,GACtCA,gBAAAA,EAAA,cAAC,SAAI,WAAU,2BACZF,EAAE,kCAAkC,CACvC,CACF;AAEJ;"}