{"version": 3, "file": "SlackChannelCombobox.js", "sources": ["../../../../../../src/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport {\n  RecipientObject,\n  SlackChannelQueryOptions,\n  useConnectedSlackChannels,\n  useKnockSlackClient,\n  useSlackChannels,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { useMemo } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { sortSlackChannelsAlphabetically } from \"../../utils\";\nimport SlackAddChannelInput from \"../SlackAddChannelInput/SlackAddChannelInput\";\n\nimport SlackConnectionError from \"./SlackConnectionError\";\nimport SlackErrorMessage from \"./SlackErrorMessage\";\nimport \"./styles.css\";\n\nconst MAX_ALLOWED_CHANNELS = 1000;\n\nexport type SlackChannelComboboxInputMessages = {\n  disconnected: string;\n  error: string;\n  noChannelsConnected: string;\n  noSlackChannelsFound: string;\n};\n\nexport interface SlackChannelComboboxProps {\n  slackChannelsRecipientObject: RecipientObject;\n  queryOptions?: SlackChannelQueryOptions;\n  inputMessages?: SlackChannelComboboxInputMessages;\n}\n\nexport const SlackChannelCombobox: FunctionComponent<\n  SlackChannelComboboxProps\n> = ({ slackChannelsRecipientObject, queryOptions, inputMessages }) => {\n  const { t } = useTranslations();\n\n  // Gather API data\n  const { connectionStatus, errorLabel: connectionErrorLabel } =\n    useKnockSlackClient();\n\n  const { data: unsortedSlackChannels, isLoading: slackChannelsLoading } =\n    useSlackChannels({ queryOptions });\n\n  const slackChannels = useMemo(\n    () => sortSlackChannelsAlphabetically(unsortedSlackChannels),\n    [unsortedSlackChannels],\n  );\n\n  const {\n    data: connectedChannels,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n    updating: connectedChannelsUpdating,\n  } = useConnectedSlackChannels({ slackChannelsRecipientObject });\n\n  const currentConnectedChannels = useMemo<SlackChannelConnection[]>(() => {\n    // Used to make sure we're only showing currently available channels to select from.\n    // There are cases where a channel is \"connected\" in Knock, but it wouldn't be\n    // posting to it if the channel is private and the Slackbot doesn't belong to it,\n    // so the channel won't show up here and it won't be posted to.\n    const slackChannelsMap = new Map(\n      slackChannels.map((channel) => [channel.id, channel]),\n    );\n\n    return (\n      connectedChannels?.filter((connectedChannel) => {\n        return slackChannelsMap.has(connectedChannel.channel_id || \"\");\n      }) || []\n    );\n  }, [connectedChannels, slackChannels]);\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectedChannelsError, connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      slackChannelsLoading,\n\n    [connectionStatus, slackChannelsLoading],\n  );\n\n  // Construct placeholder text\n  const searchPlaceholder = useMemo(() => {\n    const DEFAULT_INPUT_MESSAGES = {\n      disconnected: t(\"slackSearchbarDisconnected\"),\n      noChannelsConnected: t(\"slackSearchbarNoChannelsConnected\"),\n      noSlackChannelsFound: t(\"slackSearchbarNoChannelsFound\"),\n      channelsError: t(\"slackSearchbarChannelsError\"),\n    };\n\n    // Connection status message\n    if (connectionStatus === \"disconnected\") {\n      return inputMessages?.disconnected || DEFAULT_INPUT_MESSAGES.disconnected;\n    }\n\n    if (connectionStatus === \"error\") {\n      return inputMessages?.error || connectionErrorLabel;\n    }\n\n    // Channels status messages\n    if (!inLoadingState && slackChannels.length === 0) {\n      return (\n        inputMessages?.noSlackChannelsFound ||\n        DEFAULT_INPUT_MESSAGES.noSlackChannelsFound\n      );\n    }\n\n    const numberConnectedChannels = currentConnectedChannels?.length || 0;\n\n    if (currentConnectedChannels && numberConnectedChannels === 0) {\n      return (\n        inputMessages?.noChannelsConnected ||\n        DEFAULT_INPUT_MESSAGES.noChannelsConnected\n      );\n    }\n\n    return \"\";\n  }, [\n    connectionStatus,\n    inLoadingState,\n    slackChannels,\n    currentConnectedChannels,\n    inputMessages,\n    connectionErrorLabel,\n    t,\n  ]);\n\n  const comboboxValue = useMemo(\n    () => currentConnectedChannels.map((connection) => connection.channel_id),\n    [currentConnectedChannels],\n  );\n\n  if (slackChannels.length > MAX_ALLOWED_CHANNELS) {\n    return (\n      <SlackAddChannelInput\n        inErrorState={inErrorState}\n        connectedChannels={currentConnectedChannels || []}\n        updateConnectedChannels={updateConnectedChannels}\n        connectedChannelsError={connectedChannelsError}\n        connectedChannelsUpdating={connectedChannelsUpdating}\n      />\n    );\n  }\n\n  return (\n    <Stack className=\"tgph rsk-combobox__grid\" gap=\"3\">\n      <Text\n        color=\"gray\"\n        size=\"2\"\n        as=\"div\"\n        minHeight=\"8\"\n        className=\"rsk-combobox__label\"\n      >\n        Channels\n      </Text>\n      <Combobox.Root\n        value={comboboxValue}\n        onValueChange={(channelIds) => {\n          const updatedConnections = channelIds.map<SlackChannelConnection>(\n            (channelId) => ({\n              channel_id: channelId,\n            }),\n          );\n\n          updateConnectedChannels(updatedConnections).catch(console.error);\n        }}\n        placeholder={searchPlaceholder ?? \"\"}\n        disabled={inErrorState || slackChannels.length === 0}\n        errored={inErrorState}\n        closeOnSelect={false}\n        layout=\"wrap\"\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search\n            label={t(\"slackSearchChannels\")}\n            className=\"rsk-combobox__search\"\n          />\n          <Combobox.Options maxHeight=\"36\">\n            {slackChannels.map((channel) => (\n              <Combobox.Option key={channel.id} value={channel.id}>\n                <Stack align=\"center\" gap=\"1\">\n                  <Icon\n                    icon={channel.is_private ? Lucide.Lock : Lucide.Hash}\n                    size=\"0\"\n                    aria-hidden\n                  />\n                  {channel.name}\n                </Stack>\n              </Combobox.Option>\n            ))}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n      <SlackConnectionError />\n      {!!connectedChannelsError && (\n        <SlackErrorMessage message={connectedChannelsError} />\n      )}\n    </Stack>\n  );\n};\n"], "names": ["MAX_ALLOWED_CHANNELS", "SlackChannelCombobox", "slackChannelsRecipientObject", "queryOptions", "inputMessages", "t", "useTranslations", "connectionStatus", "error<PERSON><PERSON><PERSON>", "connectionErrorLabel", "useKnockSlackClient", "data", "unsortedSlackChannels", "isLoading", "slackChannelsLoading", "useSlackChannels", "slackChannels", "useMemo", "sortSlackChannelsAlphabetically", "connectedChannels", "updateConnectedChannels", "error", "connectedChannelsError", "updating", "connectedChannelsUpdating", "useConnectedSlackChannels", "currentConnectedChannels", "slackChannelsMap", "Map", "map", "channel", "id", "filter", "connectedChannel", "has", "channel_id", "inErrorState", "inLoadingState", "searchPlaceholder", "DEFAULT_INPUT_MESSAGES", "disconnected", "noChannelsConnected", "noSlackChannelsFound", "channelsError", "length", "numberConnectedChannels", "comboboxValue", "connection", "React", "SlackAddChannelInput", "<PERSON><PERSON>", "Text", "Combobox", "channelIds", "updatedConnections", "channelId", "catch", "console", "Icon", "is_private", "Lucide", "Lock", "Hash", "name", "SlackConnectionError", "SlackErrorMessage"], "mappings": "ojBAwBMA,EAAuB,IAehBC,EAETA,CAAC,CAAEC,6BAAAA,EAA8BC,aAAAA,EAAcC,cAAAA,CAAc,IAAM,CAC/D,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EAGxB,CAAEC,iBAAAA,EAAkBC,WAAYC,GACpCC,sBAAoB,EAEhB,CAAEC,KAAMC,EAAuBC,UAAWC,GAC9CC,mBAAiB,CAAEZ,aAAAA,CAAAA,CAAc,EAE7Ba,EAAgBC,EAAAA,QACpB,IAAMC,EAAAA,gCAAgCN,CAAqB,EAC3D,CAACA,CAAqB,CACxB,EAEM,CACJD,KAAMQ,EACNC,wBAAAA,EACAC,MAAOC,EACPC,SAAUC,GACRC,4BAA0B,CAAEvB,6BAAAA,CAAAA,CAA8B,EAExDwB,EAA2BT,EAAAA,QAAkC,IAAM,CAKjEU,MAAAA,EAAmB,IAAIC,IAC3BZ,EAAca,IAAiBC,GAAA,CAACA,EAAQC,GAAID,CAAO,CAAC,CACtD,EAGEX,OAAAA,GAAAA,YAAAA,EAAmBa,OAA6BC,GACvCN,EAAiBO,IAAID,EAAiBE,YAAc,EAAE,KACzD,CAAE,CAAA,EAET,CAAChB,EAAmBH,CAAa,CAAC,EAE/BoB,EAAenB,EAAAA,QACnB,IACEV,IAAqB,gBACrBA,IAAqB,SACrB,CAAC,CAACe,EACJ,CAACA,EAAwBf,CAAgB,CAC3C,EAEM8B,EAAiBpB,EAAAA,QACrB,IACEV,IAAqB,cACrBA,IAAqB,iBACrBO,EAEF,CAACP,EAAkBO,CAAoB,CACzC,EAGMwB,EAAoBrB,EAAAA,QAAQ,IAAM,CACtC,MAAMsB,EAAyB,CAC7BC,aAAcnC,EAAE,4BAA4B,EAC5CoC,oBAAqBpC,EAAE,mCAAmC,EAC1DqC,qBAAsBrC,EAAE,+BAA+B,EACvDsC,cAAetC,EAAE,6BAA6B,CAChD,EAGA,GAAIE,IAAqB,eAChBH,OAAAA,GAAAA,YAAAA,EAAeoC,eAAgBD,EAAuBC,aAG/D,GAAIjC,IAAqB,QACvB,OAAOH,GAAAA,YAAAA,EAAeiB,QAASZ,EAIjC,GAAI,CAAC4B,GAAkBrB,EAAc4B,SAAW,EAE5CxC,OAAAA,GAAAA,YAAAA,EAAesC,uBACfH,EAAuBG,qBAIrBG,MAAAA,GAA0BnB,GAAAA,YAAAA,EAA0BkB,SAAU,EAEhElB,OAAAA,GAA4BmB,IAA4B,GAExDzC,GAAAA,YAAAA,EAAeqC,sBACfF,EAAuBE,oBAIpB,EAAA,EACN,CACDlC,EACA8B,EACArB,EACAU,EACAtB,EACAK,EACAJ,CAAC,CACF,EAEKyC,EAAgB7B,EAAAA,QACpB,IAAMS,EAAyBG,IAAoBkB,GAAAA,EAAWZ,UAAU,EACxE,CAACT,CAAwB,CAC3B,EAEIV,OAAAA,EAAc4B,OAAS5C,EAEvBgD,UAAA,cAACC,GACC,aAAAb,EACA,kBAAmBV,GAA4B,GAC/C,wBAAAN,EACA,uBAAAE,EACA,0BAAAE,CACA,CAAA,EAKJwB,EAAAA,QAAA,cAACE,EAAAA,MAAM,CAAA,UAAU,0BAA0B,IAAI,GAC7C,EAAAF,EAAA,QAAA,cAACG,EACC,KAAA,CAAA,MAAM,OACN,KAAK,IACL,GAAG,MACH,UAAU,IACV,UAAU,qBAAqB,EAAA,UAGjC,EACAH,EAAAA,QAAA,cAACI,EAAAA,SAAS,KAAT,CACC,MAAON,EACP,cAA+BO,GAAA,CACvBC,MAAAA,EAAqBD,EAAWxB,IACpB0B,IAAA,CACdpB,WAAYoB,CAAAA,EAEhB,EAEAnC,EAAwBkC,CAAkB,EAAEE,MAAMC,QAAQpC,KAAK,CAAA,EAEjE,YAAaiB,GAAqB,GAClC,SAAUF,GAAgBpB,EAAc4B,SAAW,EACnD,QAASR,EACT,cAAe,GACf,OAAO,OACP,MAEE,4BAGDgB,WAAS,QAAT,IAAgB,0BAChBA,EAAAA,SAAS,QAAT,KACCJ,UAAA,cAACI,WAAS,OAAT,CACC,MAAO/C,EAAE,qBAAqB,EAC9B,UAAU,sBAAsB,CAAA,EAElC2C,EAAAA,QAAA,cAACI,WAAS,QAAT,CAAiB,UAAU,IACzBpC,EAAAA,EAAca,IACbC,GAAAkB,EAAAA,QAAA,cAACI,EAAS,SAAA,OAAT,CAAgB,IAAKtB,EAAQC,GAAI,MAAOD,EAAQC,IAC9CiB,EAAA,QAAA,cAAAE,QAAA,CAAM,MAAM,SAAS,IAAI,GACxB,EAAAF,UAAA,cAACU,EACC,KAAA,CAAA,KAAM5B,EAAQ6B,WAAaC,EAAAA,OAAOC,KAAOD,SAAOE,KAChD,KAAK,IACL,cAAW,EAAA,CAAA,EAEZhC,EAAQiC,IACX,CACF,CACD,CACH,EACCf,EAAAA,QAAA,cAAAI,WAAS,MAAT,IAAc,CACjB,CACF,EACAJ,UAAA,cAACgB,EAAoB,IAAA,EACpB,CAAC,CAAC1C,2BACA2C,EAAkB,CAAA,QAAS3C,EAC7B,CACH,CAEJ"}