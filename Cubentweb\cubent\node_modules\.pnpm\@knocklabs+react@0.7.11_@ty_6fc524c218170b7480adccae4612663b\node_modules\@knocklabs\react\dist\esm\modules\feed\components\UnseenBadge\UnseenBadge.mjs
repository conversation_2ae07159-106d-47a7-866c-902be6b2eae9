import { useKnockFeed as a, formatBadgeCount as o } from "@knocklabs/react-core";
import u from "react";
/* empty css            */
function s(n, e) {
  switch (n) {
    case "all":
      return e.total_count;
    case "unread":
      return e.unread_count;
    case "unseen":
      return e.unseen_count;
  }
}
const m = ({
  badgeCountType: n = "unseen"
}) => {
  const {
    useFeedStore: e
  } = a(), t = e((r) => s(n, r.metadata));
  return t !== 0 ? /* @__PURE__ */ u.createElement("div", { className: "rnf-unseen-badge" }, /* @__PURE__ */ u.createElement("span", { className: "rnf-unseen-badge__count" }, o(t))) : null;
};
export {
  m as UnseenBadge
};
//# sourceMappingURL=UnseenBadge.mjs.map
