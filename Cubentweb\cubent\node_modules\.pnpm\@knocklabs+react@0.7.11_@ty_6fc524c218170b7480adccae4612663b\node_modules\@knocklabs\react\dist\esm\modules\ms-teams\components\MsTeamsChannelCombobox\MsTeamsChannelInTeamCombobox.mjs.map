{"version": 3, "file": "MsTeamsChannelInTeamCombobox.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelInTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsChannelConnection } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n  useKnockMsTeamsClient,\n  useMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useCallback, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\ninterface MsTeamsChannelInTeamComboboxProps {\n  teamId?: string;\n  msTeamsChannelsRecipientObject: RecipientObject;\n  queryOptions?: MsTeamsChannelQueryOptions;\n}\n\nexport const MsTeamsChannelInTeamCombobox: FunctionComponent<\n  MsTeamsChannelInTeamComboboxProps\n> = ({ teamId, msTeamsChannelsRecipientObject, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: availableChannels = [] } = useMsTeamsChannels({\n    teamId,\n    queryOptions,\n  });\n\n  const sortedChannels = useMemo(\n    () => sortByDisplayName(availableChannels),\n    [availableChannels],\n  );\n\n  const {\n    data: currentConnections,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n  } = useConnectedMsTeamsChannels({ msTeamsChannelsRecipientObject });\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectionStatus, connectedChannelsError],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" || connectionStatus === \"disconnecting\",\n    [connectionStatus],\n  );\n\n  const isChannelInThisTeam = useCallback(\n    (channelId: string) =>\n      availableChannels.some((channel) => channel.id === channelId),\n    [availableChannels],\n  );\n\n  const comboboxValue = useMemo(\n    () =>\n      currentConnections\n        ?.filter(\n          (connection) =>\n            connection.ms_teams_channel_id &&\n            isChannelInThisTeam(connection.ms_teams_channel_id),\n        )\n        .map((connection) => connection.ms_teams_channel_id),\n    [currentConnections, isChannelInThisTeam],\n  );\n\n  return (\n    <>\n      <Box w=\"full\" minW=\"0\">\n        <Combobox.Root\n          value={comboboxValue}\n          onValueChange={(channelIds) => {\n            const connectedChannelsInThisTeam =\n              channelIds.map<MsTeamsChannelConnection>((channelId) => ({\n                ms_teams_team_id: teamId,\n                ms_teams_channel_id: channelId,\n              }));\n            const connectedChannelsInOtherTeams =\n              currentConnections?.filter(\n                (connection) =>\n                  !connection.ms_teams_channel_id ||\n                  !isChannelInThisTeam(connection.ms_teams_channel_id),\n              ) ?? [];\n\n            const updatedConnections = [\n              ...connectedChannelsInOtherTeams,\n              ...connectedChannelsInThisTeam,\n            ];\n\n            updateConnectedChannels(updatedConnections).catch(console.error);\n          }}\n          placeholder=\"Select channels\"\n          disabled={\n            teamId === undefined ||\n            inErrorState ||\n            inLoadingState ||\n            availableChannels.length === 0\n          }\n          closeOnSelect={false}\n          layout=\"wrap\"\n          modal={\n            // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n            false\n          }\n        >\n          <Combobox.Trigger />\n          <Combobox.Content>\n            <Combobox.Search className=\"rtk-combobox__search\" />\n            <Combobox.Options maxHeight=\"36\">\n              {sortedChannels.map((channel) => (\n                <Combobox.Option key={channel.id} value={channel.id}>\n                  {channel.displayName}\n                </Combobox.Option>\n              ))}\n            </Combobox.Options>\n            <Combobox.Empty />\n          </Combobox.Content>\n        </Combobox.Root>\n      </Box>\n      {!!connectedChannelsError && (\n        <MsTeamsErrorMessage message={connectedChannelsError} />\n      )}\n    </>\n  );\n};\n"], "names": ["MsTeamsChannelInTeamCombobox", "teamId", "msTeamsChannelsRecipientObject", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "availableChannels", "useMsTeamsChannels", "sortedChannels", "useMemo", "sortByDisplayName", "currentConnections", "updateConnectedChannels", "error", "connectedChannelsError", "useConnectedMsTeamsChannels", "inErrorState", "inLoadingState", "isChannelInThisTeam", "useCallback", "channelId", "some", "channel", "id", "comboboxValue", "filter", "connection", "ms_teams_channel_id", "map", "React", "Box", "Combobox", "channelIds", "connectedChannelsInThisTeam", "ms_teams_team_id", "updatedConnections", "catch", "console", "undefined", "length", "displayName", "MsTeamsErrorMessage"], "mappings": ";;;;;;AAsBO,MAAMA,IAETA,CAAC;AAAA,EAAEC,QAAAA;AAAAA,EAAQC,gCAAAA;AAAAA,EAAgCC,cAAAA;AAAa,MAAM;AAC1D,QAAA;AAAA,IAAEC,kBAAAA;AAAAA,MAAqBC,EAAsB,GAE7C;AAAA,IAAEC,MAAMC,IAAoB,CAAA;AAAA,MAAOC,EAAmB;AAAA,IAC1DP,QAAAA;AAAAA,IACAE,cAAAA;AAAAA,EAAAA,CACD,GAEKM,IAAiBC,EACrB,MAAMC,EAAkBJ,CAAiB,GACzC,CAACA,CAAiB,CACpB,GAEM;AAAA,IACJD,MAAMM;AAAAA,IACNC,yBAAAA;AAAAA,IACAC,OAAOC;AAAAA,MACLC,EAA4B;AAAA,IAAEd,gCAAAA;AAAAA,EAAAA,CAAgC,GAE5De,IAAeP,EACnB,MACEN,MAAqB,kBACrBA,MAAqB,WACrB,CAAC,CAACW,GACJ,CAACX,GAAkBW,CAAsB,CAC3C,GAEMG,IAAiBR,EACrB,MACEN,MAAqB,gBAAgBA,MAAqB,iBAC5D,CAACA,CAAgB,CACnB,GAEMe,IAAsBC,EAC1B,CAACC,MACCd,EAAkBe,KAAMC,CAAYA,MAAAA,EAAQC,OAAOH,CAAS,GAC9D,CAACd,CAAiB,CACpB,GAEMkB,IAAgBf,EACpB,MACEE,KAAAA,gBAAAA,EACIc,OACCC,CACCA,MAAAA,EAAWC,uBACXT,EAAoBQ,EAAWC,mBAAmB,GAErDC,IAAKF,CAAeA,MAAAA,EAAWC,sBACpC,CAAChB,GAAoBO,CAAmB,CAC1C;AAEA,SAEIW,gBAAAA,EAAA,cAAAA,EAAA,UAAA,MAAAA,gBAAAA,EAAA,cAACC,GAAI,EAAA,GAAE,QAAO,MAAK,OAChBD,gBAAAA,EAAA,cAAAE,EAAS,MAAT,EACC,OAAOP,GACP,eAAgBQ,CAAeA,MAAA;AACvBC,UAAAA,IACJD,EAAWJ,IAA+BR,CAAeA,OAAA;AAAA,MACvDc,kBAAkBlC;AAAAA,MAClB2B,qBAAqBP;AAAAA,IAAAA,EACrB,GAQEe,IAAqB,CACzB,IAPAxB,KAAAA,gBAAAA,EAAoBc,OACjBC,CAAAA,MACC,CAACA,EAAWC,uBACZ,CAACT,EAAoBQ,EAAWC,mBAAmB,OAClD,CAAE,GAIP,GAAGM,CAA2B;AAGhCrB,IAAAA,EAAwBuB,CAAkB,EAAEC,MAAMC,QAAQxB,KAAK;AAAA,EAAA,GAEjE,aAAY,mBACZ,UACEb,MAAWsC,UACXtB,KACAC,KACAX,EAAkBiC,WAAW,GAE/B,eAAe,IACf,QAAO,QACP;AAAA;AAAA,IAEE;AAAA,IAGF,GAAAV,gBAAAA,EAAA,cAACE,EAAS,SAAT,IAAgB,mCAChBA,EAAS,SAAT,MACCF,gBAAAA,EAAA,cAACE,EAAS,QAAT,EAAgB,WAAU,uBAAsB,CAAA,GACjDF,gBAAAA,EAAA,cAACE,EAAS,SAAT,EAAiB,WAAU,KACzBvB,GAAAA,EAAeoB,IAAKN,CAAAA,MAClBO,gBAAAA,EAAA,cAAAE,EAAS,QAAT,EAAgB,KAAKT,EAAQC,IAAI,OAAOD,EAAQC,GAC9CD,GAAAA,EAAQkB,WACX,CACD,CACH,GACCX,gBAAAA,EAAA,cAAAE,EAAS,OAAT,IAAc,CACjB,CACF,CACF,GACC,CAAC,CAACjB,KACAe,gBAAAA,EAAA,cAAAY,GAAA,EAAoB,SAAS3B,EAC/B,CAAA,CACH;AAEJ;"}