{"version": 3, "file": "MsTeamsErrorMessage.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsErrorMessage.tsx"], "sourcesContent": ["import { Icon, Lucide } from \"@telegraph/icon\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent } from \"react\";\n\ninterface Props {\n  message?: string;\n}\n\nconst MsTeamsErrorMessage: FunctionComponent<Props> = ({ message }) => {\n  return (\n    <div className=\"rtk-combobox__error\">\n      <span>\n        <Icon icon={Lucide.Info} color=\"black\" size=\"1\" aria-hidden />\n      </span>\n      <Text as=\"div\" color=\"black\" size=\"1\">\n        {message}\n      </Text>\n    </div>\n  );\n};\n\nexport default MsTeamsErrorMessage;\n"], "names": ["MsTeamsErrorMessage", "message", "React", "Icon", "Lucide", "Info", "Text"], "mappings": "qKAQMA,EAAgDA,CAAC,CAAEC,QAAAA,CAAQ,IAE5DC,EAAAA,QAAA,cAAA,MAAA,CAAI,UAAU,qBAAA,EACZA,UAAA,cAAA,OAAA,KACEA,EAAAA,QAAA,cAAAC,EAAA,KAAA,CAAK,KAAMC,EAAAA,OAAOC,KAAM,MAAM,QAAQ,KAAK,IAAI,cAAW,EAAA,CAAA,CAC7D,EACAH,EAAA,QAAA,cAACI,EAAAA,KAAK,CAAA,GAAG,MAAM,MAAM,QAAQ,KAAK,GAC/BL,EAAAA,CACH,CACF"}