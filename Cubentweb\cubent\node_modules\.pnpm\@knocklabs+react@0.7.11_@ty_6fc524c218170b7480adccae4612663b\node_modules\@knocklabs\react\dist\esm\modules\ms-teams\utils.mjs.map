{"version": 3, "file": "utils.mjs", "sources": ["../../../../src/modules/ms-teams/utils.ts"], "sourcesContent": ["export const sortByDisplayName = <T extends { displayName: string }>(\n  items: T[],\n) =>\n  items.sort((a, b) =>\n    a.displayName.toLowerCase().localeCompare(b.displayName.toLowerCase()),\n  );\n"], "names": ["sortByDisplayName", "items", "sort", "a", "b", "displayName", "toLowerCase", "localeCompare"], "mappings": "AAAO,MAAMA,IAAoB,CAC/BC,MAEAA,EAAMC,KAAK,CAACC,GAAGC,MACbD,EAAEE,YAAYC,cAAcC,cAAcH,EAAEC,YAAYC,aAAa,CACvE;"}