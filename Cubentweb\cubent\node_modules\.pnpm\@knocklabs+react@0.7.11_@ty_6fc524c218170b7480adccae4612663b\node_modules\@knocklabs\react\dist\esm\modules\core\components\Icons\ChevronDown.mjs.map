{"version": 3, "file": "ChevronDown.mjs", "sources": ["../../../../../../src/modules/core/components/Icons/ChevronDown.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype ChevronDownProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst ChevronDown: FunctionComponent<ChevronDownProps> = ({\n  width = 8,\n  height = 6,\n  \"aria-hidden\": ariaHidden,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 8 6\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { ChevronDown };\n"], "names": ["ChevronDown", "width", "height", "ariaHidden", "React"], "mappings": ";AAQA,MAAMA,IAAmDA,CAAC;AAAA,EACxDC,OAAAA,IAAQ;AAAA,EACRC,QAAAA,IAAS;AAAA,EACT,eAAeC;AACjB,MACEC,gBAAAA,EAAA,cAAC,OACC,EAAA,OAAAH,GACA,QAAAC,GACA,SAAQ,WACR,MAAK,QACL,OAAM,8BACN,eAAaC,EAAAA,mCAEZ,QACC,EAAA,GAAE,oDACF,QAAO,gBACP,aAAY,KACZ,eAAc,SACd,gBAAe,QAAA,CAAO,CAE1B;"}