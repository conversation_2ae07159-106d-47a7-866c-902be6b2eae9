"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const N=require("@knocklabs/react-core"),w=require("clsx"),v=require("react"),V=require("../helpers.js");;/* empty css            */const k=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},l=k(w),t=k(v),h="banner",u=({children:e,className:a,...n})=>t.default.createElement("div",{className:l.default("knock-guide-banner",a),...n},e);u.displayName="BannerView.Root";const d=({children:e,className:a,...n})=>t.default.createElement("div",{className:l.default("knock-guide-banner__message",a),...n},e);d.displayName="BannerView.Content";const s=({title:e,className:a,...n})=>t.default.createElement("div",{className:l.default("knock-guide-banner__title",a),...n},e);s.displayName="BannerView.Title";const m=({body:e,className:a,...n})=>t.default.createElement("div",{className:l.default("knock-guide-banner__body",a),dangerouslySetInnerHTML:{__html:e},...n});m.displayName="BannerView.Body";const f=({children:e,className:a,...n})=>t.default.createElement("div",{className:l.default("knock-guide-banner__actions",a),...n},e);f.displayName="BannerView.Actions";const y=({text:e,action:a,className:n,...r})=>t.default.createElement("button",{className:l.default("knock-guide-banner__action",n),...r},e);y.displayName="BannerView.PrimaryButton";const _=({text:e,action:a,className:n,...r})=>t.default.createElement("button",{className:l.default("knock-guide-banner__action knock-guide-banner__action--secondary",n),...r},e);_.displayName="BannerView.SecondaryButton";const b=({className:e,...a})=>t.default.createElement("button",{className:l.default("knock-guide-banner__close",e),...a},t.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},t.default.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},t.default.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),t.default.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));b.displayName="BannerView.DismissButton";const p=({content:e,colorMode:a="light",onDismiss:n,onButtonClick:r})=>t.default.createElement(u,{"data-knock-color-mode":a},t.default.createElement(d,null,t.default.createElement(s,{title:e.title}),t.default.createElement(m,{body:e.body})),t.default.createElement(f,null,e.secondary_button&&t.default.createElement(_,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:o=>{if(r){const{text:c,action:i}=e.secondary_button;r(o,{name:"secondary_button",text:c,action:i})}}}),e.primary_button&&t.default.createElement(y,{text:e.primary_button.text,action:e.primary_button.action,onClick:o=>{if(r){const{text:c,action:i}=e.primary_button;r(o,{name:"primary_button",text:c,action:i})}}}),e.dismissible&&t.default.createElement(b,{onClick:n})));p.displayName="BannerView.Default";const E=({guideKey:e,onButtonClick:a})=>{const{guide:n,step:r,colorMode:o}=N.useGuide({key:e,type:h});return t.default.useEffect(()=>{r&&r.markAsSeen()},[r]),!n||!r?null:t.default.createElement(p,{content:r.content,colorMode:o,onDismiss:()=>r.markAsArchived(),onButtonClick:(c,i)=>{const B={...i,type:"button_click"};return r.markAsInteracted({metadata:B}),a?a(c,{button:i,step:r,guide:n}):V.maybeNavigateToUrlWithDelay(i.action)}})};E.displayName="Banner";const g={};Object.assign(g,{Default:p,Root:u,Content:d,Title:s,Body:m,Actions:f,PrimaryButton:y,SecondaryButton:_,DismissButton:b});exports.Banner=E;exports.BannerView=g;
//# sourceMappingURL=Banner.js.map
