"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("@knocklabs/react-core"),y=require("react"),A=require("../../../core/components/Button/Button.js"),B=require("../../../core/components/Button/ButtonGroup.js");require("lodash.debounce");const T=require("./ArchiveButton.js"),h=require("./Avatar.js");;/* empty css            */const w=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=w(y);function p(e){e&&e!==""&&setTimeout(()=>window.location.assign(e),200)}const M=t.default.forwardRef(({item:e,onItemClick:i,onButtonClick:s,avatar:b,children:f,archiveButton:k},v)=>{var _;const{feedClient:l,colorMode:N}=c.useKnockFeed(),{locale:E}=c.useTranslations(),o=y.useMemo(()=>e.blocks.reduce((n,a)=>({...n,[a.name]:a}),{}),[e]),u=(_=o.action_url)==null?void 0:_.rendered,m=o.actions,d=t.default.useCallback(()=>(l.markAsInteracted(e,{type:"cell_click",action:u}),i?i(e):p(u)),[e,u,i,l]),C=t.default.useCallback((n,a)=>(l.markAsInteracted(e,{type:"button_click",name:a.name,label:a.label,action:a.action}),s?s(e,a):p(a.action)),[s,l,e]),q=t.default.useCallback(n=>{switch(n.key){case"Enter":{n.stopPropagation(),d();break}}},[d]),r=e.actors[0];return t.default.createElement("div",{ref:v,className:`rnf-notification-cell rnf-notification-cell--${N}`,onClick:d,onKeyDown:q,tabIndex:0},t.default.createElement("div",{className:"rnf-notification-cell__inner"},!e.read_at&&t.default.createElement("div",{className:"rnf-notification-cell__unread-dot"}),c.renderNodeOrFallback(b,r&&"name"in r&&r.name&&t.default.createElement(h.Avatar,{name:r.name,src:r.avatar})),t.default.createElement("div",{className:"rnf-notification-cell__content-outer"},o.body&&t.default.createElement("div",{className:"rnf-notification-cell__content",dangerouslySetInnerHTML:{__html:o.body.rendered}}),m&&t.default.createElement("div",{className:"rnf-notification-cell__button-group"},t.default.createElement(B.ButtonGroup,null,m.buttons.map((n,a)=>t.default.createElement(A.Button,{variant:a===0?"primary":"secondary",key:n.name,onClick:g=>C(g,n)},n.label)))),f&&t.default.createElement("div",{className:"rnf-notification-cell__child-content"},f),t.default.createElement("span",{className:"rnf-notification-cell__timestamp"},c.formatTimestamp(e.inserted_at,{locale:E}))),c.renderNodeOrFallback(k,t.default.createElement(T.ArchiveButton,{item:e}))))});exports.NotificationCell=M;
//# sourceMappingURL=NotificationCell.js.map
