"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const i=require("react"),s=require("@knocklabs/react-core"),h=require("../../../core/utils.js");;/* empty css               */const l=require("../SlackIcon/SlackIcon.js");;/* empty css            */const C=t=>t&&typeof t=="object"&&"default"in t?t:{default:t},e=C(i),L=({slackClientId:t,redirectUrl:p,onAuthenticationComplete:u,scopes:d,additionalScopes:k})=>{const{t:n}=s.useTranslations(),f=s.useKnockClient(),{setConnectionStatus:o,connectionStatus:c,setActionLabel:a,actionLabel:_,errorLabel:E}=s.useKnockSlackClient(),g=i.useMemo(()=>({scopes:d,additionalScopes:k}),[d,k]),{buildSlackAuthUrl:m,disconnectFromSlack:S}=s.useSlackAuth(t,p,g);i.useEffect(()=>{const b=r=>{if(r.origin===f.host)try{r.data==="authComplete"&&o("connected"),r.data==="authFailed"&&o("error"),u&&u(r.data)}catch{o("error")}};return window.addEventListener("message",b,!1),()=>{window.removeEventListener("message",b)}},[f.host,u,o]);const w=n("slackDisconnect")||null,x=n("slackReconnect")||null;return c==="connecting"||c==="disconnecting"?e.default.createElement("div",{className:"rsk-connect__button rsk-connect__button--loading"},e.default.createElement(l.SlackIcon,{height:"16px",width:"16px"}),e.default.createElement("span",null,n(c==="connecting"?"slackConnecting":"slackDisconnecting"))):c==="error"?e.default.createElement("button",{onClick:()=>h.openPopupWindow(m()),className:"rsk-connect__button rsk-connect__button--error",onMouseEnter:()=>a(x),onMouseLeave:()=>a(null)},e.default.createElement(l.SlackIcon,{height:"16px",width:"16px"}),e.default.createElement("span",{className:"rsk-connect__button__text--error"},_||E||n("slackError"))):c==="disconnected"?e.default.createElement("button",{onClick:()=>h.openPopupWindow(m()),className:"rsk-connect__button rsk-connect__button--disconnected"},e.default.createElement(l.SlackIcon,{height:"16px",width:"16px"}),e.default.createElement("span",null,n("slackConnect"))):e.default.createElement("button",{onClick:S,className:"rsk-connect__button rsk-connect__button--connected",onMouseEnter:()=>a(w),onMouseLeave:()=>a(null)},e.default.createElement(l.SlackIcon,{height:"16px",width:"16px"}),e.default.createElement("span",{className:"rsk-connect__button__text--connected"},_||n("slackConnected")))};exports.SlackAuthButton=L;
//# sourceMappingURL=SlackAuthButton.js.map
