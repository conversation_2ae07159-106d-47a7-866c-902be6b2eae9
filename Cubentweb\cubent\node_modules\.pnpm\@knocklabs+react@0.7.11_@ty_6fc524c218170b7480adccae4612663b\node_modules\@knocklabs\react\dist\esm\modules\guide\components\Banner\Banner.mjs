import { useGuide as g } from "@knocklabs/react-core";
import i from "clsx";
import t from "react";
import { maybeNavigateToUrlWithDelay as N } from "../helpers.mjs";
/* empty css            */
const f = "banner", s = ({
  children: e,
  className: n,
  ...a
}) => /* @__PURE__ */ t.createElement("div", { className: i("knock-guide-banner", n), ...a }, e);
s.displayName = "BannerView.Root";
const m = ({
  children: e,
  className: n,
  ...a
}) => /* @__PURE__ */ t.createElement("div", { className: i("knock-guide-banner__message", n), ...a }, e);
m.displayName = "BannerView.Content";
const d = ({
  title: e,
  className: n,
  ...a
}) => /* @__PURE__ */ t.createElement("div", { className: i("knock-guide-banner__title", n), ...a }, e);
d.displayName = "BannerView.Title";
const u = ({
  body: e,
  className: n,
  ...a
}) => /* @__PURE__ */ t.createElement("div", { className: i("knock-guide-banner__body", n), dangerouslySetInnerHTML: {
  __html: e
}, ...a });
u.displayName = "BannerView.Body";
const y = ({
  children: e,
  className: n,
  ...a
}) => /* @__PURE__ */ t.createElement("div", { className: i("knock-guide-banner__actions", n), ...a }, e);
y.displayName = "BannerView.Actions";
const _ = ({
  text: e,
  action: n,
  className: a,
  ...r
}) => /* @__PURE__ */ t.createElement("button", { className: i("knock-guide-banner__action", a), ...r }, e);
_.displayName = "BannerView.PrimaryButton";
const b = ({
  text: e,
  action: n,
  className: a,
  ...r
}) => /* @__PURE__ */ t.createElement("button", { className: i("knock-guide-banner__action knock-guide-banner__action--secondary", a), ...r }, e);
b.displayName = "BannerView.SecondaryButton";
const p = ({
  className: e,
  ...n
}) => /* @__PURE__ */ t.createElement("button", { className: i("knock-guide-banner__close", e), ...n }, /* @__PURE__ */ t.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "18", height: "18", fill: "none" }, /* @__PURE__ */ t.createElement("g", { fill: "#60646C", fillRule: "evenodd", clipRule: "evenodd" }, /* @__PURE__ */ t.createElement("path", { d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z" }), /* @__PURE__ */ t.createElement("path", { d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z" }))));
p.displayName = "BannerView.DismissButton";
const k = ({
  content: e,
  colorMode: n = "light",
  onDismiss: a,
  onButtonClick: r
}) => /* @__PURE__ */ t.createElement(s, { "data-knock-color-mode": n }, /* @__PURE__ */ t.createElement(m, null, /* @__PURE__ */ t.createElement(d, { title: e.title }), /* @__PURE__ */ t.createElement(u, { body: e.body })), /* @__PURE__ */ t.createElement(y, null, e.secondary_button && /* @__PURE__ */ t.createElement(b, { text: e.secondary_button.text, action: e.secondary_button.action, onClick: (c) => {
  if (r) {
    const {
      text: l,
      action: o
    } = e.secondary_button;
    r(c, {
      name: "secondary_button",
      text: l,
      action: o
    });
  }
} }), e.primary_button && /* @__PURE__ */ t.createElement(_, { text: e.primary_button.text, action: e.primary_button.action, onClick: (c) => {
  if (r) {
    const {
      text: l,
      action: o
    } = e.primary_button;
    r(c, {
      name: "primary_button",
      text: l,
      action: o
    });
  }
} }), e.dismissible && /* @__PURE__ */ t.createElement(p, { onClick: a })));
k.displayName = "BannerView.Default";
const B = ({
  guideKey: e,
  onButtonClick: n
}) => {
  const {
    guide: a,
    step: r,
    colorMode: c
  } = g({
    key: e,
    type: f
  });
  return t.useEffect(() => {
    r && r.markAsSeen();
  }, [r]), !a || !r ? null : /* @__PURE__ */ t.createElement(k, { content: r.content, colorMode: c, onDismiss: () => r.markAsArchived(), onButtonClick: (l, o) => {
    const E = {
      ...o,
      type: "button_click"
    };
    return r.markAsInteracted({
      metadata: E
    }), n ? n(l, {
      button: o,
      step: r,
      guide: a
    }) : N(o.action);
  } });
};
B.displayName = "Banner";
const w = {};
Object.assign(w, {
  Default: k,
  Root: s,
  Content: m,
  Title: d,
  Body: u,
  Actions: y,
  PrimaryButton: _,
  SecondaryButton: b,
  DismissButton: p
});
export {
  B as Banner,
  w as BannerView
};
//# sourceMappingURL=Banner.mjs.map
