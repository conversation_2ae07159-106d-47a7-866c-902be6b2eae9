{"version": 3, "file": "UnseenBadge.mjs", "sources": ["../../../../../../src/modules/feed/components/UnseenBadge/UnseenBadge.tsx"], "sourcesContent": ["import { FeedMetadata } from \"@knocklabs/client\";\nimport { formatBadgeCount, useKnockFeed } from \"@knocklabs/react-core\";\nimport React from \"react\";\n\nimport \"./styles.css\";\n\nexport type BadgeCountType = \"unseen\" | \"unread\" | \"all\";\n\nexport type UnseenBadgeProps = {\n  badgeCountType?: BadgeCountType;\n};\n\nfunction selectBadgeCount(\n  badgeCountType: BadgeCountType,\n  metadata: FeedMetadata,\n) {\n  switch (badgeCountType) {\n    case \"all\":\n      return metadata.total_count;\n    case \"unread\":\n      return metadata.unread_count;\n    case \"unseen\":\n      return metadata.unseen_count;\n  }\n}\n\nexport const UnseenBadge: React.FC<UnseenBadgeProps> = ({\n  badgeCountType = \"unseen\",\n}) => {\n  const { useFeedStore } = useKnockFeed();\n  const badgeCountValue = useFeedStore((state) =>\n    selectBadgeCount(badgeCountType, state.metadata),\n  );\n\n  return badgeCountValue !== 0 ? (\n    <div className=\"rnf-unseen-badge\">\n      <span className=\"rnf-unseen-badge__count\">\n        {formatBadgeCount(badgeCountValue)}\n      </span>\n    </div>\n  ) : null;\n};\n"], "names": ["selectBadgeCount", "badgeCountType", "metadata", "total_count", "unread_count", "unseen_count", "UnseenBadge", "useFeedStore", "useKnockFeed", "badgeCountValue", "state", "React", "formatBadgeCount"], "mappings": ";;;AAYA,SAASA,EACPC,GACAC,GACA;AACA,UAAQD,GAAc;AAAA,IACpB,KAAK;AACH,aAAOC,EAASC;AAAAA,IAClB,KAAK;AACH,aAAOD,EAASE;AAAAA,IAClB,KAAK;AACH,aAAOF,EAASG;AAAAA,EAAAA;AAEtB;AAEO,MAAMC,IAA0CA,CAAC;AAAA,EACtDL,gBAAAA,IAAiB;AACnB,MAAM;AACE,QAAA;AAAA,IAAEM,cAAAA;AAAAA,MAAiBC,EAAa,GAChCC,IAAkBF,EAAcG,CAAAA,MACpCV,EAAiBC,GAAgBS,EAAMR,QAAQ,CACjD;AAEA,SAAOO,MAAoB,IACxBE,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,sBACbA,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,0BACbC,GAAAA,EAAiBH,CAAe,CACnC,CACF,IACE;AACN;"}