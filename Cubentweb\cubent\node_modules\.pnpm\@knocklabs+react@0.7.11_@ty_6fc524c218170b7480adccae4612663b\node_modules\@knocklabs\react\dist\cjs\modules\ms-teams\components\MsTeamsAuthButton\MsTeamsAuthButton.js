"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const b=require("react"),u=require("@knocklabs/react-core"),f=require("../../../core/utils.js");;/* empty css               */const l=require("../MsTeamsIcon/MsTeamsIcon.js");;/* empty css            */const g=t=>t&&typeof t=="object"&&"default"in t?t:{default:t},e=g(b),h=({msTeamsBotId:t,redirectUrl:T,onAuthenticationComplete:s})=>{const{t:n}=u.useTranslations(),i=u.useKnockClient(),{setConnectionStatus:o,connectionStatus:c,setActionLabel:a,actionLabel:d,errorLabel:p}=u.useKnockMsTeamsClient(),{buildMsTeamsAuthUrl:m,disconnectFromMsTeams:E}=u.useMsTeamsAuth(t,T);b.useEffect(()=>{const _=r=>{if(r.origin===i.host)try{r.data==="authComplete"&&o("connected"),r.data==="authFailed"&&o("error"),s==null||s(r.data)}catch{o("error")}};return window.addEventListener("message",_,!1),()=>{window.removeEventListener("message",_)}},[i.host,s,o]);const k=n("msTeamsDisconnect")||null,M=n("msTeamsReconnect")||null;return c==="connecting"||c==="disconnecting"?e.default.createElement("div",{className:"rtk-connect__button rtk-connect__button--loading"},e.default.createElement(l.MsTeamsIcon,{height:"16px",width:"16px"}),e.default.createElement("span",null,n(c==="connecting"?"msTeamsConnecting":"msTeamsDisconnecting"))):c==="error"?e.default.createElement("button",{onClick:()=>f.openPopupWindow(m()),className:"rtk-connect__button rtk-connect__button--error",onMouseEnter:()=>a(M),onMouseLeave:()=>a(null)},e.default.createElement(l.MsTeamsIcon,{height:"16px",width:"16px"}),e.default.createElement("span",{className:"rtk-connect__button__text--error"},d||p||n("msTeamsError"))):c==="disconnected"?e.default.createElement("button",{onClick:()=>f.openPopupWindow(m()),className:"rtk-connect__button rtk-connect__button--disconnected"},e.default.createElement(l.MsTeamsIcon,{height:"16px",width:"16px"}),e.default.createElement("span",null,n("msTeamsConnect"))):e.default.createElement("button",{onClick:E,className:"rtk-connect__button rtk-connect__button--connected",onMouseEnter:()=>a(k),onMouseLeave:()=>a(null)},e.default.createElement(l.MsTeamsIcon,{height:"16px",width:"16px"}),e.default.createElement("span",{className:"rtk-connect__button__text--connected"},d||n("msTeamsConnected")))};exports.MsTeamsAuthButton=h;
//# sourceMappingURL=MsTeamsAuthButton.js.map
