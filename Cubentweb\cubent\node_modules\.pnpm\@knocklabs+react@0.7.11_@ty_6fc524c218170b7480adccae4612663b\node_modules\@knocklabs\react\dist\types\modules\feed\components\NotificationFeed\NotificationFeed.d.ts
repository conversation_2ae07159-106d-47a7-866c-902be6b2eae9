import { FeedItem } from '@knocklabs/client';
import { FilterStatus } from '@knocklabs/react-core';
import { GenericData } from '@knocklabs/types';
import { default as React, ReactNode } from 'react';
import { NotificationCellProps } from '../NotificationCell';
import { NotificationFeedHeaderProps } from './NotificationFeedHeader';
export type RenderItemProps<T = GenericData> = {
    item: FeedItem<T>;
    onItemClick?: NotificationCellProps["onItemClick"];
    onButtonClick?: NotificationCellProps["onButtonClick"];
};
export type RenderItem = (props: RenderItemProps) => ReactNode;
export interface NotificationFeedProps {
    EmptyComponent?: ReactNode;
    /**
     * @deprecated Use `renderHeader` instead to accept `NotificationFeedHeaderProps`
     */
    header?: ReactNode;
    renderItem?: RenderItem;
    renderHeader?: (props: NotificationFeedHeaderProps) => ReactNode;
    onNotificationClick?: NotificationCellProps["onItemClick"];
    onNotificationButtonClick?: NotificationCellProps["onButtonClick"];
    onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;
    initialFilterStatus?: FilterStatus;
}
export declare const NotificationFeed: React.FC<NotificationFeedProps>;
//# sourceMappingURL=NotificationFeed.d.ts.map