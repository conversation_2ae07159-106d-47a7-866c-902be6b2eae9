import { useTranslations as y, useKnockFeed as _ } from "@knocklabs/react-core";
import { createPopper as E } from "@popperjs/core";
import n, { useEffect as c } from "react";
import F from "../../../core/hooks/useComponentVisible.mjs";
import { NotificationFeed as N } from "../NotificationFeed/NotificationFeed.mjs";
import "../NotificationFeed/NotificationFeedHeader.mjs";
/* empty css                              */
/* empty css            */
const x = ({
  store: e,
  feedClient: t
}) => {
  e.metadata.unseen_count > 0 && t.markAllAsSeen();
}, K = ({
  isVisible: e,
  onOpen: t = x,
  onClose: p,
  buttonRef: r,
  closeOnClickOutside: s = !0,
  placement: i = "bottom-end",
  ...m
}) => {
  const {
    t: d
  } = y(), {
    colorMode: l,
    feedClient: f,
    useFeedStore: u
  } = _(), a = u(), {
    ref: o
  } = F(e, p, {
    closeOnClickOutside: s
  });
  return c(() => {
    e && t && t({
      store: a,
      feedClient: f
    });
  }, [e, t, a, f]), c(() => {
    if (r.current && o.current) {
      const v = E(r.current, o.current, {
        strategy: "fixed",
        placement: i,
        modifiers: [{
          name: "offset",
          options: {
            offset: [0, 8]
          }
        }]
      });
      return () => {
        v.destroy();
      };
    }
  }, [r, o, i]), /* @__PURE__ */ n.createElement("div", { className: `rnf-notification-feed-popover rnf-notification-feed-popover--${l}`, style: {
    visibility: e ? "visible" : "hidden",
    opacity: e ? 1 : 0
  }, ref: o, role: "dialog", "aria-label": d("notifications"), tabIndex: -1 }, /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-feed-popover__inner" }, /* @__PURE__ */ n.createElement(N, { ...m })));
};
export {
  K as NotificationFeedPopover
};
//# sourceMappingURL=NotificationFeedPopover.mjs.map
