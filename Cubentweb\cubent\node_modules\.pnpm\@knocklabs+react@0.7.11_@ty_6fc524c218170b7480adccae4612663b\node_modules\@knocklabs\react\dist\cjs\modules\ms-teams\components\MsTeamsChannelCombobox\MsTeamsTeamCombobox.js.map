{"version": 3, "file": "MsTeamsTeamCombobox.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsTeam } from \"@knocklabs/client\";\nimport {\n  MsTeamsTeamQueryOptions,\n  useKnockMsTeamsClient,\n  useMsTeamsTeams,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\ninterface MsTeamsTeamComboboxProps {\n  team: MsTeamsTeam | null;\n  onTeamChange: (team: MsTeamsTeam) => void;\n  getChannelCount: (teamId: string) => number;\n  queryOptions?: MsTeamsTeamQueryOptions;\n}\n\nexport const MsTeamsTeamCombobox: FunctionComponent<\n  MsTeamsTeamComboboxProps\n> = ({ team, onTeamChange, getChannelCount, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: teams, isLoading: isLoadingTeams } = useMsTeamsTeams({\n    queryOptions,\n  });\n\n  const sortedTeams = useMemo(() => sortByDisplayName(teams), [teams]);\n\n  const inErrorState = useMemo(\n    () => connectionStatus === \"disconnected\" || connectionStatus === \"error\",\n    [connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      isLoadingTeams,\n    [connectionStatus, isLoadingTeams],\n  );\n\n  return (\n    <Box w=\"full\" minW=\"0\">\n      <Combobox.Root\n        value={team?.id}\n        onValueChange={(teamId) => {\n          const selectedTeam = sortedTeams.find((team) => team.id === teamId);\n          if (selectedTeam) {\n            onTeamChange(selectedTeam);\n          }\n        }}\n        placeholder=\"Select team\"\n        disabled={inErrorState || inLoadingState || sortedTeams.length === 0}\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search className=\"rtk-combobox__search\" />\n          <Combobox.Options maxHeight=\"36\">\n            {sortedTeams.map((team) => {\n              const channelCount = getChannelCount(team.id);\n              return (\n                <Combobox.Option key={team.id} value={team.id}>\n                  {channelCount > 0\n                    ? `${team.displayName} (${channelCount})`\n                    : team.displayName}\n                </Combobox.Option>\n              );\n            })}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n    </Box>\n  );\n};\n"], "names": ["MsTeamsTeamCombobox", "team", "onTeamChange", "getChannelCount", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "teams", "isLoading", "isLoadingTeams", "useMsTeamsTeams", "sortedTeams", "useMemo", "sortByDisplayName", "inErrorState", "inLoadingState", "React", "Box", "Combobox", "id", "teamId", "selectedTeam", "find", "length", "map", "channelCount", "displayName"], "mappings": "uSAmBaA,EAETA,CAAC,CAAEC,KAAAA,EAAMC,aAAAA,EAAcC,gBAAAA,EAAiBC,aAAAA,CAAa,IAAM,CACvD,KAAA,CAAEC,iBAAAA,GAAqBC,wBAAsB,EAE7C,CAAEC,KAAMC,EAAOC,UAAWC,GAAmBC,kBAAgB,CACjEP,aAAAA,CAAAA,CACD,EAEKQ,EAAcC,EAAAA,QAAQ,IAAMC,EAAAA,kBAAkBN,CAAK,EAAG,CAACA,CAAK,CAAC,EAE7DO,EAAeF,UACnB,IAAMR,IAAqB,gBAAkBA,IAAqB,QAClE,CAACA,CAAgB,CACnB,EAEMW,EAAiBH,EAAAA,QACrB,IACER,IAAqB,cACrBA,IAAqB,iBACrBK,EACF,CAACL,EAAkBK,CAAc,CACnC,EAEA,OACGO,EAAA,QAAA,cAAAC,MAAA,CAAI,EAAE,OAAO,KAAK,KACjBD,EAAAA,QAAA,cAACE,EAAS,SAAA,KAAT,CACC,MAAOlB,GAAAA,YAAAA,EAAMmB,GACb,cAA2BC,GAAA,CACzB,MAAMC,EAAeV,EAAYW,KAAMtB,GAASA,EAAKmB,KAAOC,CAAM,EAC9DC,GACFpB,EAAaoB,CAAY,CAC3B,EAEF,YAAY,cACZ,SAAUP,GAAgBC,GAAkBJ,EAAYY,SAAW,EACnE,MAEE,EAGF,EAAAP,EAAAA,QAAA,cAACE,EAAAA,SAAS,QAAT,IAAgB,0BAChBA,EAAS,SAAA,QAAT,KACCF,EAAA,QAAA,cAACE,EAAS,SAAA,OAAT,CAAgB,UAAU,sBAAA,CAAsB,EACjDF,EAAA,QAAA,cAACE,EAAS,SAAA,QAAT,CAAiB,UAAU,IACzBP,EAAAA,EAAYa,IAAKxB,GAAS,CACnByB,MAAAA,EAAevB,EAAgBF,EAAKmB,EAAE,EAC5C,+BACGD,EAAAA,SAAS,OAAT,CAAgB,IAAKlB,EAAKmB,GAAI,MAAOnB,EAAKmB,IACxCM,EAAe,EACZ,GAAGzB,EAAK0B,WAAW,KAAKD,CAAY,IACpCzB,EAAK0B,WACX,CAAA,CAEH,CACH,EACAV,EAAAA,QAAA,cAACE,WAAS,MAAT,IAAc,CACjB,CACF,CACF,CAEJ"}