"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const l=require("react"),c=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=c(l);function s(e){return e==="fast"?600:e==="slow"?900:750}const d=({color:e="rgba(0,0,0,0.4)",speed:r="medium",gap:a=4,thickness:n=4,size:i="1em",...o})=>t.default.createElement("svg",{height:i,width:i,...o,style:{animationDuration:`${s(r)}ms`},className:"__react-svg-spinner_circle",role:"img","aria-labelledby":"title desc",viewBox:"0 0 32 32"},t.default.createElement("title",{id:"title"},"Circle loading spinner"),t.default.createElement("desc",{id:"desc"},'Image of a partial circle indicating "loading."'),t.default.createElement("style",{dangerouslySetInnerHTML:{__html:`
      .__react-svg-spinner_circle{
          transition-property: transform;
          animation-name: __react-svg-spinner_infinite-spin;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
      }
      @keyframes __react-svg-spinner_infinite-spin {
          from {transform: rotate(0deg)}
          to {transform: rotate(360deg)}
      }
    `}}),t.default.createElement("circle",{role:"presentation",cx:16,cy:16,r:14-n/2,stroke:e,fill:"none",strokeWidth:n,strokeDasharray:Math.PI*2*(11-a),strokeLinecap:"round"}));exports.Spinner=d;
//# sourceMappingURL=Spinner.js.map
