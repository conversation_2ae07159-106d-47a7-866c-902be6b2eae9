// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    type BalanceTransactionSource =
      | ApplicationFee
      | Charge
      | ConnectCollectionTransfer
      | CustomerCashBalanceTransaction
      | Dispute
      | FeeRefund
      | Issuing.Authorization
      | Issuing.Dispute
      | Issuing.Transaction
      | Payout
      | Refund
      | ReserveTransaction
      | TaxDeductedAtSource
      | Topup
      | Transfer
      | TransferReversal;
  }
}
