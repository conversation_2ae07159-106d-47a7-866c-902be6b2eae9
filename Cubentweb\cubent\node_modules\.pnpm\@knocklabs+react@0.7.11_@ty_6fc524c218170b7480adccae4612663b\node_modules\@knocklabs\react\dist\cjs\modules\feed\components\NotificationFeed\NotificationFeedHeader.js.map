{"version": 3, "file": "NotificationFeedHeader.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/NotificationFeedHeader.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { FilterStatus, useTranslations } from \"@knocklabs/react-core\";\nimport React, { SetStateAction } from \"react\";\n\nimport { Dropdown } from \"./Dropdown\";\nimport { MarkAsRead } from \"./MarkAsRead\";\n\nexport type NotificationFeedHeaderProps = {\n  filterStatus: FilterStatus;\n  setFilterStatus: React.Dispatch<SetStateAction<FilterStatus>>;\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nconst OrderedFilterStatuses = [\n  FilterStatus.All,\n  FilterStatus.Unread,\n  FilterStatus.Read,\n];\n\nexport const NotificationFeedHeader: React.FC<NotificationFeedHeaderProps> = ({\n  onMarkAllAsReadClick,\n  filterStatus,\n  setFilterStatus,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <header className=\"rnf-notification-feed__header\">\n      <div className=\"rnf-notification-feed__selector\">\n        <span className=\"rnf-notification-feed__type\">\n          {t(\"notifications\")}\n        </span>\n        <Dropdown\n          value={filterStatus}\n          onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}\n        >\n          {OrderedFilterStatuses.map((filterStatus) => (\n            <option key={filterStatus} value={filterStatus}>\n              {t(filterStatus)}\n            </option>\n          ))}\n        </Dropdown>\n      </div>\n      <MarkAsRead onClick={onMarkAllAsReadClick} />\n    </header>\n  );\n};\n"], "names": ["OrderedFilterStatuses", "FilterStatus", "All", "Unread", "Read", "NotificationFeedHeader", "onMarkAllAsReadClick", "filterStatus", "setFilterStatus", "t", "useTranslations", "React", "Dropdown", "e", "target", "value", "map", "MarkAsRead"], "mappings": "mQAaMA,EAAwB,CAC5BC,EAAAA,aAAaC,IACbD,EAAaE,aAAAA,OACbF,eAAaG,IAAI,EAGNC,EAAgEA,CAAC,CAC5EC,qBAAAA,EACAC,aAAAA,EACAC,gBAAAA,CACF,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EAG5B,OAAAC,EAAA,QAAA,cAAC,UAAO,UAAU,+BAAA,0BACf,MAAI,CAAA,UAAU,iCACb,EAAAA,EAAA,QAAA,cAAC,OAAK,CAAA,UAAU,+BACbF,EAAE,eAAe,CACpB,EACAE,EAAAA,QAAA,cAACC,EAAAA,UACC,MAAOL,EACP,SAAWM,GAAML,EAAgBK,EAAEC,OAAOC,KAAqB,CAAA,EAE9Df,EAAsBgB,IAAKT,GACzBI,EAAAA,QAAA,cAAA,SAAA,CAAO,IAAKJ,EAAc,MAAOA,CAAAA,EAC/BE,EAAEF,CAAY,CACjB,CACD,CACH,CACF,EACCI,EAAA,QAAA,cAAAM,EAAA,WAAA,CAAW,QAASX,CAAqB,CAAA,CAC5C,CAEJ"}