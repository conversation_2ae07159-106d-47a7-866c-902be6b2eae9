import { useKnockFeed as c } from "@knocklabs/react-core";
import e from "react";
import { ButtonSpinner as b } from "./ButtonSpinner.mjs";
/* empty css            */
const _ = ({
  variant: r = "primary",
  loadingText: n,
  isLoading: t = !1,
  isDisabled: a = !1,
  isFullWidth: s = !1,
  onClick: l,
  children: o
}) => {
  const {
    colorMode: u
  } = c(), f = ["rnf-button", `rnf-button--${r}`, s ? "rnf-button--full-width" : "", t ? "rnf-button--is-loading" : "", `rnf-button--${u}`].join(" "), m = n || /* @__PURE__ */ e.createElement("span", { className: "rnf-button__button-text-hidden" }, o);
  return /* @__PURE__ */ e.createElement("button", { onClick: l, className: f, disabled: t || a, type: "button" }, t && /* @__PURE__ */ e.createElement(b, { hasLabel: !!n }), t ? m : o);
};
export {
  _ as Button
};
//# sourceMappingURL=Button.mjs.map
