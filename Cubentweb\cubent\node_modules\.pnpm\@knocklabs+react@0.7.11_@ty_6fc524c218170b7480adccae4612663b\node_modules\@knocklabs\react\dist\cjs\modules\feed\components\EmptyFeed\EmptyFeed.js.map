{"version": 3, "file": "EmptyFeed.js", "sources": ["../../../../../../src/modules/feed/components/EmptyFeed/EmptyFeed.tsx"], "sourcesContent": ["import { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"./styles.css\";\n\nexport const EmptyFeed: FunctionComponent = () => {\n  const { colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  return (\n    <div className={`rnf-empty-feed rnf-empty-feed--${colorMode}`}>\n      <div className=\"rnf-empty-feed__inner\">\n        <h2 className=\"rnf-empty-feed__header\">{t(\"emptyFeedTitle\")}</h2>\n        <p className=\"rnf-empty-feed__body\">{t(\"emptyFeedBody\")}</p>\n      </div>\n    </div>\n  );\n};\n"], "names": ["EmptyFeed", "colorMode", "useKnockFeed", "t", "useTranslations", "React"], "mappings": "4OAKaA,EAA+BA,IAAM,CAC1C,KAAA,CAAEC,UAAAA,GAAcC,eAAa,EAC7B,CAAEC,EAAAA,GAAMC,kBAAgB,EAG5B,OAAAC,EAAAA,QAAA,cAAC,MAAI,CAAA,UAAW,kCAAkCJ,CAAS,EACzD,EAAAI,EAAAA,QAAA,cAAC,MAAI,CAAA,UAAU,uBACb,EAAAA,UAAA,cAAC,KAAG,CAAA,UAAU,0BAA0BF,EAAE,gBAAgB,CAAE,EAC3DE,EAAA,QAAA,cAAA,IAAA,CAAE,UAAU,sBAAA,EAAwBF,EAAE,eAAe,CAAE,CAC1D,CACF,CAEJ"}