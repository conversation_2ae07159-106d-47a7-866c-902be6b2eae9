{"version": 3, "file": "MarkAsRead.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/MarkAsRead.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport * as React from \"react\";\n\nimport { CheckmarkCircle } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type MarkAsReadProps = {\n  onClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nexport const MarkAsRead: React.FC<MarkAsReadProps> = ({ onClick }) => {\n  const { useFeedStore, feedClient, colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  const unreadItems = useFeedStore((state) =>\n    state.items.filter((item) => !item.read_at),\n  );\n\n  const unreadCount = useFeedStore((state) => state.metadata.unread_count);\n\n  const onClickHandler = React.useCallback(\n    (e: React.MouseEvent) => {\n      feedClient.markAllAsRead();\n      if (onClick) onClick(e, unreadItems);\n    },\n    [feedClient, unreadItems, onClick],\n  );\n\n  return (\n    <button\n      className={`rnf-mark-all-as-read rnf-mark-all-as-read--${colorMode}`}\n      disabled={unreadCount === 0}\n      onClick={onClickHandler}\n      type=\"button\"\n    >\n      {t(\"markAllAsRead\")}\n      <CheckmarkCircle aria-hidden />\n    </button>\n  );\n};\n"], "names": ["MarkAsRead", "onClick", "useFeedStore", "feedClient", "colorMode", "useKnockFeed", "t", "useTranslations", "unreadItems", "state", "items", "filter", "item", "read_at", "unreadCount", "metadata", "unread_count", "onClickHandler", "React", "useCallback", "e", "markAllAsRead", "CheckmarkCircle"], "mappings": "ijBAYaA,EAAwCA,CAAC,CAAEC,QAAAA,CAAQ,IAAM,CAC9D,KAAA,CAAEC,aAAAA,EAAcC,WAAAA,EAAYC,UAAAA,GAAcC,eAAa,EACvD,CAAEC,EAAAA,GAAMC,kBAAgB,EAExBC,EAAcN,EAAcO,GAChCA,EAAMC,MAAMC,OAAiBC,GAAA,CAACA,EAAKC,OAAO,CAC5C,EAEMC,EAAcZ,EAAwBO,GAAAA,EAAMM,SAASC,YAAY,EAEjEC,EAAiBC,EAAMC,YAC1BC,GAAwB,CACvBjB,EAAWkB,cAAc,EACrBpB,GAAiBmB,EAAAA,EAAGZ,CAAW,CAErC,EAAA,CAACL,EAAYK,EAAaP,CAAO,CACnC,EAGE,OAAAiB,EAAA,cAAC,UACC,UAAW,8CAA8Cd,CAAS,GAClE,SAAUU,IAAgB,EAC1B,QAASG,EACT,KAAK,UAEJX,EAAE,eAAe,kBACjBgB,EAAAA,gBAAgB,CAAA,cAAW,EAAA,CAAA,CAC9B,CAEJ"}