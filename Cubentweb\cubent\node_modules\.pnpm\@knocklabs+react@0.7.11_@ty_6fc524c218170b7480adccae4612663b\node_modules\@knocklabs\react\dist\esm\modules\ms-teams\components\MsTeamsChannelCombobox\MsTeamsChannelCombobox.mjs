import e, { useState as d, useCallback as g } from "react";
import { useConnectedMsTeamsChannels as f } from "@knocklabs/react-core";
import { Icon as u, Lucide as C } from "@telegraph/icon";
import { Stack as r } from "@telegraph/layout";
import { Text as n } from "@telegraph/typography";
/* empty css               */
import { MsTeamsChannelInTeamCombobox as h } from "./MsTeamsChannelInTeamCombobox.mjs";
import _ from "./MsTeamsConnectionError.mjs";
import { MsTeamsTeamCombobox as E } from "./MsTeamsTeamCombobox.mjs";
/* empty css            */
const v = ({
  msTeamsChannelsRecipientObject: o,
  teamQueryOptions: s,
  channelQueryOptions: l
}) => {
  const [t, i] = d(null), {
    data: a
  } = f({
    msTeamsChannelsRecipientObject: o
  }), c = g((p) => (a == null ? void 0 : a.filter((m) => m.ms_teams_team_id === p && !!m.ms_teams_channel_id).length) ?? 0, [a]);
  return /* @__PURE__ */ e.createElement(r, { className: "tgph rtk-combobox__grid", gap: "3" }, /* @__PURE__ */ e.createElement(n, { color: "gray", size: "2", as: "div" }, "Team"), /* @__PURE__ */ e.createElement(E, { team: t, onTeamChange: i, getChannelCount: c, queryOptions: s }), /* @__PURE__ */ e.createElement(r, { alignItems: "center", gap: "3", minHeight: "8", style: {
    alignSelf: "start"
  } }, /* @__PURE__ */ e.createElement(u, { color: "gray", size: "1", icon: C.CornerDownRight, "aria-hidden": !0 }), /* @__PURE__ */ e.createElement(n, { color: "gray", size: "2", as: "div" }, "Channels")), /* @__PURE__ */ e.createElement(h, { teamId: t == null ? void 0 : t.id, msTeamsChannelsRecipientObject: o, queryOptions: l }), /* @__PURE__ */ e.createElement(_, null));
};
export {
  v as default
};
//# sourceMappingURL=MsTeamsChannelCombobox.mjs.map
