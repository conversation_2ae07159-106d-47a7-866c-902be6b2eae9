{"version": 3, "file": "NotificationIconButton.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationIconButton/NotificationIconButton.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { SyntheticEvent } from \"react\";\n\nimport { BellIcon } from \"../../../core/components/Icons\";\nimport { BadgeCountType, UnseenBadge } from \"../UnseenBadge\";\n\nimport \"./styles.css\";\n\nexport interface NotificationIconButtonProps {\n  // What value should we use to drive the badge count?\n  badgeCountType?: BadgeCountType;\n  onClick: (e: SyntheticEvent) => void;\n}\n\nexport const NotificationIconButton = React.forwardRef<\n  HTMLButtonElement,\n  NotificationIconButtonProps\n>(({ onClick, badgeCountType }, ref) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <button\n      className={`rnf-notification-icon-button rnf-notification-icon-button--${colorMode}`}\n      aria-label=\"Open notification feed\"\n      ref={ref}\n      onClick={onClick}\n    >\n      <BellIcon aria-hidden />\n      <UnseenBadge badgeCountType={badgeCountType} />\n    </button>\n  );\n});\n"], "names": ["NotificationIconButton", "React", "forwardRef", "onClick", "badgeCountType", "ref", "colorMode", "useKnockFeed", "BellIcon", "UnseenBadge"], "mappings": ";;;;;AAcaA,MAAAA,IAAyBC,EAAMC,WAG1C,CAAC;AAAA,EAAEC,SAAAA;AAAAA,EAASC,gBAAAA;AAAe,GAAGC,MAAQ;AAChC,QAAA;AAAA,IAAEC,WAAAA;AAAAA,MAAcC,EAAa;AAEnC,yCACG,UACC,EAAA,WAAW,8DAA8DD,CAAS,IAClF,cAAW,0BACX,KAAAD,GACA,SAAAF,KAEAF,gBAAAA,EAAA,cAACO,KAAS,eAAW,GAAA,CAAA,GACpBP,gBAAAA,EAAA,cAAAQ,GAAA,EAAY,gBAAAL,EAA+B,CAAA,CAC9C;AAEJ,CAAC;"}