'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@repo/design-system/components/ui/card';
import { Progress } from '@repo/design-system/components/ui/progress';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@repo/design-system/components/ui/tabs';
import { RefreshCw, AlertTriangle, CheckCircle, Zap, TrendingUp, Calendar, BarChart3 } from 'lucide-react';

interface UnitUsage {
  unitsUsed: number;
  unitsLimit: number;
  unitsRemaining: number;
  usagePercentage: number;
  subscriptionTier: string;
  lastResetDate?: string;
  recentUsage: Array<{
    modelId: string;
    unitsUsed: number;
    timestamp: string;
    metadata?: any;
  }>;
  modelBreakdown: Record<string, { units: number; requests: number }>;
  dailyUsage: Array<{
    date: string;
    units: number;
    requests: number;
  }>;
}

interface CubentUnitsTrackerProps {
  userId: string;
}

export const CubentUnitsTracker: React.FC<CubentUnitsTrackerProps> = ({ userId }) => {
  const [usage, setUsage] = useState<UnitUsage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsage = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/extension/units/usage', {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch usage: ${response.status}`);
      }

      const data = await response.json();
      setUsage(data);
    } catch (err) {
      console.error('Error fetching unit usage:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch usage');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsage();
  }, [userId]);

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUsageStatus = (percentage: number) => {
    if (percentage >= 90) return { icon: AlertTriangle, text: 'Critical', color: 'destructive' };
    if (percentage >= 75) return { icon: AlertTriangle, text: 'Warning', color: 'warning' };
    return { icon: CheckCircle, text: 'Good', color: 'default' };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Cubent Units Usage
          </CardTitle>
          <CardDescription>Loading your usage analytics...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cubent Units Usage</CardTitle>
          <CardDescription>Your AI usage analytics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchUsage} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!usage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cubent Units Usage</CardTitle>
          <CardDescription>No usage data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const status = getUsageStatus(usage.usagePercentage);
  const StatusIcon = status.icon;

  return (
    <div className="space-y-6">
      {/* Overview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Cubent Units Usage
            </div>
            <Button onClick={fetchUsage} variant="ghost" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardTitle>
          <CardDescription>Your AI usage analytics and limits</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Usage Overview */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Current Usage</span>
              <div className="flex items-center gap-2">
                <StatusIcon className={`h-4 w-4 ${getUsageColor(usage.usagePercentage)}`} />
                <Badge variant={status.color as any}>{status.text}</Badge>
              </div>
            </div>
            
            <Progress 
              value={usage.usagePercentage} 
              className="h-3"
            />
            
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{usage.unitsUsed.toFixed(2)} units used</span>
              <span>{usage.unitsRemaining.toFixed(2)} units remaining</span>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{usage.unitsUsed.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground">Units Used</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{usage.unitsLimit}</div>
              <div className="text-sm text-muted-foreground">Unit Limit</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{usage.usagePercentage.toFixed(1)}%</div>
              <div className="text-sm text-muted-foreground">Usage</div>
            </div>
          </div>

          {/* Subscription Info */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Plan:</span>
              <Badge variant="outline">{usage.subscriptionTier}</Badge>
            </div>
            {usage.lastResetDate && (
              <div className="text-sm text-muted-foreground">
                Last reset: {formatDate(usage.lastResetDate)}
              </div>
            )}
          </div>

          {/* Warning Messages */}
          {usage.usagePercentage >= 90 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-red-800">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-medium">Usage Critical</span>
              </div>
              <p className="text-sm text-red-700 mt-2">
                You've used {usage.usagePercentage.toFixed(1)}% of your Cubent units. 
                Consider upgrading your plan to continue using AI features.
              </p>
            </div>
          )}

          {usage.usagePercentage >= 75 && usage.usagePercentage < 90 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-medium">Usage Warning</span>
              </div>
              <p className="text-sm text-yellow-700 mt-2">
                You've used {usage.usagePercentage.toFixed(1)}% of your Cubent units.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Usage Analytics
          </CardTitle>
          <CardDescription>Detailed breakdown of your Cubent unit usage</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="daily" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="daily">Daily Usage</TabsTrigger>
              <TabsTrigger value="models">By Model</TabsTrigger>
              <TabsTrigger value="recent">Recent Activity</TabsTrigger>
            </TabsList>
            
            <TabsContent value="daily" className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Last 7 Days
                </h4>
                <div className="space-y-2">
                  {usage.dailyUsage.map((day, index) => (
                    <div key={day.date} className="flex items-center justify-between p-2 rounded border">
                      <span className="text-sm">{formatDate(day.date)}</span>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-muted-foreground">
                          {day.requests} requests
                        </span>
                        <span className="text-sm font-medium">
                          {day.units.toFixed(2)} units
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="models" className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Usage by Model
                </h4>
                <div className="space-y-2">
                  {Object.entries(usage.modelBreakdown)
                    .sort(([,a], [,b]) => b.units - a.units)
                    .map(([modelId, stats]) => (
                    <div key={modelId} className="flex items-center justify-between p-2 rounded border">
                      <span className="text-sm font-medium">{modelId}</span>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-muted-foreground">
                          {stats.requests} requests
                        </span>
                        <span className="text-sm font-medium">
                          {stats.units.toFixed(2)} units
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="recent" className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Recent Requests</h4>
                <div className="space-y-2">
                  {usage.recentUsage.slice(0, 10).map((request, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded border">
                      <div>
                        <span className="text-sm font-medium">{request.modelId}</span>
                        <div className="text-xs text-muted-foreground">
                          {formatDateTime(request.timestamp)}
                        </div>
                      </div>
                      <span className="text-sm font-medium">
                        {request.unitsUsed.toFixed(2)} units
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
