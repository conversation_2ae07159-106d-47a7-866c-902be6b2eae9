"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const x=require("@knocklabs/react-core"),A=require("clsx"),D=require("react"),b=require("../helpers.js");;/* empty css            */const C=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},i=C(A),a=C(D),S="card",o=({children:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card",t),...l},e);o.displayName="CardView.Root";const m=({children:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__message",t),...l},e);m.displayName="CardView.Content";const w=({children:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__header",t),...l},e);w.displayName="CardView.Header";const h=({headline:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__headline",t),...l},e);h.displayName="CardView.Headline";const f=({title:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__title",t),...l},e);f.displayName="CardView.Title";const y=({body:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__body",t),dangerouslySetInnerHTML:{__html:e},...l});y.displayName="CardView.Body";const _=({children:e,className:t,alt:l,...r})=>a.default.createElement("img",{className:i.default("knock-guide-card__img",t),alt:l||"",...r},e);_.displayName="CardView.Img";const g=({children:e,className:t,...l})=>a.default.createElement("div",{className:i.default("knock-guide-card__actions",t),...l},e);g.displayName="CardView.Actions";const k=({text:e,action:t,className:l,...r})=>a.default.createElement("button",{className:i.default("knock-guide-card__action",l),...r},e);k.displayName="CardView.PrimaryButton";const p=({text:e,action:t,className:l,...r})=>a.default.createElement("button",{className:i.default("knock-guide-card__action knock-guide-card__action--secondary",l),...r},e);p.displayName="CardView.SecondaryButton";const E=({className:e,...t})=>a.default.createElement("button",{className:i.default("knock-guide-card__close",e),...t},a.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},a.default.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},a.default.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),a.default.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));E.displayName="CardView.DismissButton";const N=({content:e,colorMode:t="light",onDismiss:l,onButtonClick:r,onImageClick:d})=>a.default.createElement(o,{"data-knock-color-mode":t},a.default.createElement(m,null,a.default.createElement(w,null,a.default.createElement(h,{headline:e.headline}),e.dismissible&&a.default.createElement(E,{onClick:l})),a.default.createElement(f,{title:e.title}),a.default.createElement(y,{body:e.body})),e.image&&a.default.createElement("a",{href:b.isValidHttpUrl(e.image.action)?e.image.action:void 0,target:"_blank"},a.default.createElement(_,{src:e.image.url,alt:e.image.alt,onClick:c=>{d&&d(c,e.image)}})),(e.primary_button||e.secondary_button)&&a.default.createElement(g,null,e.primary_button&&a.default.createElement(k,{text:e.primary_button.text,action:e.primary_button.action,onClick:c=>{if(r){const{text:u,action:n}=e.primary_button;r(c,{name:"primary_button",text:u,action:n})}}}),e.secondary_button&&a.default.createElement(p,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:c=>{if(r){const{text:u,action:n}=e.secondary_button;r(c,{name:"secondary_button",text:u,action:n})}}})));N.displayName="CardView.Default";const V=({guideKey:e,onButtonClick:t,onImageClick:l})=>{const{guide:r,step:d,colorMode:c}=x.useGuide({key:e,type:S});return a.default.useEffect(()=>{d&&d.markAsSeen()},[d]),!r||!d?null:a.default.createElement(N,{content:d.content,colorMode:c,onDismiss:()=>d.markAsArchived(),onButtonClick:(u,n)=>{const s={...n,type:"button_click"};return d.markAsInteracted({metadata:s}),t?t(u,{button:n,step:d,guide:r}):b.maybeNavigateToUrlWithDelay(n.action)},onImageClick:(u,n)=>{const s={...n,type:"image_click"};if(d.markAsInteracted({metadata:s}),l)return l(u,{image:n,step:d,guide:r})}})};V.displayName="Card";const v={};Object.assign(v,{Default:N,Root:o,Content:m,Title:f,Body:y,Img:_,Actions:g,PrimaryButton:k,SecondaryButton:p,DismissButton:E});exports.Card=V;exports.CardView=v;
//# sourceMappingURL=Card.js.map
