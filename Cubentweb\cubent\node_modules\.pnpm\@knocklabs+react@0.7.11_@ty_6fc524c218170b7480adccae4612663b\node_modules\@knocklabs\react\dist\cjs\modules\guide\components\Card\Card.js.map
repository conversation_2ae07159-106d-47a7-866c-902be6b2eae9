{"version": 3, "file": "Card.js", "sources": ["../../../../../../src/modules/guide/components/Card/Card.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"card\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"CardView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"CardView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"CardView.Header\";\n\nconst Headline: React.FC<\n  { headline: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ headline, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__headline\", className)} {...props}>\n      {headline}\n    </div>\n  );\n};\nHeadline.displayName = \"CardView.Headline\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"CardView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-card__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"CardView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-card__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"CardView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"CardView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-card__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"CardView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-card__action knock-guide-card__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"CardView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-card__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"CardView.DismissButton\";\n\ntype CardContent = {\n  headline: string;\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: CardContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Header>\n          <Headline headline={content.headline} />\n          {content.dismissible && <DismissButton onClick={onDismiss} />}\n        </Header>\n\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      {content.image && (\n        <a\n          href={\n            isValidHttpUrl(content.image.action)\n              ? content.image.action\n              : undefined\n          }\n          target=\"_blank\"\n        >\n          <Img\n            src={content.image.url}\n            alt={content.image.alt}\n            onClick={(e) => {\n              if (onImageClick) {\n                onImageClick(e, content.image!);\n              }\n            }}\n          />\n        </a>\n      )}\n      {(content.primary_button || content.secondary_button) && (\n        <Actions>\n          {content.primary_button && (\n            <PrimaryButton\n              text={content.primary_button.text}\n              action={content.primary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.primary_button!;\n                  onButtonClick(e, { name: \"primary_button\", text, action });\n                }\n              }}\n            />\n          )}\n          {content.secondary_button && (\n            <SecondaryButton\n              text={content.secondary_button.text}\n              action={content.secondary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.secondary_button!;\n                  onButtonClick(e, { name: \"secondary_button\", text, action });\n                }\n              }}\n            />\n          )}\n        </Actions>\n      )}\n    </Root>\n  );\n};\nDefaultView.displayName = \"CardView.Default\";\n\ntype CardProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Card: React.FC<CardProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as CardContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nCard.displayName = \"Card\";\n\nexport const CardView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Headline: typeof Headline;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(CardView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Header", "Headline", "headline", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Card", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "Card<PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": "gSAgBMA,EAAe,OAEfC,EAEFA,CAAC,CAAEC,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,mBAAoBH,CAAS,EAAG,GAAIC,CAAAA,EACtDF,CACH,EAGJD,EAAKM,YAAc,gBAEnB,MAAMC,EAEFA,CAAC,CAAEN,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,4BAA6BH,CAAS,EAAG,GAAIC,CAAAA,EAC/DF,CACH,EAGJM,EAAQD,YAAc,mBAEtB,MAAME,EAEFA,CAAC,CAAEP,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,2BAA4BH,CAAS,EAAG,GAAIC,CAAAA,EAC9DF,CACH,EAGJO,EAAOF,YAAc,kBAErB,MAAMG,EAEFA,CAAC,CAAEC,SAAAA,EAAUR,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,6BAA8BH,CAAS,EAAG,GAAIC,CAAAA,EAChEO,CACH,EAGJD,EAASH,YAAc,oBAEvB,MAAMK,EAEFA,CAAC,CAAEC,MAAAA,EAAOV,UAAAA,EAAW,GAAGC,CAAM,IAE9BC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,0BAA2BH,CAAS,EAAG,GAAIC,CAAAA,EAC7DS,CACH,EAGJD,EAAML,YAAc,iBAEpB,MAAMO,EAAwEA,CAAC,CAC7EC,KAAAA,EACAZ,UAAAA,EACA,GAAGC,CACL,4BAEK,MACC,CAAA,UAAWE,EAAAA,QAAK,yBAA0BH,CAAS,EACnD,wBAAyB,CAAEa,OAAQD,CAAAA,EAC/BX,GAAAA,CACJ,CAAA,EAGNU,EAAKP,YAAc,gBAEnB,MAAMU,EAEFA,CAAC,CAAEf,SAAAA,EAAUC,UAAAA,EAAWe,IAAAA,EAAK,GAAGd,CAAM,IAErCC,EAAAA,QAAA,cAAA,MAAA,CACC,UAAWC,EAAAA,QAAK,wBAAyBH,CAAS,EAClD,IAAKe,GAAO,GACRd,GAAAA,CAAAA,EAEHF,CACH,EAGJe,EAAIV,YAAc,eAElB,MAAMY,EAEFA,CAAC,CAAEjB,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,4BAA6BH,CAAS,EAAG,GAAIC,CAAAA,EAC/DF,CACH,EAGJiB,EAAQZ,YAAc,mBAEtB,MAAMa,EAEFA,CAAC,CAAEC,KAAAA,EAAMC,OAAAA,EAAQnB,UAAAA,EAAW,GAAGC,CAAM,IAErCC,UAAA,cAAC,UAAO,UAAWC,EAAAA,QAAK,2BAA4BH,CAAS,EAAG,GAAIC,CAAAA,EACjEiB,CACH,EAGJD,EAAcb,YAAc,yBAE5B,MAAMgB,EAEFA,CAAC,CAAEF,KAAAA,EAAMC,OAAAA,EAAQnB,UAAAA,EAAW,GAAGC,CAAM,IAErCC,UAAA,cAAC,UACC,UAAWC,EAAAA,QACT,+DACAH,CACF,EACA,GAAIC,CAAAA,EAEHiB,CACH,EAGJE,EAAgBhB,YAAc,2BAE9B,MAAMiB,EAAiEA,CAAC,CACtErB,UAAAA,EACA,GAAGC,CACL,4BAEK,SAAO,CAAA,UAAWE,UAAK,0BAA2BH,CAAS,EAAG,GAAIC,GACjEC,EAAAA,QAAA,cAAC,OACC,MAAM,6BACN,MAAM,KACN,OAAO,KACP,KAAK,QAEJA,EAAAA,QAAA,cAAA,IAAA,CAAE,KAAK,UAAU,SAAS,UAAU,SAAS,WAC3CA,EAAA,QAAA,cAAA,OAAA,CAAK,EAAE,sFAAA,CAAsF,EAC7FA,EAAAA,QAAA,cAAA,OAAA,CAAK,EAAE,sFAAqF,CAC/F,CACF,CACF,EAGJmB,EAAcjB,YAAc,yBAY5B,MAAMkB,EAMDA,CAAC,CACJC,QAAAA,EACAC,UAAAA,EAAY,QACZC,UAAAA,EACAC,cAAAA,EACAC,aAAAA,CACF,IAEKzB,EAAAA,QAAA,cAAAJ,EAAA,CAAK,wBAAuB0B,CAAAA,EAC1BtB,EAAAA,QAAA,cAAAG,EAAA,KACEH,EAAAA,QAAA,cAAAI,EAAA,KACEJ,UAAA,cAAAK,EAAA,CAAS,SAAUgB,EAAQf,QAAS,CAAA,EACpCe,EAAQK,aAAgB1B,EAAAA,QAAA,cAAAmB,EAAA,CAAc,QAASI,CAAAA,CAAa,CAC/D,EAECvB,EAAA,QAAA,cAAAO,EAAA,CAAM,MAAOc,EAAQb,MAAM,0BAC3BC,EAAK,CAAA,KAAMY,EAAQX,KAAK,CAC3B,EACCW,EAAQM,OACN3B,EAAA,QAAA,cAAA,IAAA,CACC,KACE4B,EAAAA,eAAeP,EAAQM,MAAMV,MAAM,EAC/BI,EAAQM,MAAMV,OACdY,OAEN,OAAO,QAAA,EAEN7B,EAAAA,QAAA,cAAAY,EAAA,CACC,IAAKS,EAAQM,MAAMG,IACnB,IAAKT,EAAQM,MAAMd,IACnB,QAAgBkB,GAAA,CACVN,GACWM,EAAAA,EAAGV,EAAQM,KAAM,CAElC,CAAA,CAAE,CAEN,GAEAN,EAAQW,gBAAkBX,EAAQY,mBACjCjC,EAAA,QAAA,cAAAc,EAAA,KACEO,EAAQW,wCACNjB,EACC,CAAA,KAAMM,EAAQW,eAAehB,KAC7B,OAAQK,EAAQW,eAAef,OAC/B,QAAgBc,GAAA,CACd,GAAIP,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQW,eACjCR,EAAcO,EAAG,CAAEG,KAAM,iBAAkBlB,KAAAA,EAAMC,OAAAA,CAAAA,CAAQ,CAAA,CAC3D,CAGL,CAAA,EACAI,EAAQY,0CACNf,EACC,CAAA,KAAMG,EAAQY,iBAAiBjB,KAC/B,OAAQK,EAAQY,iBAAiBhB,OACjC,QAAgBc,GAAA,CACd,GAAIP,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQY,iBACjCT,EAAcO,EAAG,CAAEG,KAAM,mBAAoBlB,KAAAA,EAAMC,OAAAA,CAAAA,CAAQ,CAAA,CAE/D,CAAA,CAEH,CACH,CAEJ,EAGJG,EAAYlB,YAAc,mBAQnB,MAAMiC,EAA4BA,CAAC,CACxCC,SAAAA,EACAZ,cAAAA,EACAC,aAAAA,CACF,IAAM,CACE,KAAA,CAAEY,MAAAA,EAAOC,KAAAA,EAAMhB,UAAAA,GAAciB,WAAS,CAC1CC,IAAKJ,EACLK,KAAM9C,CAAAA,CACP,EAMD,OAJAK,EAAAA,QAAM0C,UAAU,IAAM,CAChBJ,KAAWK,WAAW,CAAA,EACzB,CAACL,CAAI,CAAC,EAEL,CAACD,GAAS,CAACC,EAAa,KAGzBtC,EAAAA,QAAA,cAAAoB,EAAA,CACC,QAASkB,EAAKjB,QACd,UAAAC,EACA,UAAW,IAAMgB,EAAKM,eAAe,EACrC,cAAe,CAACb,EAAGc,IAAW,CAC5B,MAAMC,EAAW,CAAE,GAAGD,EAAQJ,KAAM,cAAe,EACnDH,OAAAA,EAAKS,iBAAiB,CAAED,SAAAA,CAAAA,CAAU,EAE3BtB,EACHA,EAAcO,EAAG,CAAEc,OAAAA,EAAQP,KAAAA,EAAMD,MAAAA,CAAAA,CAAO,EACxCW,EAAAA,4BAA4BH,EAAO5B,MAAM,CAAA,EAE/C,aAAc,CAACc,EAAGJ,IAAU,CAC1B,MAAMmB,EAAW,CAAE,GAAGnB,EAAOc,KAAM,aAAc,EAGjD,GAFAH,EAAKS,iBAAiB,CAAED,SAAAA,CAAAA,CAAU,EAE9BrB,EACF,OAAOA,EAAaM,EAAG,CAAEJ,MAAAA,EAAOW,KAAAA,EAAMD,MAAAA,CAAAA,CAAO,CAC/C,EAEF,CAEN,EACAF,EAAKjC,YAAc,OAEZ,MAAM+C,EAAW,CAAA,EAcxBC,OAAOC,OAAOF,EAAU,CACtBG,QAAShC,EACTxB,KAAAA,EACAO,QAAAA,EACAI,MAAAA,EACAE,KAAAA,EACAG,IAAAA,EACAE,QAAAA,EACAC,cAAAA,EACAG,gBAAAA,EACAC,cAAAA,CACF,CAAC"}