"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("react"),r=require("@knocklabs/react-core");;/* empty css            */const d=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=d(n),c=()=>{const{colorMode:e}=r.useKnockFeed(),{t:a}=r.useTranslations();return t.default.createElement("div",{className:`rnf-empty-feed rnf-empty-feed--${e}`},t.default.createElement("div",{className:"rnf-empty-feed__inner"},t.default.createElement("h2",{className:"rnf-empty-feed__header"},a("emptyFeedTitle")),t.default.createElement("p",{className:"rnf-empty-feed__body"},a("emptyFeedBody"))))};exports.EmptyFeed=c;
//# sourceMappingURL=EmptyFeed.js.map
