{"version": 3, "file": "NotificationCell.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/NotificationCell.tsx"], "sourcesContent": ["import {\n  <PERSON>Button,\n  ButtonSetContentBlock,\n  ContentBlock,\n  FeedItem,\n  MarkdownContentBlock,\n  TextContentBlock,\n} from \"@knocklabs/client\";\nimport {\n  formatTimestamp,\n  renderNodeOrFallback,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport React, { ReactNode, useMemo } from \"react\";\n\nimport { Button, ButtonGroup } from \"../../../core\";\n\nimport { ArchiveButton } from \"./ArchiveButton\";\nimport { Avatar } from \"./Avatar\";\nimport \"./styles.css\";\n\nexport interface NotificationCellProps {\n  item: FeedItem;\n  // Invoked when the outer container is clicked\n  onItemClick?: (item: FeedItem) => void;\n  // Invoked when a button in the notification cell is clicked\n  onButtonClick?: (item: FeedItem, action: ActionButton) => void;\n  avatar?: ReactNode;\n  children?: ReactNode;\n  archiveButton?: ReactNode;\n}\n\ntype BlockByName = {\n  [name: string]: ContentBlock;\n};\n\nfunction maybeNavigateToUrlWithDelay(url?: string) {\n  if (url && url !== \"\") {\n    setTimeout(() => window.location.assign(url), 200);\n  }\n}\n\nexport const NotificationCell = React.forwardRef<\n  HTMLDivElement,\n  NotificationCellProps\n>(\n  (\n    { item, onItemClick, onButtonClick, avatar, children, archiveButton },\n    ref,\n  ) => {\n    const { feedClient, colorMode } = useKnockFeed();\n    const { locale } = useTranslations();\n\n    const blocksByName: BlockByName = useMemo(() => {\n      return item.blocks.reduce((acc, block) => {\n        return { ...acc, [block.name]: block };\n      }, {});\n    }, [item]);\n\n    const actionUrl = (blocksByName.action_url as TextContentBlock)?.rendered;\n    const buttonSet = blocksByName.actions as ButtonSetContentBlock;\n\n    const onContainerClickHandler = React.useCallback(() => {\n      // Mark as interacted + read once we click the item\n      feedClient.markAsInteracted(item, {\n        type: \"cell_click\",\n        action: actionUrl,\n      });\n\n      if (onItemClick) return onItemClick(item);\n\n      return maybeNavigateToUrlWithDelay(actionUrl);\n    }, [item, actionUrl, onItemClick, feedClient]);\n\n    const onButtonClickHandler = React.useCallback(\n      (_e: React.MouseEvent, button: ActionButton) => {\n        // Record the interaction with the metadata for the button that was clicked\n        feedClient.markAsInteracted(item, {\n          type: \"button_click\",\n          name: button.name,\n          label: button.label,\n          action: button.action,\n        });\n\n        if (onButtonClick) return onButtonClick(item, button);\n\n        return maybeNavigateToUrlWithDelay(button.action);\n      },\n      [onButtonClick, feedClient, item],\n    );\n\n    const onKeyDown = React.useCallback(\n      (ev: React.KeyboardEvent<HTMLDivElement>) => {\n        switch (ev.key) {\n          case \"Enter\": {\n            ev.stopPropagation();\n            onContainerClickHandler();\n            break;\n          }\n          default:\n            break;\n        }\n      },\n      [onContainerClickHandler],\n    );\n\n    const actor = item.actors[0];\n\n    return (\n      // eslint-disable-next-line jsx-a11y/no-static-element-interactions\n      <div\n        ref={ref}\n        className={`rnf-notification-cell rnf-notification-cell--${colorMode}`}\n        onClick={onContainerClickHandler}\n        onKeyDown={onKeyDown}\n        // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex\n        tabIndex={0}\n      >\n        <div className=\"rnf-notification-cell__inner\">\n          {!item.read_at && (\n            <div className=\"rnf-notification-cell__unread-dot\" />\n          )}\n\n          {renderNodeOrFallback(\n            avatar,\n            actor && \"name\" in actor && actor.name && (\n              <Avatar name={actor.name} src={actor.avatar} />\n            ),\n          )}\n\n          <div className=\"rnf-notification-cell__content-outer\">\n            {blocksByName.body && (\n              <div\n                className=\"rnf-notification-cell__content\"\n                dangerouslySetInnerHTML={{\n                  __html: (blocksByName.body as MarkdownContentBlock).rendered,\n                }}\n              />\n            )}\n\n            {buttonSet && (\n              <div className=\"rnf-notification-cell__button-group\">\n                <ButtonGroup>\n                  {buttonSet.buttons.map((button, i) => (\n                    <Button\n                      variant={i === 0 ? \"primary\" : \"secondary\"}\n                      key={button.name}\n                      onClick={(e) => onButtonClickHandler(e, button)}\n                    >\n                      {button.label}\n                    </Button>\n                  ))}\n                </ButtonGroup>\n              </div>\n            )}\n\n            {children && (\n              <div className=\"rnf-notification-cell__child-content\">\n                {children}\n              </div>\n            )}\n\n            <span className=\"rnf-notification-cell__timestamp\">\n              {formatTimestamp(item.inserted_at, { locale })}\n            </span>\n          </div>\n\n          {renderNodeOrFallback(archiveButton, <ArchiveButton item={item} />)}\n        </div>\n      </div>\n    );\n  },\n);\n"], "names": ["maybeNavigateToUrlWithDelay", "url", "setTimeout", "window", "location", "assign", "NotificationCell", "React", "forwardRef", "item", "onItemClick", "onButtonClick", "avatar", "children", "archiveButton", "ref", "feedClient", "colorMode", "useKnockFeed", "locale", "useTranslations", "blocksByName", "useMemo", "blocks", "reduce", "acc", "block", "name", "actionUrl", "action_url", "rendered", "buttonSet", "actions", "onContainerClickHandler", "useCallback", "markAsInteracted", "type", "action", "onButtonClickHandler", "_e", "button", "label", "onKeyDown", "ev", "key", "stopPropagation", "actor", "actors", "read_at", "renderNodeOrFallback", "Avatar", "body", "__html", "ButtonGroup", "buttons", "map", "i", "<PERSON><PERSON>", "e", "formatTimestamp", "inserted_at", "ArchiveButton"], "mappings": ";;;;;;;;AAqCA,SAASA,EAA4BC,GAAc;AAC7CA,EAAAA,KAAOA,MAAQ,MACjBC,WAAW,MAAMC,OAAOC,SAASC,OAAOJ,CAAG,GAAG,GAAG;AAErD;AAEaK,MAAAA,IAAmBC,EAAMC,WAIpC,CACE;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,aAAAA;AAAAA,EAAaC,eAAAA;AAAAA,EAAeC,QAAAA;AAAAA,EAAQC,UAAAA;AAAAA,EAAUC,eAAAA;AAAc,GACpEC,MACG;;AACG,QAAA;AAAA,IAAEC,YAAAA;AAAAA,IAAYC,WAAAA;AAAAA,MAAcC,EAAa,GACzC;AAAA,IAAEC,QAAAA;AAAAA,MAAWC,EAAgB,GAE7BC,IAA4BC,EAAQ,MACjCb,EAAKc,OAAOC,OAAO,CAACC,GAAKC,OACvB;AAAA,IAAE,GAAGD;AAAAA,IAAK,CAACC,EAAMC,IAAI,GAAGD;AAAAA,EAAM,IACpC,EAAE,GACJ,CAACjB,CAAI,CAAC,GAEHmB,KAAaP,IAAAA,EAAaQ,eAAbR,gBAAAA,EAA8CS,UAC3DC,IAAYV,EAAaW,SAEzBC,IAA0B1B,EAAM2B,YAAY,OAEhDlB,EAAWmB,iBAAiB1B,GAAM;AAAA,IAChC2B,MAAM;AAAA,IACNC,QAAQT;AAAAA,EAAAA,CACT,GAEGlB,IAAoBA,EAAYD,CAAI,IAEjCT,EAA4B4B,CAAS,IAC3C,CAACnB,GAAMmB,GAAWlB,GAAaM,CAAU,CAAC,GAEvCsB,IAAuB/B,EAAM2B,YACjC,CAACK,GAAsBC,OAErBxB,EAAWmB,iBAAiB1B,GAAM;AAAA,IAChC2B,MAAM;AAAA,IACNT,MAAMa,EAAOb;AAAAA,IACbc,OAAOD,EAAOC;AAAAA,IACdJ,QAAQG,EAAOH;AAAAA,EAAAA,CAChB,GAEG1B,IAAsBA,EAAcF,GAAM+B,CAAM,IAE7CxC,EAA4BwC,EAAOH,MAAM,IAElD,CAAC1B,GAAeK,GAAYP,CAAI,CAClC,GAEMiC,IAAYnC,EAAM2B,YACtB,CAACS,MAA4C;AAC3C,YAAQA,EAAGC,KAAG;AAAA,MACZ,KAAK,SAAS;AACZD,QAAAA,EAAGE,gBAAgB,GACKZ,EAAA;AACxB;AAAA,MAAA;AAAA,IAGA;AAAA,EACJ,GAEF,CAACA,CAAuB,CAC1B,GAEMa,IAAQrC,EAAKsC,OAAO,CAAC;AAE3B;AAAA;AAAA,IAEExC,gBAAAA,EAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,KAAAQ;AAAA,QACA,WAAW,gDAAgDE,CAAS;AAAA,QACpE,SAASgB;AAAAA,QACT,WAAAS;AAAA,QAEA,UAAU;AAAA,MAAA;AAAA,sCAET,OAAI,EAAA,WAAU,kCACZ,CAACjC,EAAKuC,WACLzC,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,qCAChB,GAEA0C,EACCrC,GACAkC,KAAS,UAAUA,KAASA,EAAMnB,QAC/BpB,gBAAAA,EAAA,cAAA2C,GAAA,EAAO,MAAMJ,EAAMnB,MAAM,KAAKmB,EAAMlC,SAEzC,GAEAL,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,0CACZc,EAAa8B,wCACX,OACC,EAAA,WAAU,kCACV,yBAAyB;AAAA,QACvBC,QAAS/B,EAAa8B,KAA8BrB;AAAAA,MACtD,EAAA,CAEH,GAEAC,KACCxB,gBAAAA,EAAA,cAAC,SAAI,WAAU,sCAAA,GACZA,gBAAAA,EAAA,cAAA8C,GAAA,MACEtB,EAAUuB,QAAQC,IAAI,CAACf,GAAQgB,MAC9BjD,gBAAAA,EAAA,cAACkD,GACC,EAAA,SAASD,MAAM,IAAI,YAAY,aAC/B,KAAKhB,EAAOb,MACZ,SAAU+B,CAAMpB,MAAAA,EAAqBoB,GAAGlB,CAAM,EAAA,GAE7CA,EAAOC,KACV,CACD,CACH,CACF,GAGD5B,qCACE,OAAI,EAAA,WAAU,uCACZA,GAAAA,CACH,GAGFN,gBAAAA,EAAA,cAAC,UAAK,WAAU,mCAAA,GACboD,EAAgBlD,EAAKmD,aAAa;AAAA,QAAEzC,QAAAA;AAAAA,MAAAA,CAAQ,CAC/C,CACF,GAEC8B,EAAqBnC,GAAgBP,gBAAAA,EAAA,cAAAsD,GAAA,EAAc,MAAApD,IAAc,CACpE;AAAA,IAAA;AAAA;AAGN,CACF;"}