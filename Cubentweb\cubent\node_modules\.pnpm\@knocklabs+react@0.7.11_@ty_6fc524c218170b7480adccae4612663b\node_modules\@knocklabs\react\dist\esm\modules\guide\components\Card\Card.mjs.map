{"version": 3, "file": "Card.mjs", "sources": ["../../../../../../src/modules/guide/components/Card/Card.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"card\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"CardView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"CardView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"CardView.Header\";\n\nconst Headline: React.FC<\n  { headline: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ headline, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__headline\", className)} {...props}>\n      {headline}\n    </div>\n  );\n};\nHeadline.displayName = \"CardView.Headline\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"CardView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-card__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"CardView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-card__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"CardView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-card__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"CardView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-card__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"CardView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-card__action knock-guide-card__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"CardView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-card__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"CardView.DismissButton\";\n\ntype CardContent = {\n  headline: string;\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: CardContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Header>\n          <Headline headline={content.headline} />\n          {content.dismissible && <DismissButton onClick={onDismiss} />}\n        </Header>\n\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      {content.image && (\n        <a\n          href={\n            isValidHttpUrl(content.image.action)\n              ? content.image.action\n              : undefined\n          }\n          target=\"_blank\"\n        >\n          <Img\n            src={content.image.url}\n            alt={content.image.alt}\n            onClick={(e) => {\n              if (onImageClick) {\n                onImageClick(e, content.image!);\n              }\n            }}\n          />\n        </a>\n      )}\n      {(content.primary_button || content.secondary_button) && (\n        <Actions>\n          {content.primary_button && (\n            <PrimaryButton\n              text={content.primary_button.text}\n              action={content.primary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.primary_button!;\n                  onButtonClick(e, { name: \"primary_button\", text, action });\n                }\n              }}\n            />\n          )}\n          {content.secondary_button && (\n            <SecondaryButton\n              text={content.secondary_button.text}\n              action={content.secondary_button.action}\n              onClick={(e) => {\n                if (onButtonClick) {\n                  const { text, action } = content.secondary_button!;\n                  onButtonClick(e, { name: \"secondary_button\", text, action });\n                }\n              }}\n            />\n          )}\n        </Actions>\n      )}\n    </Root>\n  );\n};\nDefaultView.displayName = \"CardView.Default\";\n\ntype CardProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Card: React.FC<CardProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as CardContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nCard.displayName = \"Card\";\n\nexport const CardView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Headline: typeof Headline;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(CardView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Header", "Headline", "headline", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Card", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "Card<PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;AAgBA,MAAMA,IAAe,QAEfC,IAEFA,CAAC;AAAA,EAAEC,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,oBAAoBH,CAAS,GAAG,GAAIC,EAAAA,GACtDF,CACH;AAGJD,EAAKM,cAAc;AAEnB,MAAMC,IAEFA,CAAC;AAAA,EAAEN,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,6BAA6BH,CAAS,GAAG,GAAIC,EAAAA,GAC/DF,CACH;AAGJM,EAAQD,cAAc;AAEtB,MAAME,IAEFA,CAAC;AAAA,EAAEP,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,4BAA4BH,CAAS,GAAG,GAAIC,EAAAA,GAC9DF,CACH;AAGJO,EAAOF,cAAc;AAErB,MAAMG,IAEFA,CAAC;AAAA,EAAEC,UAAAA;AAAAA,EAAUR,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,8BAA8BH,CAAS,GAAG,GAAIC,EAAAA,GAChEO,CACH;AAGJD,EAASH,cAAc;AAEvB,MAAMK,IAEFA,CAAC;AAAA,EAAEC,OAAAA;AAAAA,EAAOV,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAE9BC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,2BAA2BH,CAAS,GAAG,GAAIC,EAAAA,GAC7DS,CACH;AAGJD,EAAML,cAAc;AAEpB,MAAMO,IAAwEA,CAAC;AAAA,EAC7EC,MAAAA;AAAAA,EACAZ,WAAAA;AAAAA,EACA,GAAGC;AACL,sCAEK,OACC,EAAA,WAAWE,EAAK,0BAA0BH,CAAS,GACnD,yBAAyB;AAAA,EAAEa,QAAQD;AAAAA,GAC/BX,GAAAA,EACJ,CAAA;AAGNU,EAAKP,cAAc;AAEnB,MAAMU,IAEFA,CAAC;AAAA,EAAEf,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAWe,KAAAA;AAAAA,EAAK,GAAGd;AAAM,MAErCC,gBAAAA,EAAA,cAAA,OAAA,EACC,WAAWC,EAAK,yBAAyBH,CAAS,GAClD,KAAKe,KAAO,IACRd,GAAAA,EAAAA,GAEHF,CACH;AAGJe,EAAIV,cAAc;AAElB,MAAMY,IAEFA,CAAC;AAAA,EAAEjB,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,6BAA6BH,CAAS,GAAG,GAAIC,EAAAA,GAC/DF,CACH;AAGJiB,EAAQZ,cAAc;AAEtB,MAAMa,IAEFA,CAAC;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQnB,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YAAO,WAAWC,EAAK,4BAA4BH,CAAS,GAAG,GAAIC,EAAAA,GACjEiB,CACH;AAGJD,EAAcb,cAAc;AAE5B,MAAMgB,IAEFA,CAAC;AAAA,EAAEF,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQnB,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YACC,WAAWC,EACT,gEACAH,CACF,GACA,GAAIC,EAAAA,GAEHiB,CACH;AAGJE,EAAgBhB,cAAc;AAE9B,MAAMiB,IAAiEA,CAAC;AAAA,EACtErB,WAAAA;AAAAA,EACA,GAAGC;AACL,sCAEK,UAAO,EAAA,WAAWE,EAAK,2BAA2BH,CAAS,GAAG,GAAIC,KACjEC,gBAAAA,EAAA,cAAC,SACC,OAAM,8BACN,OAAM,MACN,QAAO,MACP,MAAK,UAEJA,gBAAAA,EAAA,cAAA,KAAA,EAAE,MAAK,WAAU,UAAS,WAAU,UAAS,aAC3CA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,uFAAA,CAAsF,GAC7FA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,uFAAqF,CAC/F,CACF,CACF;AAGJmB,EAAcjB,cAAc;AAY5B,MAAMkB,IAMDA,CAAC;AAAA,EACJC,SAAAA;AAAAA,EACAC,WAAAA,IAAY;AAAA,EACZC,WAAAA;AAAAA,EACAC,eAAAA;AAAAA,EACAC,cAAAA;AACF,MAEKzB,gBAAAA,EAAA,cAAAJ,GAAA,EAAK,yBAAuB0B,EAAAA,GAC1BtB,gBAAAA,EAAA,cAAAG,GAAA,MACEH,gBAAAA,EAAA,cAAAI,GAAA,MACEJ,gBAAAA,EAAA,cAAAK,GAAA,EAAS,UAAUgB,EAAQf,SAAS,CAAA,GACpCe,EAAQK,eAAgB1B,gBAAAA,EAAA,cAAAmB,GAAA,EAAc,SAASI,EAAAA,CAAa,CAC/D,GAECvB,gBAAAA,EAAA,cAAAO,GAAA,EAAM,OAAOc,EAAQb,OAAM,mCAC3BC,GAAK,EAAA,MAAMY,EAAQX,MAAK,CAC3B,GACCW,EAAQM,SACN3B,gBAAAA,EAAA,cAAA,KAAA,EACC,MACE4B,EAAeP,EAAQM,MAAMV,MAAM,IAC/BI,EAAQM,MAAMV,SACdY,QAEN,QAAO,SAAA,GAEN7B,gBAAAA,EAAA,cAAAY,GAAA,EACC,KAAKS,EAAQM,MAAMG,KACnB,KAAKT,EAAQM,MAAMd,KACnB,SAAUkB,CAAMA,MAAA;AACd,EAAIN,KACWM,EAAAA,GAAGV,EAAQM,KAAM;AAElC,EAAA,CAAE,CAEN,IAEAN,EAAQW,kBAAkBX,EAAQY,qBACjCjC,gBAAAA,EAAA,cAAAc,GAAA,MACEO,EAAQW,kDACNjB,GACC,EAAA,MAAMM,EAAQW,eAAehB,MAC7B,QAAQK,EAAQW,eAAef,QAC/B,SAAUc,CAAMA,MAAA;AACd,MAAIP,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQW;AACjCR,IAAAA,EAAcO,GAAG;AAAA,MAAEG,MAAM;AAAA,MAAkBlB,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAC3D,EAGL,CAAA,GACAI,EAAQY,oDACNf,GACC,EAAA,MAAMG,EAAQY,iBAAiBjB,MAC/B,QAAQK,EAAQY,iBAAiBhB,QACjC,SAAUc,CAAMA,MAAA;AACd,MAAIP,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQY;AACjCT,IAAAA,EAAcO,GAAG;AAAA,MAAEG,MAAM;AAAA,MAAoBlB,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAE/D,EAAA,CAEH,CACH,CAEJ;AAGJG,EAAYlB,cAAc;AAQnB,MAAMiC,IAA4BA,CAAC;AAAA,EACxCC,UAAAA;AAAAA,EACAZ,eAAAA;AAAAA,EACAC,cAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEY,OAAAA;AAAAA,IAAOC,MAAAA;AAAAA,IAAMhB,WAAAA;AAAAA,MAAciB,EAAS;AAAA,IAC1CC,KAAKJ;AAAAA,IACLK,MAAM9C;AAAAA,EAAAA,CACP;AAMD,SAJAK,EAAM0C,UAAU,MAAM;AAChBJ,IAAAA,OAAWK,WAAW;AAAA,EAAA,GACzB,CAACL,CAAI,CAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzBtC,gBAAAA,EAAA,cAAAoB,GAAA,EACC,SAASkB,EAAKjB,SACd,WAAAC,GACA,WAAW,MAAMgB,EAAKM,eAAe,GACrC,eAAe,CAACb,GAAGc,MAAW;AAC5B,UAAMC,IAAW;AAAA,MAAE,GAAGD;AAAAA,MAAQJ,MAAM;AAAA,IAAe;AACnDH,WAAAA,EAAKS,iBAAiB;AAAA,MAAED,UAAAA;AAAAA,IAAAA,CAAU,GAE3BtB,IACHA,EAAcO,GAAG;AAAA,MAAEc,QAAAA;AAAAA,MAAQP,MAAAA;AAAAA,MAAMD,OAAAA;AAAAA,IAAAA,CAAO,IACxCW,EAA4BH,EAAO5B,MAAM;AAAA,EAAA,GAE/C,cAAc,CAACc,GAAGJ,MAAU;AAC1B,UAAMmB,IAAW;AAAA,MAAE,GAAGnB;AAAAA,MAAOc,MAAM;AAAA,IAAc;AAGjD,QAFAH,EAAKS,iBAAiB;AAAA,MAAED,UAAAA;AAAAA,IAAAA,CAAU,GAE9BrB;AACF,aAAOA,EAAaM,GAAG;AAAA,QAAEJ,OAAAA;AAAAA,QAAOW,MAAAA;AAAAA,QAAMD,OAAAA;AAAAA,MAAAA,CAAO;AAAA,EAC/C,GAEF;AAEN;AACAF,EAAKjC,cAAc;AAEZ,MAAM+C,IAAW,CAAA;AAcxBC,OAAOC,OAAOF,GAAU;AAAA,EACtBG,SAAShC;AAAAA,EACTxB,MAAAA;AAAAA,EACAO,SAAAA;AAAAA,EACAI,OAAAA;AAAAA,EACAE,MAAAA;AAAAA,EACAG,KAAAA;AAAAA,EACAE,SAAAA;AAAAA,EACAC,eAAAA;AAAAA,EACAG,iBAAAA;AAAAA,EACAC,eAAAA;AACF,CAAC;"}