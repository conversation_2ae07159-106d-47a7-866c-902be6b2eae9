"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validator = exports.deepCompareStrict = void 0;
var json_schema_1 = require("@cfworker/json-schema");
Object.defineProperty(exports, "deepCompareStrict", { enumerable: true, get: function () { return json_schema_1.deepCompareStrict; } });
Object.defineProperty(exports, "Validator", { enumerable: true, get: function () { return json_schema_1.Validator; } });
