/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l4 2", key: "mmk7yg" }],
  ["path", { d: "M16 21.16a10 10 0 1 1 5-13.516", key: "cxo92l" }],
  ["path", { d: "M20 11.5v6", key: "2ei3xq" }],
  ["path", { d: "M20 21.5h.01", key: "1r2dzp" }]
];
const ClockAlert = createLucideIcon("clock-alert", __iconNode);

export { __iconNode, ClockAlert as default };
//# sourceMappingURL=clock-alert.js.map
