{"version": 3, "file": "NotificationFeedHeader.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/NotificationFeedHeader.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { FilterStatus, useTranslations } from \"@knocklabs/react-core\";\nimport React, { SetStateAction } from \"react\";\n\nimport { Dropdown } from \"./Dropdown\";\nimport { MarkAsRead } from \"./MarkAsRead\";\n\nexport type NotificationFeedHeaderProps = {\n  filterStatus: FilterStatus;\n  setFilterStatus: React.Dispatch<SetStateAction<FilterStatus>>;\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nconst OrderedFilterStatuses = [\n  FilterStatus.All,\n  FilterStatus.Unread,\n  FilterStatus.Read,\n];\n\nexport const NotificationFeedHeader: React.FC<NotificationFeedHeaderProps> = ({\n  onMarkAllAsReadClick,\n  filterStatus,\n  setFilterStatus,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <header className=\"rnf-notification-feed__header\">\n      <div className=\"rnf-notification-feed__selector\">\n        <span className=\"rnf-notification-feed__type\">\n          {t(\"notifications\")}\n        </span>\n        <Dropdown\n          value={filterStatus}\n          onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}\n        >\n          {OrderedFilterStatuses.map((filterStatus) => (\n            <option key={filterStatus} value={filterStatus}>\n              {t(filterStatus)}\n            </option>\n          ))}\n        </Dropdown>\n      </div>\n      <MarkAsRead onClick={onMarkAllAsReadClick} />\n    </header>\n  );\n};\n"], "names": ["OrderedFilterStatuses", "FilterStatus", "All", "Unread", "Read", "NotificationFeedHeader", "onMarkAllAsReadClick", "filterStatus", "setFilterStatus", "t", "useTranslations", "React", "Dropdown", "e", "target", "value", "map", "MarkAsRead"], "mappings": ";;;;AAaA,MAAMA,IAAwB,CAC5BC,EAAaC,KACbD,EAAaE,QACbF,EAAaG,IAAI,GAGNC,IAAgEA,CAAC;AAAA,EAC5EC,sBAAAA;AAAAA,EACAC,cAAAA;AAAAA,EACAC,iBAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB;AAG5B,SAAAC,gBAAAA,EAAA,cAAC,YAAO,WAAU,gCAAA,mCACf,OAAI,EAAA,WAAU,kCACb,GAAAA,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,iCACbF,EAAE,eAAe,CACpB,GACAE,gBAAAA,EAAA,cAACC,KACC,OAAOL,GACP,UAAWM,CAAAA,MAAML,EAAgBK,EAAEC,OAAOC,KAAqB,EAAA,GAE9Df,EAAsBgB,IAAKT,CAAAA,MACzBI,gBAAAA,EAAA,cAAA,UAAA,EAAO,KAAKJ,GAAc,OAAOA,EAAAA,GAC/BE,EAAEF,CAAY,CACjB,CACD,CACH,CACF,GACCI,gBAAAA,EAAA,cAAAM,GAAA,EAAW,SAASX,EAAqB,CAAA,CAC5C;AAEJ;"}