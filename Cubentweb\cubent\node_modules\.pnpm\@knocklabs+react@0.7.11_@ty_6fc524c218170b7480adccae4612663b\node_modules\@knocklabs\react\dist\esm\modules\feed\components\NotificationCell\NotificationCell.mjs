import { use<PERSON>nock<PERSON>eed as w, useTranslations as T, renderNodeOrFallback as u, formatTimestamp as h } from "@knocklabs/react-core";
import n, { useMemo as A } from "react";
import { Button as B } from "../../../core/components/Button/Button.mjs";
import { ButtonGroup as H } from "../../../core/components/Button/ButtonGroup.mjs";
import "lodash.debounce";
import { ArchiveButton as M } from "./ArchiveButton.mjs";
import { Avatar as x } from "./Avatar.mjs";
/* empty css            */
function p(e) {
  e && e !== "" && setTimeout(() => window.location.assign(e), 200);
}
const L = n.forwardRef(({
  item: e,
  onItemClick: l,
  onButtonClick: i,
  avatar: k,
  children: f,
  archiveButton: y
}, E) => {
  var _;
  const {
    feedClient: o,
    colorMode: N
  } = w(), {
    locale: b
  } = T(), c = A(() => e.blocks.reduce((t, a) => ({
    ...t,
    [a.name]: a
  }), {}), [e]), s = (_ = c.action_url) == null ? void 0 : _.rendered, d = c.actions, m = n.useCallback(() => (o.markAsInteracted(e, {
    type: "cell_click",
    action: s
  }), l ? l(e) : p(s)), [e, s, l, o]), v = n.useCallback((t, a) => (o.markAsInteracted(e, {
    type: "button_click",
    name: a.name,
    label: a.label,
    action: a.action
  }), i ? i(e, a) : p(a.action)), [i, o, e]), C = n.useCallback((t) => {
    switch (t.key) {
      case "Enter": {
        t.stopPropagation(), m();
        break;
      }
    }
  }, [m]), r = e.actors[0];
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    /* @__PURE__ */ n.createElement(
      "div",
      {
        ref: E,
        className: `rnf-notification-cell rnf-notification-cell--${N}`,
        onClick: m,
        onKeyDown: C,
        tabIndex: 0
      },
      /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__inner" }, !e.read_at && /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__unread-dot" }), u(k, r && "name" in r && r.name && /* @__PURE__ */ n.createElement(x, { name: r.name, src: r.avatar })), /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__content-outer" }, c.body && /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__content", dangerouslySetInnerHTML: {
        __html: c.body.rendered
      } }), d && /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__button-group" }, /* @__PURE__ */ n.createElement(H, null, d.buttons.map((t, a) => /* @__PURE__ */ n.createElement(B, { variant: a === 0 ? "primary" : "secondary", key: t.name, onClick: (g) => v(g, t) }, t.label)))), f && /* @__PURE__ */ n.createElement("div", { className: "rnf-notification-cell__child-content" }, f), /* @__PURE__ */ n.createElement("span", { className: "rnf-notification-cell__timestamp" }, h(e.inserted_at, {
        locale: b
      }))), u(y, /* @__PURE__ */ n.createElement(M, { item: e })))
    )
  );
});
export {
  L as NotificationCell
};
//# sourceMappingURL=NotificationCell.mjs.map
