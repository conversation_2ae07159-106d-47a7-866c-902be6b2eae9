{"version": 3, "file": "useOnBottomScroll.js", "sources": ["../../../../../src/modules/core/hooks/useOnBottomScroll.ts"], "sourcesContent": ["import debounce from \"lodash.debounce\";\nimport { RefObject, useCallback, useEffect, useMemo } from \"react\";\n\ntype OnBottomScrollOptions = {\n  ref: RefObject<HTMLDivElement | undefined>;\n  callback: () => void;\n  offset?: number;\n};\n\nconst noop = () => {};\n\nfunction useOnBottomScroll(options: OnBottomScrollOptions) {\n  const callback = options.callback ?? noop;\n  const ref = options.ref;\n  const offset = options.offset ?? 0;\n\n  const debouncedCallback = useMemo(() => debounce(callback, 200), [callback]);\n\n  const handleOnScroll = useCallback(() => {\n    if (ref.current) {\n      const scrollNode = ref.current;\n      const scrollContainerBottomPosition = Math.round(\n        scrollNode.scrollTop + scrollNode.clientHeight,\n      );\n      const scrollPosition = Math.round(scrollNode.scrollHeight - offset);\n\n      if (scrollPosition <= scrollContainerBottomPosition) {\n        debouncedCallback();\n      }\n    }\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [debouncedCallback]);\n\n  useEffect(() => {\n    let element: HTMLElement | undefined;\n    if (ref.current) {\n      element = ref.current;\n      ref.current.addEventListener(\"scroll\", handleOnScroll);\n    }\n\n    return () => {\n      if (element) {\n        element.removeEventListener(\"scroll\", handleOnScroll);\n      }\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [handleOnScroll]);\n}\n\nexport default useOnBottomScroll;\n"], "names": ["noop", "useOnBottomScroll", "options", "callback", "ref", "offset", "deboun<PERSON><PERSON><PERSON><PERSON>", "useMemo", "debounce", "handleOnScroll", "useCallback", "current", "scrollNode", "scrollContainerBottomPosition", "Math", "round", "scrollTop", "clientHeight", "scrollHeight", "useEffect", "element", "addEventListener", "removeEventListener"], "mappings": "kIASMA,EAAOA,IAAM,CAAC,EAEpB,SAASC,EAAkBC,EAAgC,CACnDC,MAAAA,EAAWD,EAAQC,UAAYH,EAC/BI,EAAMF,EAAQE,IACdC,EAASH,EAAQG,QAAU,EAE3BC,EAAoBC,UAAQ,IAAMC,EAAAA,QAASL,EAAU,GAAG,EAAG,CAACA,CAAQ,CAAC,EAErEM,EAAiBC,EAAAA,YAAY,IAAM,CACvC,GAAIN,EAAIO,QAAS,CACf,MAAMC,EAAaR,EAAIO,QACjBE,EAAgCC,KAAKC,MACzCH,EAAWI,UAAYJ,EAAWK,YACpC,EACuBH,KAAKC,MAAMH,EAAWM,aAAeb,CAAM,GAE5CQ,GACFP,EAAA,CACpB,CACF,EAGC,CAACA,CAAiB,CAAC,EAEtBa,EAAAA,UAAU,IAAM,CACVC,IAAAA,EACJ,OAAIhB,EAAIO,UACNS,EAAUhB,EAAIO,QACVA,EAAAA,QAAQU,iBAAiB,SAAUZ,CAAc,GAGhD,IAAM,CACPW,GACME,EAAAA,oBAAoB,SAAUb,CAAc,CAExD,CAAA,EAGC,CAACA,CAAc,CAAC,CACrB"}