"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const r=require("@knocklabs/client"),c=require("@knocklabs/react-core"),n=require("react"),v=require("../../../core/components/Spinner/Spinner.js"),R=require("../../../core/hooks/useOnBottomScroll.js"),I=require("../EmptyFeed/EmptyFeed.js"),B=require("../NotificationCell/NotificationCell.js");;/* empty css                              */const H=require("./NotificationFeedHeader.js");;/* empty css            */const K=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=K(n),M=e=>t.default.createElement(B.NotificationCell,{key:e.item.id,...e}),P=e=>t.default.createElement(H.NotificationFeedHeader,{...e}),g=({colorMode:e})=>t.default.createElement("div",{className:"rnf-notification-feed__spinner-container"},t.default.createElement(v.Spinner,{thickness:3,size:"16px",color:e==="dark"?"rgba(255, 255, 255, 0.65)":void 0})),j="https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed",x=({EmptyComponent:e=t.default.createElement(I.EmptyFeed,null),renderItem:E=M,onNotificationClick:_,onNotificationButtonClick:N,onMarkAllAsReadClick:S,initialFilterStatus:i=c.FilterStatus.All,header:b,renderHeader:q=P})=>{const[l,u]=n.useState(i),{feedClient:o,useFeedStore:F,colorMode:s}=c.useKnockFeed(),{settings:f}=c.useFeedSettings(o),{t:y}=c.useTranslations(),{pageInfo:m,items:k,networkStatus:a}=F(),p=n.useRef(null);n.useEffect(()=>{u(i)},[i]),n.useEffect(()=>{o.fetch({status:l})},[o,l]);const h=k.length===0,d=r.isRequestInFlight(a),C=n.useCallback(()=>{!d&&m.after&&o.fetchNextPage()},[d,m,o]);return R({ref:p,callback:C,offset:70}),t.default.createElement("div",{className:`rnf-notification-feed rnf-notification-feed--${s}`},b||q({setFilterStatus:u,filterStatus:l,onMarkAllAsReadClick:S}),t.default.createElement("div",{className:"rnf-notification-feed__container",ref:p},a===r.NetworkStatus.loading&&t.default.createElement(g,{colorMode:s}),t.default.createElement("div",{className:"rnf-notification-feed__feed-items-container"},a!==r.NetworkStatus.loading&&k.map(w=>E({item:w,onItemClick:_,onButtonClick:N}))),a===r.NetworkStatus.fetchMore&&t.default.createElement(g,{colorMode:s}),!d&&h&&e),(f==null?void 0:f.features.branding_required)&&t.default.createElement("div",{className:"rnf-notification-feed__knock-branding"},t.default.createElement("a",{href:j,target:"_blank"},y("poweredBy")||"Powered by Knock")))};exports.NotificationFeed=x;
//# sourceMappingURL=NotificationFeed.js.map
