import { ActionButton, FeedItem } from '@knocklabs/client';
import { default as React, ReactNode } from 'react';
export interface NotificationCellProps {
    item: FeedItem;
    onItemClick?: (item: FeedItem) => void;
    onButtonClick?: (item: FeedItem, action: ActionButton) => void;
    avatar?: ReactNode;
    children?: ReactNode;
    archiveButton?: ReactNode;
}
export declare const NotificationCell: React.ForwardRefExoticComponent<NotificationCellProps & React.RefAttributes<HTMLDivElement>>;
//# sourceMappingURL=NotificationCell.d.ts.map