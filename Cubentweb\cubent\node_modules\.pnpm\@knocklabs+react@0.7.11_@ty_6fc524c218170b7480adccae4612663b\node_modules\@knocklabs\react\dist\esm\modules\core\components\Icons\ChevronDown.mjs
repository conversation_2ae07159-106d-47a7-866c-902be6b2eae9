import e from "react";
const i = ({
  width: o = 8,
  height: r = 6,
  "aria-hidden": t
}) => /* @__PURE__ */ e.createElement("svg", { width: o, height: r, viewBox: "0 0 8 6", fill: "none", xmlns: "http://www.w3.org/2000/svg", "aria-hidden": t }, /* @__PURE__ */ e.createElement("path", { d: "M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }));
export {
  i as ChevronDown
};
//# sourceMappingURL=ChevronDown.mjs.map
