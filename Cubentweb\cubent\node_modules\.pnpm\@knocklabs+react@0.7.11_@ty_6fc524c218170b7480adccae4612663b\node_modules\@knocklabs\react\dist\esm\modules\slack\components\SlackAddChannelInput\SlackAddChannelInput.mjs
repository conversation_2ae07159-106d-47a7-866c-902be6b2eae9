import e, { useState as s } from "react";
import { useTranslations as f } from "@knocklabs/react-core";
import { Spinner as C } from "../../../core/components/Spinner/Spinner.mjs";
/* empty css                                          */
import "lodash.debounce";
/* empty css               */
import _ from "../SlackChannelCombobox/SlackConnectionError.mjs";
import { SlackIcon as E } from "../SlackIcon/SlackIcon.mjs";
/* empty css            */
const A = ({
  inErrorState: i,
  connectedChannels: c = [],
  updateConnectedChannels: m,
  connectedChannelsError: u,
  connectedChannelsUpdating: p
}) => {
  const {
    t
  } = f(), [n, a] = s(null), [l, r] = s(null), d = () => {
    if (!n)
      return;
    if (l && r(null), c.find((k) => k.channel_id === n))
      return a(""), r(t("slackChannelAlreadyConnected") || "");
    const h = [...c, {
      channel_id: n
    }];
    m(h), a("");
  };
  return /* @__PURE__ */ e.createElement("div", { className: "rsk-connect-channel" }, /* @__PURE__ */ e.createElement("input", { className: `rsk-connect-channel__input ${(i || !!l) && !n && "rsk-connect-channel__input--error"}`, tabIndex: -1, id: "slack-channel-search", type: "text", placeholder: l || u || t("slackChannelId"), onChange: (o) => a(o.target.value), value: n || "" }), /* @__PURE__ */ e.createElement("button", { className: "rsk-connect-channel__button", onClick: d }, p ? /* @__PURE__ */ e.createElement(C, { size: "15px", thickness: 3 }) : /* @__PURE__ */ e.createElement(E, { height: "16px", width: "16px" }), t("slackConnectChannel")), /* @__PURE__ */ e.createElement(_, null));
};
export {
  A as default
};
//# sourceMappingURL=SlackAddChannelInput.mjs.map
