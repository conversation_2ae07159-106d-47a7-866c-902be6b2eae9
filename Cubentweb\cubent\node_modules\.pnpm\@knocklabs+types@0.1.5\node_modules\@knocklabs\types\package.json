{"name": "@knocklabs/types", "description": "Type definitions for Knock libraries", "author": "@knocklabs", "version": "0.1.5", "license": "MIT", "types": "./src/index.d.ts", "scripts": {"lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type:check": "tsc --noEmit", "format": "prettier \"src/**/*.{js,ts,tsx}\" --write", "format:check": "prettier \"src/**/*.{js,ts,tsx}\" --check"}, "devDependencies": {"eslint": "^8.56.0", "typescript": "^5.6.3"}, "publishConfig": {"access": "public"}}