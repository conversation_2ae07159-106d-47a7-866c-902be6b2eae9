"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const b=require("@knocklabs/react-core"),d=require("react"),i=require("./ButtonSpinner.js");;/* empty css            */const m=t=>t&&typeof t=="object"&&"default"in t?t:{default:t},n=m(d),p=({variant:t="primary",loadingText:o,isLoading:e=!1,isDisabled:u=!1,isFullWidth:a=!1,onClick:l,children:r})=>{const{colorMode:s}=b.useKnockFeed(),c=["rnf-button",`rnf-button--${t}`,a?"rnf-button--full-width":"",e?"rnf-button--is-loading":"",`rnf-button--${s}`].join(" "),f=o||n.default.createElement("span",{className:"rnf-button__button-text-hidden"},r);return n.default.createElement("button",{onClick:l,className:c,disabled:e||u,type:"button"},e&&n.default.createElement(i.ButtonSpinner,{hasLabel:!!o}),e?f:r)};exports.Button=p;
//# sourceMappingURL=Button.js.map
