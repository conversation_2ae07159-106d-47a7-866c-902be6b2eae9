{"version": 3, "file": "Modal.js", "sources": ["../../../../../../src/modules/guide/components/Modal/Modal.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"modal\";\n\ntype RootProps = Omit<\n  React.ComponentPropsWithoutRef<typeof Dialog.Root>,\n  \"modal\"\n> &\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>;\n\nconst Root = ({ children, onOpenChange, ...props }: RootProps) => {\n  return (\n    <Dialog.Root defaultOpen onOpenChange={onOpenChange} {...props}>\n      <Dialog.Portal>{children}</Dialog.Portal>\n    </Dialog.Root>\n  );\n};\nRoot.displayName = \"ModalView.Root\";\n\ntype OverlayProps = React.ComponentPropsWithoutRef<typeof Dialog.Overlay> &\n  React.ComponentPropsWithRef<\"div\">;\ntype OverlayRef = React.ElementRef<\"div\">;\n\nconst Overlay = React.forwardRef<OverlayRef, OverlayProps>(\n  ({ className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Overlay\n        className={clsx(\"knock-guide-modal__overlay\", className)}\n        ref={forwardedRef}\n        {...props}\n      />\n    );\n  },\n);\nOverlay.displayName = \"ModalView.Overlay\";\n\ntype ContentProps = React.ComponentPropsWithoutRef<typeof Dialog.Content> &\n  React.ComponentPropsWithRef<\"div\">;\ntype ContentRef = React.ElementRef<\"div\">;\n\nconst Content = React.forwardRef<ContentRef, ContentProps>(\n  ({ children, className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Content\n        className={clsx(\"knock-guide-modal\", className)}\n        ref={forwardedRef}\n        {...props}\n      >\n        {children}\n      </Dialog.Content>\n    );\n  },\n);\nContent.displayName = \"ModalView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"ModalView.Header\";\n\ntype TitleProps = React.ComponentPropsWithoutRef<typeof Dialog.Title> &\n  React.ComponentPropsWithRef<\"div\"> & {\n    title: string;\n  };\n\nconst Title = ({ title, className, ...props }: TitleProps) => {\n  return (\n    <Dialog.Title\n      className={clsx(\"knock-guide-modal__title\", className)}\n      {...props}\n    >\n      {title}\n    </Dialog.Title>\n  );\n};\nTitle.displayName = \"ModalView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <Dialog.Description\n      className={clsx(\"knock-guide-modal__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"ModalView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-modal__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"ModalView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"ModalView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-modal__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"ModalView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-modal__action knock-guide-modal__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"ModalView.SecondaryButton\";\n\ntype CloseProps = React.ComponentPropsWithoutRef<typeof Dialog.Close> &\n  React.ComponentPropsWithRef<\"button\">;\n\nconst Close = ({ className, ...props }: CloseProps) => {\n  return (\n    <Dialog.Close\n      className={clsx(\"knock-guide-modal__close\", className)}\n      {...props}\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </Dialog.Close>\n  );\n};\nClose.displayName = \"ModalView.Close\";\n\ntype ModalContent = {\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: ModalContent;\n  colorMode?: ColorMode;\n  onOpenChange?: (open: boolean) => void;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onOpenChange,\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root onOpenChange={onOpenChange}>\n      <Overlay />\n      {/* Must pass color mode to content for css variables to be set properly */}\n      <Content\n        data-knock-color-mode={colorMode}\n        onPointerDownOutside={onDismiss}\n      >\n        <Header>\n          <Title title={content.title} />\n          {content.dismissible && <Close onClick={onDismiss} />}\n        </Header>\n\n        <Body body={content.body} />\n\n        {content.image && (\n          <a\n            href={\n              isValidHttpUrl(content.image.action)\n                ? content.image.action\n                : undefined\n            }\n            target=\"_blank\"\n          >\n            <Img\n              src={content.image.url}\n              alt={content.image.alt}\n              onClick={(e) => {\n                if (onImageClick) {\n                  onImageClick(e, content.image!);\n                }\n              }}\n            />\n          </a>\n        )}\n\n        {(content.primary_button || content.secondary_button) && (\n          <Actions>\n            {content.secondary_button && (\n              <SecondaryButton\n                text={content.secondary_button.text}\n                action={content.secondary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.secondary_button!;\n                    onButtonClick(e, {\n                      name: \"secondary_button\",\n                      text,\n                      action,\n                    });\n                  }\n                }}\n              />\n            )}\n            {content.primary_button && (\n              <PrimaryButton\n                text={content.primary_button.text}\n                action={content.primary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.primary_button!;\n                    onButtonClick(e, { name: \"primary_button\", text, action });\n                  }\n                }}\n              />\n            )}\n          </Actions>\n        )}\n      </Content>\n    </Root>\n  );\n};\nDefaultView.displayName = \"ModalView.Default\";\n\ntype ModalProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Modal: React.FC<ModalProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as ModalContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nModal.displayName = \"Modal\";\n\nexport const ModalView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Overlay: typeof Overlay;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  Close: typeof Close;\n};\n\nObject.assign(ModalView, {\n  Default: DefaultView,\n  Root,\n  Overlay,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  Close,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "onOpenChange", "props", "React", "Dialog", "displayName", "Overlay", "forwardRef", "className", "forwardedRef", "clsx", "Content", "Header", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "Close", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Modal", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": "+oBAiBMA,EAAe,QAQfC,EAAOA,CAAC,CAAEC,SAAAA,EAAUC,aAAAA,EAAc,GAAGC,CAAiB,IAEvDC,EAAAA,QAAA,cAAAC,EAAO,KAAP,CAAY,YAAW,GAAC,aAAAH,EAA4B,GAAIC,CAAAA,EACtDC,EAAA,QAAA,cAAAC,EAAO,OAAP,KAAeJ,CAAS,CAC3B,EAGJD,EAAKM,YAAc,iBAMnB,MAAMC,EAAUH,EAAAA,QAAMI,WACpB,CAAC,CAAEC,UAAAA,EAAW,GAAGN,CAAM,EAAGO,IAErBN,EAAA,QAAA,cAAAC,EAAO,QAAP,CACC,UAAWM,EAAAA,QAAK,6BAA8BF,CAAS,EACvD,IAAKC,EACL,GAAIP,CACJ,CAAA,CAGR,EACAI,EAAQD,YAAc,oBAMtB,MAAMM,EAAUR,EAAAA,QAAMI,WACpB,CAAC,CAAEP,SAAAA,EAAUQ,UAAAA,EAAW,GAAGN,CAAM,EAAGO,IAE/BN,EAAAA,QAAA,cAAAC,EAAO,QAAP,CACC,UAAWM,EAAAA,QAAK,oBAAqBF,CAAS,EAC9C,IAAKC,EACDP,GAAAA,GAEHF,CACH,CAGN,EACAW,EAAQN,YAAc,oBAEtB,MAAMO,EAEFA,CAAC,CAAEZ,SAAAA,EAAUQ,UAAAA,EAAW,GAAGN,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWO,EAAAA,QAAK,4BAA6BF,CAAS,EAAG,GAAIN,CAAAA,EAC/DF,CACH,EAGJY,EAAOP,YAAc,mBAOrB,MAAMQ,EAAQA,CAAC,CAAEC,MAAAA,EAAON,UAAAA,EAAW,GAAGN,CAAkB,IAEpDC,EAAA,QAAA,cAACC,EAAO,MAAP,CACC,UAAWM,UAAK,2BAA4BF,CAAS,EACrD,GAAIN,CAAAA,EAEHY,CACH,EAGJD,EAAMR,YAAc,kBAEpB,MAAMU,EAAwEA,CAAC,CAC7EC,KAAAA,EACAR,UAAAA,EACA,GAAGN,CACL,IAEIC,UAAA,cAACC,EAAO,YAAP,CACC,UAAWM,EAAAA,QAAK,0BAA2BF,CAAS,EACpD,wBAAyB,CAAES,OAAQD,CAAAA,EAC/Bd,GAAAA,CACJ,CAAA,EAGNa,EAAKV,YAAc,iBAEnB,MAAMa,EAEFA,CAAC,CAAElB,SAAAA,EAAUQ,UAAAA,EAAWW,IAAAA,EAAK,GAAGjB,CAAM,IAErCC,EAAAA,QAAA,cAAA,MAAA,CACC,UAAWO,EAAAA,QAAK,yBAA0BF,CAAS,EACnD,IAAKW,GAAO,GACRjB,GAAAA,CAAAA,EAEHF,CACH,EAGJkB,EAAIb,YAAc,gBAElB,MAAMe,EAEFA,CAAC,CAAEpB,SAAAA,EAAUQ,UAAAA,EAAW,GAAGN,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWO,EAAAA,QAAK,6BAA8BF,CAAS,EAAG,GAAIN,CAAAA,EAChEF,CACH,EAGJoB,EAAQf,YAAc,oBAEtB,MAAMgB,EAEFA,CAAC,CAAEC,KAAAA,EAAMC,OAAAA,EAAQf,UAAAA,EAAW,GAAGN,CAAM,IAErCC,UAAA,cAAC,UAAO,UAAWO,EAAAA,QAAK,4BAA6BF,CAAS,EAAG,GAAIN,CAAAA,EAClEoB,CACH,EAGJD,EAAchB,YAAc,0BAE5B,MAAMmB,EAEFA,CAAC,CAAEF,KAAAA,EAAMC,OAAAA,EAAQf,UAAAA,EAAW,GAAGN,CAAM,IAErCC,UAAA,cAAC,UACC,UAAWO,EAAAA,QACT,iEACAF,CACF,EACA,GAAIN,CAAAA,EAEHoB,CACH,EAGJE,EAAgBnB,YAAc,4BAK9B,MAAMoB,EAAQA,CAAC,CAAEjB,UAAAA,EAAW,GAAGN,CAAkB,4BAE5CE,EAAO,MAAP,CACC,UAAWM,EAAAA,QAAK,2BAA4BF,CAAS,EACrD,GAAIN,CAAAA,0BAEH,MACC,CAAA,MAAM,6BACN,MAAM,KACN,OAAO,KACP,KAAK,MAEL,EAAAC,UAAA,cAAC,KAAE,KAAK,UAAU,SAAS,UAAU,SAAS,WAC3CA,EAAAA,QAAA,cAAA,OAAA,CAAK,EAAE,uFAAsF,EAC7FA,UAAA,cAAA,OAAA,CAAK,EAAE,qFAAqF,CAAA,CAC/F,CACF,CACF,EAGJsB,EAAMpB,YAAc,kBAWpB,MAAMqB,EAODA,CAAC,CACJC,QAAAA,EACAC,UAAAA,EAAY,QACZ3B,aAAAA,EACA4B,UAAAA,EACAC,cAAAA,EACAC,aAAAA,CACF,IAEK5B,EAAAA,QAAA,cAAAJ,EAAA,CAAK,aAAAE,GACJE,EAAAA,QAAA,cAACG,EAAO,IAAA,EAEPH,EAAA,QAAA,cAAAQ,EAAA,CACC,wBAAuBiB,EACvB,qBAAsBC,GAErB1B,EAAAA,QAAA,cAAAS,EAAA,KACET,UAAA,cAAAU,EAAA,CAAM,MAAOc,EAAQb,KAAM,CAAA,EAC3Ba,EAAQK,aAAgB7B,EAAAA,QAAA,cAAAsB,EAAA,CAAM,QAASI,CAAAA,CAAa,CACvD,EAEC1B,EAAA,QAAA,cAAAY,EAAA,CAAK,KAAMY,EAAQX,IAAK,CAAA,EAExBW,EAAQM,OACN9B,EAAA,QAAA,cAAA,IAAA,CACC,KACE+B,EAAAA,eAAeP,EAAQM,MAAMV,MAAM,EAC/BI,EAAQM,MAAMV,OACdY,OAEN,OAAO,QAAA,EAENhC,EAAAA,QAAA,cAAAe,EAAA,CACC,IAAKS,EAAQM,MAAMG,IACnB,IAAKT,EAAQM,MAAMd,IACnB,QAAgBkB,GAAA,CACVN,GACWM,EAAAA,EAAGV,EAAQM,KAAM,CAElC,CAAA,CAAE,CAEN,GAGAN,EAAQW,gBAAkBX,EAAQY,mBACjCpC,EAAA,QAAA,cAAAiB,EAAA,KACEO,EAAQY,0CACNf,EACC,CAAA,KAAMG,EAAQY,iBAAiBjB,KAC/B,OAAQK,EAAQY,iBAAiBhB,OACjC,QAAgBc,GAAA,CACd,GAAIP,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQY,iBACjCT,EAAcO,EAAG,CACfG,KAAM,mBACNlB,KAAAA,EACAC,OAAAA,CAAAA,CACD,CAAA,CACH,CAGL,CAAA,EACAI,EAAQW,wCACNjB,EACC,CAAA,KAAMM,EAAQW,eAAehB,KAC7B,OAAQK,EAAQW,eAAef,OAC/B,QAAgBc,GAAA,CACd,GAAIP,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQW,eACjCR,EAAcO,EAAG,CAAEG,KAAM,iBAAkBlB,KAAAA,EAAMC,OAAAA,CAAAA,CAAQ,CAAA,CAC3D,CAGL,CAAA,CACH,CAEJ,CACF,EAGJG,EAAYrB,YAAc,oBAQnB,MAAMoC,EAA8BA,CAAC,CAC1CC,SAAAA,EACAZ,cAAAA,EACAC,aAAAA,CACF,IAAM,CACE,KAAA,CAAEY,MAAAA,EAAOC,KAAAA,EAAMhB,UAAAA,GAAciB,WAAS,CAC1CC,IAAKJ,EACLK,KAAMjD,CAAAA,CACP,EAMD,OAJAK,EAAAA,QAAM6C,UAAU,IAAM,CAChBJ,KAAWK,WAAW,CAAA,EACzB,CAACL,CAAI,CAAC,EAEL,CAACD,GAAS,CAACC,EAAa,KAGzBzC,EAAAA,QAAA,cAAAuB,EAAA,CACC,QAASkB,EAAKjB,QACd,UAAAC,EACA,UAAW,IAAMgB,EAAKM,eAAe,EACrC,cAAe,CAACb,EAAGc,IAAW,CAC5B,MAAMC,EAAW,CAAE,GAAGD,EAAQJ,KAAM,cAAe,EACnDH,OAAAA,EAAKS,iBAAiB,CAAED,SAAAA,CAAAA,CAAU,EAE3BtB,EACHA,EAAcO,EAAG,CAAEc,OAAAA,EAAQP,KAAAA,EAAMD,MAAAA,CAAAA,CAAO,EACxCW,EAAAA,4BAA4BH,EAAO5B,MAAM,CAAA,EAE/C,aAAc,CAACc,EAAGJ,IAAU,CAC1B,MAAMmB,EAAW,CAAE,GAAGnB,EAAOc,KAAM,aAAc,EAGjD,GAFAH,EAAKS,iBAAiB,CAAED,SAAAA,CAAAA,CAAU,EAE9BrB,EACF,OAAOA,EAAaM,EAAG,CAAEJ,MAAAA,EAAOW,KAAAA,EAAMD,MAAAA,CAAAA,CAAO,CAC/C,EAEF,CAEN,EACAF,EAAMpC,YAAc,QAEb,MAAMkD,EAAY,CAAA,EAczBC,OAAOC,OAAOF,EAAW,CACvBG,QAAShC,EACT3B,KAAAA,EACAO,QAAAA,EACAK,QAAAA,EACAE,MAAAA,EACAE,KAAAA,EACAG,IAAAA,EACAE,QAAAA,EACAC,cAAAA,EACAG,gBAAAA,EACAC,MAAAA,CACF,CAAC"}