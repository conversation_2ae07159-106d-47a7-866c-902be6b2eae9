{"version": 3, "file": "Dropdown.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/Dropdown.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\n\nimport { ChevronDown } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type DropdownProps = {\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n};\n\nexport const Dropdown: React.FC<PropsWithChildren<DropdownProps>> = ({\n  children,\n  value,\n  onChange,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <div className={`rnf-dropdown rnf-dropdown--${colorMode}`}>\n      <select\n        aria-label=\"Select notification filter\"\n        value={value}\n        onChange={onChange}\n      >\n        {children}\n      </select>\n      <ChevronDown aria-hidden />\n    </div>\n  );\n};\n"], "names": ["Dropdown", "children", "value", "onChange", "colorMode", "useKnockFeed", "ChevronDown"], "mappings": "uSAYaA,EAAuDA,CAAC,CACnEC,SAAAA,EACAC,MAAAA,EACAC,SAAAA,CACF,IAAM,CACE,KAAA,CAAEC,UAAAA,GAAcC,eAAa,EAEnC,+BACG,MAAI,CAAA,UAAW,8BAA8BD,CAAS,EAAA,0BACpD,SACC,CAAA,aAAW,6BACX,MAAAF,EACA,SAAAC,GAECF,CACH,0BACCK,EAAAA,YAAY,CAAA,cAAW,EAAA,CAAA,CAC1B,CAEJ"}