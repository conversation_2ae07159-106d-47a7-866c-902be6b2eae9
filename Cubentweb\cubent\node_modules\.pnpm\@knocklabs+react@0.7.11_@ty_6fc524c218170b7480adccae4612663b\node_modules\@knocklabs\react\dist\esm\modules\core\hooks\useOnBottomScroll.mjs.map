{"version": 3, "file": "useOnBottomScroll.mjs", "sources": ["../../../../../src/modules/core/hooks/useOnBottomScroll.ts"], "sourcesContent": ["import debounce from \"lodash.debounce\";\nimport { RefObject, useCallback, useEffect, useMemo } from \"react\";\n\ntype OnBottomScrollOptions = {\n  ref: RefObject<HTMLDivElement | undefined>;\n  callback: () => void;\n  offset?: number;\n};\n\nconst noop = () => {};\n\nfunction useOnBottomScroll(options: OnBottomScrollOptions) {\n  const callback = options.callback ?? noop;\n  const ref = options.ref;\n  const offset = options.offset ?? 0;\n\n  const debouncedCallback = useMemo(() => debounce(callback, 200), [callback]);\n\n  const handleOnScroll = useCallback(() => {\n    if (ref.current) {\n      const scrollNode = ref.current;\n      const scrollContainerBottomPosition = Math.round(\n        scrollNode.scrollTop + scrollNode.clientHeight,\n      );\n      const scrollPosition = Math.round(scrollNode.scrollHeight - offset);\n\n      if (scrollPosition <= scrollContainerBottomPosition) {\n        debouncedCallback();\n      }\n    }\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [debouncedCallback]);\n\n  useEffect(() => {\n    let element: HTMLElement | undefined;\n    if (ref.current) {\n      element = ref.current;\n      ref.current.addEventListener(\"scroll\", handleOnScroll);\n    }\n\n    return () => {\n      if (element) {\n        element.removeEventListener(\"scroll\", handleOnScroll);\n      }\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [handleOnScroll]);\n}\n\nexport default useOnBottomScroll;\n"], "names": ["noop", "useOnBottomScroll", "options", "callback", "ref", "offset", "deboun<PERSON><PERSON><PERSON><PERSON>", "useMemo", "debounce", "handleOnScroll", "useCallback", "current", "scrollNode", "scrollContainerBottomPosition", "Math", "round", "scrollTop", "clientHeight", "scrollHeight", "useEffect", "element", "addEventListener", "removeEventListener"], "mappings": ";;AASA,MAAMA,IAAOA,MAAM;AAAC;AAEpB,SAASC,EAAkBC,GAAgC;AACnDC,QAAAA,IAAWD,EAAQC,YAAYH,GAC/BI,IAAMF,EAAQE,KACdC,IAASH,EAAQG,UAAU,GAE3BC,IAAoBC,EAAQ,MAAMC,EAASL,GAAU,GAAG,GAAG,CAACA,CAAQ,CAAC,GAErEM,IAAiBC,EAAY,MAAM;AACvC,QAAIN,EAAIO,SAAS;AACf,YAAMC,IAAaR,EAAIO,SACjBE,IAAgCC,KAAKC,MACzCH,EAAWI,YAAYJ,EAAWK,YACpC;AAGA,MAFuBH,KAAKC,MAAMH,EAAWM,eAAeb,CAAM,KAE5CQ,KACFP,EAAA;AAAA,IACpB;AAAA,EACF,GAGC,CAACA,CAAiB,CAAC;AAEtBa,EAAAA,EAAU,MAAM;AACVC,QAAAA;AACJ,WAAIhB,EAAIO,YACNS,IAAUhB,EAAIO,SACVA,EAAAA,QAAQU,iBAAiB,UAAUZ,CAAc,IAGhD,MAAM;AACX,MAAIW,KACME,EAAAA,oBAAoB,UAAUb,CAAc;AAAA,IAExD;AAAA,EAAA,GAGC,CAACA,CAAc,CAAC;AACrB;"}