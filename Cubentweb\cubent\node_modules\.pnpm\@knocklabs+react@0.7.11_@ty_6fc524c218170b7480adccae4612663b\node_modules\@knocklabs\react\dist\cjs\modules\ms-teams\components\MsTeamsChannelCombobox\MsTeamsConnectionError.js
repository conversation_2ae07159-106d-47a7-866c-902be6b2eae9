"use strict";const r=require("react"),n=require("@knocklabs/react-core"),s=require("./MsTeamsErrorMessage.js"),o=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},c=o(r),a=()=>{const{t:e}=n.useTranslations(),{connectionStatus:t}=n.useKnockMsTeamsClient();return t==="disconnected"||t==="error"?c.default.createElement(s,{message:e(t==="disconnected"?"msTeamsConnectionErrorOccurred":"msTeamsConnectionErrorExists")}):null};module.exports=a;
//# sourceMappingURL=MsTeamsConnectionError.js.map
