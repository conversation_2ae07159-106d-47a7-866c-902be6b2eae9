{"version": 3, "file": "Spinner.js", "sources": ["../../../../../../src/modules/core/components/Spinner/Spinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype Speed = \"fast\" | \"slow\" | \"medium\";\n\nfunction speedSwitch(speed: Speed) {\n  if (speed === \"fast\") return 600;\n  if (speed === \"slow\") return 900;\n  return 750;\n}\n\nexport interface SpinnerProps {\n  color?: string;\n  speed?: Speed;\n  gap?: number;\n  thickness?: number;\n  size?: string;\n}\n\nexport const Spinner: FunctionComponent<SpinnerProps> = ({\n  color = \"rgba(0,0,0,0.4)\",\n  speed = \"medium\",\n  gap = 4,\n  thickness = 4,\n  size = \"1em\",\n  ...props\n}) => (\n  <svg\n    height={size}\n    width={size}\n    {...props}\n    style={{ animationDuration: `${speedSwitch(speed)}ms` }}\n    className=\"__react-svg-spinner_circle\"\n    role=\"img\"\n    aria-labelledby=\"title desc\"\n    viewBox=\"0 0 32 32\"\n  >\n    <title id=\"title\">Circle loading spinner</title>\n    <desc id=\"desc\">Image of a partial circle indicating \"loading.\"</desc>\n    <style\n      dangerouslySetInnerHTML={{\n        __html: `\n      .__react-svg-spinner_circle{\n          transition-property: transform;\n          animation-name: __react-svg-spinner_infinite-spin;\n          animation-iteration-count: infinite;\n          animation-timing-function: linear;\n      }\n      @keyframes __react-svg-spinner_infinite-spin {\n          from {transform: rotate(0deg)}\n          to {transform: rotate(360deg)}\n      }\n    `,\n      }}\n    />\n    <circle\n      role=\"presentation\"\n      cx={16}\n      cy={16}\n      r={14 - thickness / 2}\n      stroke={color}\n      fill=\"none\"\n      strokeWidth={thickness}\n      strokeDasharray={Math.PI * 2 * (11 - gap)}\n      strokeLinecap=\"round\"\n    />\n  </svg>\n);\n"], "names": ["speedSwitch", "speed", "Spinner", "color", "gap", "thickness", "size", "props", "React", "animationDuration", "__html", "Math", "PI"], "mappings": "wKAIA,SAASA,EAAYC,EAAc,CAC7BA,OAAAA,IAAU,OAAe,IACzBA,IAAU,OAAe,IACtB,GACT,CAUO,MAAMC,EAA2CA,CAAC,CACvDC,MAAAA,EAAQ,kBACRF,MAAAA,EAAQ,SACRG,IAAAA,EAAM,EACNC,UAAAA,EAAY,EACZC,KAAAA,EAAO,MACP,GAAGC,CACL,IACEC,UAAA,cAAC,OACC,OAAQF,EACR,MAAOA,EACP,GAAIC,EACJ,MAAO,CAAEE,kBAAmB,GAAGT,EAAYC,CAAK,CAAC,IAAK,EACtD,UAAU,6BACV,KAAK,MACL,kBAAgB,aAChB,QAAQ,WAER,EAAAO,EAAA,QAAA,cAAC,QAAM,CAAA,GAAG,SAAQ,wBAAsB,0BACvC,OAAK,CAAA,GAAG,QAAO,iDAA+C,EAC9DA,EAAA,QAAA,cAAA,QAAA,CACC,wBAAyB,CACvBE,OAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAYV,CAAE,CAAA,EAEHF,EAAA,QAAA,cAAA,SAAA,CACC,KAAK,eACL,GAAI,GACJ,GAAI,GACJ,EAAG,GAAKH,EAAY,EACpB,OAAQF,EACR,KAAK,OACL,YAAaE,EACb,gBAAiBM,KAAKC,GAAK,GAAK,GAAKR,GACrC,cAAc,OAAA,CAAO,CAEzB"}