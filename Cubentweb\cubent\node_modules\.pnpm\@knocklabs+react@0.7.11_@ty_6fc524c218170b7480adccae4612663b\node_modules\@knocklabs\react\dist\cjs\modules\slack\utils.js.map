{"version": 3, "file": "utils.js", "sources": ["../../../../src/modules/slack/utils.ts"], "sourcesContent": ["import { SlackChannel } from \"@knocklabs/client\";\n\nexport const sortSlackChannelsAlphabetically = (\n  channels: readonly SlackChannel[],\n) =>\n  [...channels].sort((channel1, channel2) =>\n    channel1.name.toLowerCase().localeCompare(channel2.name.toLowerCase()),\n  );\n"], "names": ["sortSlackChannelsAlphabetically", "channels", "sort", "channel1", "channel2", "name", "toLowerCase", "localeCompare"], "mappings": "gFAEaA,MAAAA,EACXC,GAEA,CAAC,GAAGA,CAAQ,EAAEC,KAAK,CAACC,EAAUC,IAC5BD,EAASE,KAAKC,cAAcC,cAAcH,EAASC,KAAKC,aAAa,CACvE"}