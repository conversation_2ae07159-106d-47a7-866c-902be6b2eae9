{"version": 3, "file": "NotificationFeed.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/NotificationFeed.tsx"], "sourcesContent": ["import { FeedItem, NetworkStatus, isRequestInFlight } from \"@knocklabs/client\";\nimport {\n  ColorMode,\n  FilterStatus,\n  useFeedSettings,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { GenericData } from \"@knocklabs/types\";\nimport React, {\n  ReactNode,\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from \"react\";\n\nimport { Spinner } from \"../../../core/components/Spinner\";\nimport useOnBottomScroll from \"../../../core/hooks/useOnBottomScroll\";\nimport { EmptyFeed } from \"../EmptyFeed\";\nimport { NotificationCell, NotificationCellProps } from \"../NotificationCell\";\n\nimport {\n  NotificationFeedHeader,\n  NotificationFeedHeaderProps,\n} from \"./NotificationFeedHeader\";\nimport \"./styles.css\";\n\nexport type RenderItemProps<T = GenericData> = {\n  item: FeedItem<T>;\n  onItemClick?: NotificationCellProps[\"onItemClick\"];\n  onButtonClick?: NotificationCellProps[\"onButtonClick\"];\n};\n\nexport type RenderItem = (props: RenderItemProps) => ReactNode;\n\nexport interface NotificationFeedProps {\n  EmptyComponent?: ReactNode;\n  /**\n   * @deprecated Use `renderHeader` instead to accept `NotificationFeedHeaderProps`\n   */\n  header?: ReactNode;\n  renderItem?: RenderItem;\n  renderHeader?: (props: NotificationFeedHeaderProps) => ReactNode;\n  onNotificationClick?: NotificationCellProps[\"onItemClick\"];\n  onNotificationButtonClick?: NotificationCellProps[\"onButtonClick\"];\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n  initialFilterStatus?: FilterStatus;\n}\n\nconst defaultRenderItem = (props: RenderItemProps) => (\n  <NotificationCell key={props.item.id} {...props} />\n);\n\nconst defaultRenderHeader = (props: NotificationFeedHeaderProps) => (\n  <NotificationFeedHeader {...props} />\n);\n\nconst LoadingSpinner = ({ colorMode }: { colorMode: ColorMode }) => (\n  <div className=\"rnf-notification-feed__spinner-container\">\n    <Spinner\n      thickness={3}\n      size=\"16px\"\n      color={colorMode === \"dark\" ? \"rgba(255, 255, 255, 0.65)\" : undefined}\n    />\n  </div>\n);\n\nconst poweredByKnockUrl =\n  \"https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed\";\n\nexport const NotificationFeed: React.FC<NotificationFeedProps> = ({\n  EmptyComponent = <EmptyFeed />,\n  renderItem = defaultRenderItem,\n  onNotificationClick,\n  onNotificationButtonClick,\n  onMarkAllAsReadClick,\n  initialFilterStatus = FilterStatus.All,\n  header,\n  renderHeader = defaultRenderHeader,\n}) => {\n  const [status, setStatus] = useState(initialFilterStatus);\n  const { feedClient, useFeedStore, colorMode } = useKnockFeed();\n  const { settings } = useFeedSettings(feedClient);\n  const { t } = useTranslations();\n\n  const { pageInfo, items, networkStatus } = useFeedStore();\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    setStatus(initialFilterStatus);\n  }, [initialFilterStatus]);\n\n  useEffect(() => {\n    // When the feed client changes, or the status changes issue a re-fetch\n    feedClient.fetch({ status });\n  }, [feedClient, status]);\n\n  const noItems = items.length === 0;\n  const requestInFlight = isRequestInFlight(networkStatus);\n\n  // Handle fetching more once we reach the bottom of the list\n  const onBottomCallback = useCallback(() => {\n    if (!requestInFlight && pageInfo.after) {\n      feedClient.fetchNextPage();\n    }\n  }, [requestInFlight, pageInfo, feedClient]);\n\n  // Once we scroll to the bottom of the view we want to automatically fetch\n  // more items for the feed and bring them into the list\n  useOnBottomScroll({\n    ref: containerRef,\n    callback: onBottomCallback,\n    offset: 70,\n  });\n\n  return (\n    <div\n      className={`rnf-notification-feed rnf-notification-feed--${colorMode}`}\n    >\n      {header ||\n        renderHeader({\n          setFilterStatus: setStatus,\n          filterStatus: status,\n          onMarkAllAsReadClick,\n        })}\n\n      <div className=\"rnf-notification-feed__container\" ref={containerRef}>\n        {networkStatus === NetworkStatus.loading && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        <div className=\"rnf-notification-feed__feed-items-container\">\n          {networkStatus !== NetworkStatus.loading &&\n            items.map((item: FeedItem) =>\n              renderItem({\n                item,\n                onItemClick: onNotificationClick,\n                onButtonClick: onNotificationButtonClick,\n              }),\n            )}\n        </div>\n\n        {networkStatus === NetworkStatus.fetchMore && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        {!requestInFlight && noItems && EmptyComponent}\n      </div>\n\n      {settings?.features.branding_required && (\n        <div className=\"rnf-notification-feed__knock-branding\">\n          <a href={poweredByKnockUrl} target=\"_blank\">\n            {t(\"poweredBy\") || \"Powered by Knock\"}\n          </a>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": ["defaultRenderItem", "props", "React", "NotificationCell", "item", "id", "defaultRenderHeader", "NotificationFeedHeader", "LoadingSpinner", "colorMode", "Spinner", "undefined", "poweredByKnockUrl", "NotificationFeed", "EmptyComponent", "EmptyFeed", "renderItem", "onNotificationClick", "onNotificationButtonClick", "onMarkAllAsReadClick", "initialFilterStatus", "FilterStatus", "All", "header", "renderHeader", "status", "setStatus", "useState", "feedClient", "useFeedStore", "useKnockFeed", "settings", "useFeedSettings", "t", "useTranslations", "pageInfo", "items", "networkStatus", "containerRef", "useRef", "useEffect", "fetch", "noItems", "length", "requestInFlight", "isRequestInFlight", "onBottomCallback", "useCallback", "after", "fetchNextPage", "useOnBottomScroll", "ref", "callback", "offset", "setFilterStatus", "filterStatus", "NetworkStatus", "loading", "map", "onItemClick", "onButtonClick", "fetchMore", "features", "branding_required"], "mappings": "kjBAkDMA,EAAqBC,GACxBC,EAAA,QAAA,cAAAC,EAAA,iBAAA,CAAiB,IAAKF,EAAMG,KAAKC,GAAI,GAAIJ,CAC3C,CAAA,EAEKK,EAAuBL,GAC1BC,EAAA,QAAA,cAAAK,yBAAA,CAA2BN,GAAAA,EAC7B,EAEKO,EAAiBA,CAAC,CAAEC,UAAAA,CAAoC,IAC3DP,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,0CAAA,0BACZQ,EACC,QAAA,CAAA,UAAW,EACX,KAAK,OACL,MAAOD,IAAc,OAAS,4BAA8BE,OAAU,CAE1E,EAGIC,EACJ,qGAEWC,EAAoDA,CAAC,CAChEC,eAAAA,0BAAkBC,EAAAA,UAAY,IAAA,EAC9BC,WAAAA,EAAahB,EACbiB,oBAAAA,EACAC,0BAAAA,EACAC,qBAAAA,EACAC,oBAAAA,EAAsBC,EAAaC,aAAAA,IACnCC,OAAAA,EACAC,aAAAA,EAAelB,CACjB,IAAM,CACJ,KAAM,CAACmB,EAAQC,CAAS,EAAIC,EAAAA,SAASP,CAAmB,EAClD,CAAEQ,WAAAA,EAAYC,aAAAA,EAAcpB,UAAAA,GAAcqB,eAAa,EACvD,CAAEC,SAAAA,CAAAA,EAAaC,EAAAA,gBAAgBJ,CAAU,EACzC,CAAEK,EAAAA,GAAMC,kBAAgB,EAExB,CAAEC,SAAAA,EAAUC,MAAAA,EAAOC,cAAAA,GAAkBR,EAAa,EAClDS,EAAeC,SAAuB,IAAI,EAEhDC,EAAAA,UAAU,IAAM,CACdd,EAAUN,CAAmB,CAAA,EAC5B,CAACA,CAAmB,CAAC,EAExBoB,EAAAA,UAAU,IAAM,CAEdZ,EAAWa,MAAM,CAAEhB,OAAAA,CAAAA,CAAQ,CAAA,EAC1B,CAACG,EAAYH,CAAM,CAAC,EAEjBiB,MAAAA,EAAUN,EAAMO,SAAW,EAC3BC,EAAkBC,oBAAkBR,CAAa,EAGjDS,EAAmBC,EAAAA,YAAY,IAAM,CACrC,CAACH,GAAmBT,EAASa,OAC/BpB,EAAWqB,cAAc,CAE1B,EAAA,CAACL,EAAiBT,EAAUP,CAAU,CAAC,EAIxB,OAAAsB,EAAA,CAChBC,IAAKb,EACLc,SAAUN,EACVO,OAAQ,EAAA,CACT,0BAGE,MACC,CAAA,UAAW,gDAAgD5C,CAAS,IAEnEc,GACCC,EAAa,CACX8B,gBAAiB5B,EACjB6B,aAAc9B,EACdN,qBAAAA,CAAAA,CACD,EAEHjB,EAAA,QAAA,cAAC,MAAI,CAAA,UAAU,mCAAmC,IAAKoC,CACpDD,EAAAA,IAAkBmB,EAAcC,cAAAA,SAC9BvD,EAAA,QAAA,cAAAM,EAAA,CAAe,UAAAC,CACjB,CAAA,EAEDP,EAAAA,QAAA,cAAC,MAAI,CAAA,UAAU,6CACZmC,EAAAA,IAAkBmB,EAAAA,cAAcC,SAC/BrB,EAAMsB,IAAKtD,GACTY,EAAW,CACTZ,KAAAA,EACAuD,YAAa1C,EACb2C,cAAe1C,CAAAA,CAChB,CACH,CACJ,EAECmB,IAAkBmB,EAAAA,cAAcK,WAC9B3D,EAAAA,QAAA,cAAAM,EAAA,CAAe,UAAAC,CACjB,CAAA,EAEA,CAACmC,GAAmBF,GAAW5B,CAClC,GAECiB,GAAAA,YAAAA,EAAU+B,SAASC,oBACjB7D,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,uCAAA,0BACZ,IAAE,CAAA,KAAMU,EAAmB,OAAO,UAChCqB,EAAE,WAAW,GAAK,kBACrB,CACF,CAEJ,CAEJ"}