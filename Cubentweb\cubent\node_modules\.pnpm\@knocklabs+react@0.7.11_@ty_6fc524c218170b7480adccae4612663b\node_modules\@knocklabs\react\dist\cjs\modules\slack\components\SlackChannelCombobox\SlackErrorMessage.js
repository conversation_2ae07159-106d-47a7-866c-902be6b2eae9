"use strict";const a=require("react"),r=require("@telegraph/icon"),c=require("@telegraph/typography"),o=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=o(a),l=({message:e})=>t.default.createElement("div",{className:"rsk-combobox__error"},t.default.createElement("span",null,t.default.createElement(r.Icon,{icon:r.Lucide.Info,color:"black",size:"1","aria-hidden":!0})),t.default.createElement(c.Text,{as:"div",color:"black",size:"1"},e));module.exports=l;
//# sourceMappingURL=SlackErrorMessage.js.map
