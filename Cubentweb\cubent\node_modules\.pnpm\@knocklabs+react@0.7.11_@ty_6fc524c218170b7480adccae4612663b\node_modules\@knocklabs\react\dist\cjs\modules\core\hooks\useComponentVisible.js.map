{"version": 3, "file": "useComponentVisible.js", "sources": ["../../../../../src/modules/core/hooks/useComponentVisible.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\n\nfunction contains(parent: HTMLElement | null, child: HTMLElement) {\n  if (!parent) return false;\n  return parent === child || parent.contains(child);\n}\n\ntype Options = {\n  closeOnClickOutside: boolean;\n};\n\nexport default function useComponentVisible(\n  isVisible: boolean,\n  onClose: (event: Event) => void,\n  options: Options,\n) {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const handleKeydown = (event: KeyboardEvent) => {\n    if (event.key === \"Escape\") {\n      onClose(event);\n    }\n  };\n\n  const handleClickOutside = (event: Event) => {\n    if (\n      options.closeOnClickOutside &&\n      !contains(ref.current, event.target as HTMLElement)\n    ) {\n      onClose(event);\n    }\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      document.addEventListener(\"keydown\", handleKeydown, true);\n      document.addEventListener(\"click\", handleClickOutside, true);\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeydown, true);\n      document.removeEventListener(\"click\", handleClickOutside, true);\n    };\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isVisible]);\n\n  return { ref };\n}\n"], "names": ["contains", "parent", "child", "useComponentVisible", "isVisible", "onClose", "options", "ref", "useRef", "handleKeydown", "event", "key", "handleClickOutside", "closeOnClickOutside", "current", "target", "useEffect", "addEventListener", "removeEventListener"], "mappings": "sCAEA,SAASA,EAASC,EAA4BC,EAAoB,CAC5D,OAACD,EACEA,IAAWC,GAASD,EAAOD,SAASE,CAAK,EAD5B,EAEtB,CAMwBC,SAAAA,EACtBC,EACAC,EACAC,EACA,CACMC,MAAAA,EAAMC,SAAuB,IAAI,EAEjCC,EAAiBC,GAAyB,CAC1CA,EAAMC,MAAQ,UAChBN,EAAQK,CAAK,CAEjB,EAEME,EAAsBF,GAAiB,CAEzCJ,EAAQO,qBACR,CAACb,EAASO,EAAIO,QAASJ,EAAMK,MAAqB,GAElDV,EAAQK,CAAK,CAEjB,EAEAM,OAAAA,EAAAA,UAAU,KACJZ,IACOa,SAAAA,iBAAiB,UAAWR,EAAe,EAAI,EAC/CQ,SAAAA,iBAAiB,QAASL,EAAoB,EAAI,GAGtD,IAAM,CACFM,SAAAA,oBAAoB,UAAWT,EAAe,EAAI,EAClDS,SAAAA,oBAAoB,QAASN,EAAoB,EAAI,CAChE,GAGC,CAACR,CAAS,CAAC,EAEP,CAAEG,IAAAA,CAAI,CACf"}