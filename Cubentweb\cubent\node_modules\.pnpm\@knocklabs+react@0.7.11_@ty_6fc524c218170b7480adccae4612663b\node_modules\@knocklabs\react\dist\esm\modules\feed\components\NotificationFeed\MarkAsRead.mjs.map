{"version": 3, "file": "MarkAsRead.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/MarkAsRead.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport * as React from \"react\";\n\nimport { CheckmarkCircle } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type MarkAsReadProps = {\n  onClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n};\n\nexport const MarkAsRead: React.FC<MarkAsReadProps> = ({ onClick }) => {\n  const { useFeedStore, feedClient, colorMode } = useKnockFeed();\n  const { t } = useTranslations();\n\n  const unreadItems = useFeedStore((state) =>\n    state.items.filter((item) => !item.read_at),\n  );\n\n  const unreadCount = useFeedStore((state) => state.metadata.unread_count);\n\n  const onClickHandler = React.useCallback(\n    (e: React.MouseEvent) => {\n      feedClient.markAllAsRead();\n      if (onClick) onClick(e, unreadItems);\n    },\n    [feedClient, unreadItems, onClick],\n  );\n\n  return (\n    <button\n      className={`rnf-mark-all-as-read rnf-mark-all-as-read--${colorMode}`}\n      disabled={unreadCount === 0}\n      onClick={onClickHandler}\n      type=\"button\"\n    >\n      {t(\"markAllAsRead\")}\n      <CheckmarkCircle aria-hidden />\n    </button>\n  );\n};\n"], "names": ["MarkAsRead", "onClick", "useFeedStore", "feedClient", "colorMode", "useKnockFeed", "t", "useTranslations", "unreadItems", "state", "items", "filter", "item", "read_at", "unreadCount", "metadata", "unread_count", "onClickHandler", "React", "useCallback", "e", "markAllAsRead", "CheckmarkCircle"], "mappings": ";;;;AAYO,MAAMA,IAAwCA,CAAC;AAAA,EAAEC,SAAAA;AAAQ,MAAM;AAC9D,QAAA;AAAA,IAAEC,cAAAA;AAAAA,IAAcC,YAAAA;AAAAA,IAAYC,WAAAA;AAAAA,MAAcC,EAAa,GACvD;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GAExBC,IAAcN,EAAcO,CAAAA,MAChCA,EAAMC,MAAMC,OAAQC,CAASA,MAAA,CAACA,EAAKC,OAAO,CAC5C,GAEMC,IAAcZ,EAAcO,CAAUA,MAAAA,EAAMM,SAASC,YAAY,GAEjEC,IAAiBC,EAAMC,YAC3B,CAACC,MAAwB;AACvBjB,IAAAA,EAAWkB,cAAc,GACrBpB,KAAiBmB,EAAAA,GAAGZ,CAAW;AAAA,EAErC,GAAA,CAACL,GAAYK,GAAaP,CAAO,CACnC;AAGE,SAAA,gBAAAiB,EAAA,cAAC,YACC,WAAW,8CAA8Cd,CAAS,IAClE,UAAUU,MAAgB,GAC1B,SAASG,GACT,MAAK,YAEJX,EAAE,eAAe,mCACjBgB,GAAgB,EAAA,eAAW,GAAA,CAAA,CAC9B;AAEJ;"}