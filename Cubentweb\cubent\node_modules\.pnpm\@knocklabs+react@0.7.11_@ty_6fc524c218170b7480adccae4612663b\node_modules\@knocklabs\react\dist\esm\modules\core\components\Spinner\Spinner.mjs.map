{"version": 3, "file": "Spinner.mjs", "sources": ["../../../../../../src/modules/core/components/Spinner/Spinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype Speed = \"fast\" | \"slow\" | \"medium\";\n\nfunction speedSwitch(speed: Speed) {\n  if (speed === \"fast\") return 600;\n  if (speed === \"slow\") return 900;\n  return 750;\n}\n\nexport interface SpinnerProps {\n  color?: string;\n  speed?: Speed;\n  gap?: number;\n  thickness?: number;\n  size?: string;\n}\n\nexport const Spinner: FunctionComponent<SpinnerProps> = ({\n  color = \"rgba(0,0,0,0.4)\",\n  speed = \"medium\",\n  gap = 4,\n  thickness = 4,\n  size = \"1em\",\n  ...props\n}) => (\n  <svg\n    height={size}\n    width={size}\n    {...props}\n    style={{ animationDuration: `${speedSwitch(speed)}ms` }}\n    className=\"__react-svg-spinner_circle\"\n    role=\"img\"\n    aria-labelledby=\"title desc\"\n    viewBox=\"0 0 32 32\"\n  >\n    <title id=\"title\">Circle loading spinner</title>\n    <desc id=\"desc\">Image of a partial circle indicating \"loading.\"</desc>\n    <style\n      dangerouslySetInnerHTML={{\n        __html: `\n      .__react-svg-spinner_circle{\n          transition-property: transform;\n          animation-name: __react-svg-spinner_infinite-spin;\n          animation-iteration-count: infinite;\n          animation-timing-function: linear;\n      }\n      @keyframes __react-svg-spinner_infinite-spin {\n          from {transform: rotate(0deg)}\n          to {transform: rotate(360deg)}\n      }\n    `,\n      }}\n    />\n    <circle\n      role=\"presentation\"\n      cx={16}\n      cy={16}\n      r={14 - thickness / 2}\n      stroke={color}\n      fill=\"none\"\n      strokeWidth={thickness}\n      strokeDasharray={Math.PI * 2 * (11 - gap)}\n      strokeLinecap=\"round\"\n    />\n  </svg>\n);\n"], "names": ["speedSwitch", "speed", "Spinner", "color", "gap", "thickness", "size", "props", "React", "animationDuration", "__html", "Math", "PI"], "mappings": ";AAIA,SAASA,EAAYC,GAAc;AAC7BA,SAAAA,MAAU,SAAe,MACzBA,MAAU,SAAe,MACtB;AACT;AAUO,MAAMC,IAA2CA,CAAC;AAAA,EACvDC,OAAAA,IAAQ;AAAA,EACRF,OAAAA,IAAQ;AAAA,EACRG,KAAAA,IAAM;AAAA,EACNC,WAAAA,IAAY;AAAA,EACZC,MAAAA,IAAO;AAAA,EACP,GAAGC;AACL,MACEC,gBAAAA,EAAA,cAAC,SACC,QAAQF,GACR,OAAOA,GACP,GAAIC,GACJ,OAAO;AAAA,EAAEE,mBAAmB,GAAGT,EAAYC,CAAK,CAAC;AAAK,GACtD,WAAU,8BACV,MAAK,OACL,mBAAgB,cAChB,SAAQ,YAER,GAAAO,gBAAAA,EAAA,cAAC,SAAM,EAAA,IAAG,WAAQ,wBAAsB,mCACvC,QAAK,EAAA,IAAG,UAAO,iDAA+C,GAC9DA,gBAAAA,EAAA,cAAA,SAAA,EACC,yBAAyB;AAAA,EACvBE,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYV,EAAE,CAAA,GAEHF,gBAAAA,EAAA,cAAA,UAAA,EACC,MAAK,gBACL,IAAI,IACJ,IAAI,IACJ,GAAG,KAAKH,IAAY,GACpB,QAAQF,GACR,MAAK,QACL,aAAaE,GACb,iBAAiBM,KAAKC,KAAK,KAAK,KAAKR,IACrC,eAAc,QAAA,CAAO,CAEzB;"}