import e, { useEffect as f } from "react";
import { useTranslations as g, useKnockClient as T, useKnockMsTeamsClient as x, useMsTeamsAuth as M } from "@knocklabs/react-core";
import { openPopupWindow as d } from "../../../core/utils.mjs";
/* empty css               */
import { MsTeamsIcon as a } from "../MsTeamsIcon/MsTeamsIcon.mjs";
/* empty css            */
const K = ({
  msTeamsBotId: _,
  redirectUrl: b,
  onAuthenticationComplete: c
}) => {
  const {
    t
  } = g(), m = T(), {
    setConnectionStatus: o,
    connectionStatus: n,
    setActionLabel: s,
    actionLabel: i,
    errorLabel: p
  } = x(), {
    buildMsTeamsAuthUrl: l,
    disconnectFromMsTeams: E
  } = M(_, b);
  f(() => {
    const u = (r) => {
      if (r.origin === m.host)
        try {
          r.data === "authComplete" && o("connected"), r.data === "authFailed" && o("error"), c == null || c(r.data);
        } catch {
          o("error");
        }
    };
    return window.addEventListener("message", u, !1), () => {
      window.removeEventListener("message", u);
    };
  }, [m.host, c, o]);
  const k = t("msTeamsDisconnect") || null, h = t("msTeamsReconnect") || null;
  return n === "connecting" || n === "disconnecting" ? /* @__PURE__ */ e.createElement("div", { className: "rtk-connect__button rtk-connect__button--loading" }, /* @__PURE__ */ e.createElement(a, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", null, t(n === "connecting" ? "msTeamsConnecting" : "msTeamsDisconnecting"))) : n === "error" ? /* @__PURE__ */ e.createElement("button", { onClick: () => d(l()), className: "rtk-connect__button rtk-connect__button--error", onMouseEnter: () => s(h), onMouseLeave: () => s(null) }, /* @__PURE__ */ e.createElement(a, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", { className: "rtk-connect__button__text--error" }, i || p || t("msTeamsError"))) : n === "disconnected" ? /* @__PURE__ */ e.createElement("button", { onClick: () => d(l()), className: "rtk-connect__button rtk-connect__button--disconnected" }, /* @__PURE__ */ e.createElement(a, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", null, t("msTeamsConnect"))) : /* @__PURE__ */ e.createElement("button", { onClick: E, className: "rtk-connect__button rtk-connect__button--connected", onMouseEnter: () => s(k), onMouseLeave: () => s(null) }, /* @__PURE__ */ e.createElement(a, { height: "16px", width: "16px" }), /* @__PURE__ */ e.createElement("span", { className: "rtk-connect__button__text--connected" }, i || t("msTeamsConnected")));
};
export {
  K as MsTeamsAuthButton
};
//# sourceMappingURL=MsTeamsAuthButton.mjs.map
