import { FeedItem } from '@knocklabs/client';
import { FilterStatus } from '@knocklabs/react-core';
import { default as React, SetStateAction } from 'react';
export type NotificationFeedHeaderProps = {
    filterStatus: FilterStatus;
    setFilterStatus: React.Dispatch<SetStateAction<FilterStatus>>;
    onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;
};
export declare const NotificationFeedHeader: React.FC<NotificationFeedHeaderProps>;
//# sourceMappingURL=NotificationFeedHeader.d.ts.map