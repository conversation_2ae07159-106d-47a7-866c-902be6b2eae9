import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { z } from 'zod';

const heartbeatSchema = z.object({
  sessionId: z.string(),
  extensionVersion: z.string().optional(),
  vscodeVersion: z.string().optional(),
  platform: z.string().optional(),
});

/**
 * Extension heartbeat endpoint - called when extension starts or is active
 * This helps the webapp detect that the extension is connected
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    let userId: string | null = null;
    
    // Check for Bearer token in Authorization header (extension)
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Find the pending login with this token to get the user
      const pendingLogin = await database.pendingLogin.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() }, // Not expired
        },
      });

      if (pendingLogin) {
        // Token is valid, but we need to get the user ID from the session
        try {
          const { clerkClient } = await import('@repo/auth/server');
          const client = await clerkClient();
          const session = await client.sessions.getSession(token);
          userId = session.userId;
        } catch (error) {
          console.log('Invalid token in heartbeat:', error);
        }
      }
    } else {
      // Fallback to regular Clerk auth for web requests
      try {
        const authResult = await auth();
        userId = authResult.userId;
      } catch (error) {
        console.log('No auth in heartbeat:', error);
      }
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { sessionId, extensionVersion, vscodeVersion, platform } = heartbeatSchema.parse(body);

    // Find user in database
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create or update extension session
    await database.extensionSession.upsert({
      where: {
        userId_sessionId: {
          userId: dbUser.id,
          sessionId,
        },
      },
      update: {
        isActive: true,
        lastActiveAt: new Date(),
        extensionVersion,
        vscodeVersion,
        platform,
      },
      create: {
        userId: dbUser.id,
        sessionId,
        isActive: true,
        lastActiveAt: new Date(),
        extensionVersion,
        vscodeVersion,
        platform,
        tokensUsed: 0,
        requestsMade: 0,
      },
    });

    // Update user's last extension sync
    await database.user.update({
      where: { id: dbUser.id },
      data: {
        lastExtensionSync: new Date(),
        lastActiveAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Heartbeat received',
      timestamp: new Date().toISOString(),
      user: {
        id: dbUser.id,
        subscriptionTier: dbUser.subscriptionTier,
        subscriptionStatus: dbUser.subscriptionStatus,
        cubentUnitsUsed: dbUser.cubentUnitsUsed,
        cubentUnitsLimit: dbUser.cubentUnitsLimit,
      },
    });

  } catch (error) {
    console.error('Extension heartbeat error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
