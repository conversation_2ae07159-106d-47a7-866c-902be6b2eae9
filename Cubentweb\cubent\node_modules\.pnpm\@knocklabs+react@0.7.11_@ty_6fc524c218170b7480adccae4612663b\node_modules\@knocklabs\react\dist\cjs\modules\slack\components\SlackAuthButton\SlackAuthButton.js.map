{"version": 3, "file": "SlackAuthButton.js", "sources": ["../../../../../../src/modules/slack/components/SlackAuthButton/SlackAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockSlackClient,\n  useSlackAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useMemo } from \"react\";\nimport { useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthButtonProps {\n  slackClientId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n  // When provided, the default scopes will be overridden with the provided scopes\n  scopes?: string[];\n  // Additional scopes to add to the default scopes\n  additionalScopes?: string[];\n}\n\nexport const SlackAuthButton: FunctionComponent<SlackAuthButtonProps> = ({\n  slackClientId,\n  redirectUrl,\n  onAuthenticationComplete,\n  scopes,\n  additionalScopes,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockSlackClient();\n\n  const useSlackAuthOptions = useMemo(\n    () => ({\n      scopes,\n      additionalScopes,\n    }),\n    [scopes, additionalScopes],\n  );\n\n  const { buildSlackAuthUrl, disconnectFromSlack } = useSlackAuth(\n    slackClientId,\n    redirectUrl,\n    useSlackAuthOptions,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        if (onAuthenticationComplete) {\n          onAuthenticationComplete(event.data);\n        }\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"slackDisconnect\") || null;\n  const reconnectLabel = t(\"slackReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rsk-connect__button rsk-connect__button--loading\">\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"slackConnecting\")\n            : t(\"slackDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rsk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"slackError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--disconnected\"\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"slackConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromSlack}\n      className=\"rsk-connect__button rsk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <SlackIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rsk-connect__button__text--connected\">\n        {actionLabel || t(\"slackConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["SlackAuthButton", "slackClientId", "redirectUrl", "onAuthenticationComplete", "scopes", "additionalScopes", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockSlackClient", "useSlackAuthOptions", "useMemo", "buildSlackAuthUrl", "disconnectFromSlack", "useSlackAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "SlackIcon", "openPopupWindow"], "mappings": "2VAyBaA,EAA2DA,CAAC,CACvEC,cAAAA,EACAC,YAAAA,EACAC,yBAAAA,EACAC,OAAAA,EACAC,iBAAAA,CACF,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxBC,EAAQC,EAAAA,eAAe,EAEvB,CACJC,oBAAAA,EACAC,iBAAAA,EACAC,eAAAA,EACAC,YAAAA,EACAC,WAAAA,GACEC,sBAAoB,EAElBC,EAAsBC,EAAAA,QAC1B,KAAO,CACLb,OAAAA,EACAC,iBAAAA,CAAAA,GAEF,CAACD,EAAQC,CAAgB,CAC3B,EAEM,CAAEa,kBAAAA,EAAmBC,oBAAAA,CAAwBC,EAAAA,eACjDnB,EACAC,EACAc,CACF,EAEAK,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAkBC,GAAwB,CAC1CA,GAAAA,EAAMC,SAAWhB,EAAMiB,KAIvB,GAAA,CACEF,EAAMG,OAAS,gBACjBhB,EAAoB,WAAW,EAG7Ba,EAAMG,OAAS,cACjBhB,EAAoB,OAAO,EAGzBP,GACFA,EAAyBoB,EAAMG,IAAI,OAEtB,CACfhB,EAAoB,OAAO,CAAA,CAE/B,EAEOiB,cAAAA,iBAAiB,UAAWL,EAAgB,EAAK,EAGjD,IAAM,CACJM,OAAAA,oBAAoB,UAAWN,CAAc,CACtD,GACC,CAACd,EAAMiB,KAAMtB,EAA0BO,CAAmB,CAAC,EAExDmB,MAAAA,EAAkBvB,EAAE,iBAAiB,GAAK,KAC1CwB,EAAiBxB,EAAE,gBAAgB,GAAK,KAI5CK,OAAAA,IAAqB,cACrBA,IAAqB,gBAGnBoB,EAAA,QAAA,cAAC,OAAI,UAAU,kDAAA,0BACZC,EAAU,UAAA,CAAA,OAAO,OAAO,MAAM,MAAM,CAAA,EACpCD,EAAAA,QAAA,cAAA,OAAA,KAEKzB,EADHK,IAAqB,aAChB,kBACA,oBADiB,CAEzB,CACF,EAKAA,IAAqB,gCAEpB,SACC,CAAA,QAAS,IAAMsB,EAAAA,gBAAgBf,GAAmB,EAClD,UAAU,iDACV,aAAc,IAAMN,EAAekB,CAAc,EACjD,aAAc,IAAMlB,EAAe,IAAI,CAAA,0BAEtCoB,EAAAA,UAAU,CAAA,OAAO,OAAO,MAAM,OAAM,EACrCD,UAAA,cAAC,OAAK,CAAA,UAAU,oCACblB,GAAeC,GAAcR,EAAE,YAAY,CAC9C,CACF,EAKAK,IAAqB,eAErBoB,EAAA,QAAA,cAAC,UACC,QAAS,IAAME,EAAAA,gBAAgBf,EAAkB,CAAC,EAClD,UAAU,uDAEV,EAAAa,EAAA,QAAA,cAACC,aAAU,OAAO,OAAO,MAAM,MAAM,CAAA,0BACpC,OAAM1B,KAAAA,EAAE,cAAc,CAAE,CAC3B,EAMDyB,EAAAA,QAAA,cAAA,SAAA,CACC,QAASZ,EACT,UAAU,qDACV,aAAc,IAAMP,EAAeiB,CAAe,EAClD,aAAc,IAAMjB,EAAe,IAAI,GAEvCmB,EAAAA,QAAA,cAACC,EAAAA,UAAU,CAAA,OAAO,OAAO,MAAM,MAAM,CAAA,EACrCD,EAAA,QAAA,cAAC,OAAK,CAAA,UAAU,sCACblB,EAAAA,GAAeP,EAAE,gBAAgB,CACpC,CACF,CAEJ"}