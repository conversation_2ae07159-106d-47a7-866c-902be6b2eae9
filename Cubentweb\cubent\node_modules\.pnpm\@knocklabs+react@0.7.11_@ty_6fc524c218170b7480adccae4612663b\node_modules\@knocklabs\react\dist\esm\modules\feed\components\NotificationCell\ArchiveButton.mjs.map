{"version": 3, "file": "ArchiveButton.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/ArchiveButton.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { createPopper } from \"@popperjs/core\";\nimport { MouseEvent, useCallback, useEffect, useRef, useState } from \"react\";\n\nimport { CloseCircle } from \"../../../core/components/Icons\";\n\nexport interface ArchiveButtonProps {\n  item: FeedItem;\n}\n\nconst ArchiveButton: React.FC<ArchiveButtonProps> = ({ item }) => {\n  const { colorMode, feedClient } = useKnockFeed();\n  const { t } = useTranslations();\n  const [visible, setVisible] = useState(false);\n  const triggerRef = useRef<HTMLButtonElement>(null);\n  const tooltipRef = useRef<HTMLDivElement>(null);\n\n  const onClick = useCallback(\n    (e: MouseEvent<HTMLButtonElement>) => {\n      e.preventDefault();\n      e.stopPropagation();\n\n      feedClient.markAsArchived(item);\n    },\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [item],\n  );\n\n  useEffect(() => {\n    if (triggerRef.current && tooltipRef.current && visible) {\n      const popperInstance = createPopper(\n        triggerRef.current,\n        tooltipRef.current,\n        {\n          placement: \"top-end\",\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [visible]);\n\n  return (\n    <button\n      ref={triggerRef}\n      onClick={onClick}\n      onMouseEnter={() => setVisible(true)}\n      onMouseLeave={() => setVisible(false)}\n      type=\"button\"\n      aria-label={t(\"archiveNotification\")}\n      className={`rnf-archive-notification-btn rnf-archive-notification-btn--${colorMode}`}\n    >\n      <CloseCircle aria-hidden />\n\n      {visible && (\n        <div\n          ref={tooltipRef}\n          className={`rnf-tooltip rnf-tooltip--${colorMode}`}\n        >\n          {t(\"archiveNotification\")}\n        </div>\n      )}\n    </button>\n  );\n};\n\nexport { ArchiveButton };\n"], "names": ["ArchiveButton", "item", "colorMode", "feedClient", "useKnockFeed", "t", "useTranslations", "visible", "setVisible", "useState", "triggerRef", "useRef", "tooltipRef", "onClick", "useCallback", "e", "preventDefault", "stopPropagation", "markAsArchived", "useEffect", "current", "popperInstance", "createPopper", "placement", "modifiers", "name", "options", "offset", "destroy", "React", "CloseCircle"], "mappings": ";;;;AAWA,MAAMA,IAA8CA,CAAC;AAAA,EAAEC,MAAAA;AAAK,MAAM;AAC1D,QAAA;AAAA,IAAEC,WAAAA;AAAAA,IAAWC,YAAAA;AAAAA,MAAeC,EAAa,GACzC;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GACxB,CAACC,GAASC,CAAU,IAAIC,EAAS,EAAK,GACtCC,IAAaC,EAA0B,IAAI,GAC3CC,IAAaD,EAAuB,IAAI,GAExCE,IAAUC;AAAAA,IACd,CAACC,MAAqC;AACpCA,QAAEC,eAAe,GACjBD,EAAEE,gBAAgB,GAElBd,EAAWe,eAAejB,CAAI;AAAA,IAChC;AAAA;AAAA;AAAA,IAGA,CAACA,CAAI;AAAA,EACP;AAEAkB,SAAAA,EAAU,MAAM;AACd,QAAIT,EAAWU,WAAWR,EAAWQ,WAAWb,GAAS;AACvD,YAAMc,IAAiBC,EACrBZ,EAAWU,SACXR,EAAWQ,SACX;AAAA,QACEG,WAAW;AAAA,QACXC,WAAW,CACT;AAAA,UACEC,MAAM;AAAA,UACNC,SAAS;AAAA,YACPC,QAAQ,CAAC,GAAG,CAAC;AAAA,UAAA;AAAA,QAEhB,CAAA;AAAA,MAAA,CAGP;AAEA,aAAO,MAAM;AACXN,QAAAA,EAAeO,QAAQ;AAAA,MACzB;AAAA,IAAA;AAAA,EACF,GACC,CAACrB,CAAO,CAAC,mCAGT,UACC,EAAA,KAAKG,GACL,SAAAG,GACA,cAAc,MAAML,EAAW,EAAI,GACnC,cAAc,MAAMA,EAAW,EAAK,GACpC,MAAK,UACL,cAAYH,EAAE,qBAAqB,GACnC,WAAW,8DAA8DH,CAAS,MAElF2B,gBAAAA,EAAA,cAACC,KAAY,eAAW,IAAA,GAEvBvB,KACCsB,gBAAAA,EAAA,cAAC,SACC,KAAKjB,GACL,WAAW,4BAA4BV,CAAS,MAE/CG,EAAE,qBAAqB,CAC1B,CAEJ;AAEJ;"}