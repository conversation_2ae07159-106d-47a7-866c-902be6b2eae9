import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { calculateCubentUnits, canSendMessage } from '@repo/database/src/cubent-units';
import { z } from 'zod';

const trackUnitsSchema = z.object({
  modelId: z.string(),
  messageContent: z.string(),
  hasImages: z.boolean().optional().default(false),
  sessionId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    let userId: string | null = null;
    
    // Check for Bearer token in Authorization header (extension)
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Find the pending login with this token to get the user
      const pendingLogin = await database.pendingLogin.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() }, // Not expired
        },
      });

      if (pendingLogin) {
        // Token is valid, get user ID from Clerk session
        try {
          const { clerkClient } = await import('@repo/auth/server');
          const client = await clerkClient();
          const session = await client.sessions.getSession(token);
          userId = session.userId;
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid token' },
            { status: 401 }
          );
        }
      } else {
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        );
      }
    } else {
      // Fallback to regular Clerk auth for web requests
      const authResult = await auth();
      userId = authResult.userId;
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validation = trackUnitsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { modelId, messageContent, hasImages, sessionId } = validation.data;

    // Find or create user in database
    let dbUser = await database.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      // Get user info from Clerk
      const { clerkClient } = await import('@repo/auth/server');
      const client = await clerkClient();
      const clerkUser = await client.users.getUser(userId);

      // Create user in database
      dbUser = await database.user.create({
        data: {
          clerkId: userId,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          name: clerkUser.fullName,
          picture: clerkUser.imageUrl,
          cubentUnitsUsed: 0,
          cubentUnitsLimit: 50, // Default limit
        },
      });
    }

    // Calculate Cubent units for this message
    const unitsRequired = calculateCubentUnits(modelId, messageContent, hasImages);

    // Check if user has enough units
    if (!canSendMessage(dbUser.cubentUnitsUsed, dbUser.cubentUnitsLimit, modelId, messageContent, hasImages)) {
      return NextResponse.json(
        { 
          error: 'Insufficient units',
          message: `This message requires ${unitsRequired} Cubent units, but you only have ${dbUser.cubentUnitsLimit - dbUser.cubentUnitsUsed} units remaining.`,
          unitsRequired,
          unitsRemaining: dbUser.cubentUnitsLimit - dbUser.cubentUnitsUsed,
        },
        { status: 402 } // Payment Required
      );
    }

    // Update user's unit usage
    const updatedUser = await database.user.update({
      where: { id: dbUser.id },
      data: {
        cubentUnitsUsed: dbUser.cubentUnitsUsed + unitsRequired,
        lastActiveAt: new Date(),
      },
    });

    // Record usage in metrics
    await database.usageMetrics.create({
      data: {
        userId: dbUser.id,
        tokensUsed: 0, // We're tracking units now, not tokens
        cubentUnitsUsed: unitsRequired,
        requestsMade: 1,
        costAccrued: 0, // Cost is handled by Cubent units
        date: new Date(),
      },
    });

    // Record detailed analytics
    await database.usageAnalytics.create({
      data: {
        userId: dbUser.id,
        modelId,
        tokensUsed: 0,
        cubentUnitsUsed: unitsRequired,
        requestsMade: 1,
        costAccrued: 0,
        sessionId,
        metadata: {
          messageLength: messageContent.length,
          hasImages,
          timestamp: new Date().toISOString(),
        },
      },
    });

    return NextResponse.json({
      success: true,
      unitsUsed: unitsRequired,
      totalUnitsUsed: updatedUser.cubentUnitsUsed,
      unitsRemaining: updatedUser.cubentUnitsLimit - updatedUser.cubentUnitsUsed,
      unitsLimit: updatedUser.cubentUnitsLimit,
      usagePercentage: (updatedUser.cubentUnitsUsed / updatedUser.cubentUnitsLimit) * 100,
    });

  } catch (error) {
    console.error('Unit tracking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
