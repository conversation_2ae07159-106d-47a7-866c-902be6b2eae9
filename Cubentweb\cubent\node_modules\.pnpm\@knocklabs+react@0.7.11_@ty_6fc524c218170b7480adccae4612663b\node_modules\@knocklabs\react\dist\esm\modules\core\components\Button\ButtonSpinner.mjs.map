{"version": 3, "file": "ButtonSpinner.mjs", "sources": ["../../../../../../src/modules/core/components/Button/ButtonSpinner.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../Spinner\";\n\nimport \"./styles.css\";\n\ntype ButtonSpinnerProps = {\n  hasLabel: boolean;\n};\n\nexport const ButtonSpinner: FunctionComponent<ButtonSpinnerProps> = ({\n  hasLabel,\n}) => (\n  <div\n    className={`rnf-button-spinner rnf-button-spinner--${\n      hasLabel ? \"with-label\" : \"without-label\"\n    }`}\n  >\n    <Spinner />\n  </div>\n);\n"], "names": ["<PERSON>ton<PERSON><PERSON>ner", "<PERSON><PERSON><PERSON><PERSON>", "React", "Spinner"], "mappings": ";;;AAUO,MAAMA,IAAuDA,CAAC;AAAA,EACnEC,UAAAA;AACF,MACEC,gBAAAA,EAAA,cAAC,OACC,EAAA,WAAW,0CACTD,IAAW,eAAe,eAAe,GAAA,GAG1CC,gBAAAA,EAAA,cAAAC,GAAA,IAAO,CACV;"}