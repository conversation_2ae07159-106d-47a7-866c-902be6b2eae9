{"version": 3, "file": "SlackIcon.js", "sources": ["../../../../../../src/modules/slack/components/SlackIcon/SlackIcon.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\nexport interface SlackIconProps {\n  height: string;\n  width: string;\n}\n\nexport const SlackIcon: FunctionComponent<SlackIconProps> = ({\n  height,\n  width,\n}) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      style={{ height, width }}\n      viewBox=\"0 0 122.8 122.8\"\n    >\n      <path\n        d=\"M25.8 77.6c0 7.1-5.8 12.9-12.9 12.9S0 84.7 0 77.6s5.8-12.9 12.9-12.9h12.9v12.9zm6.5 0c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9v32.3c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V77.6z\"\n        fill=\"#e01e5a\"\n      ></path>\n      <path\n        d=\"M45.2 25.8c-7.1 0-12.9-5.8-12.9-12.9S38.1 0 45.2 0s12.9 5.8 12.9 12.9v12.9H45.2zm0 6.5c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H12.9C5.8 58.1 0 52.3 0 45.2s5.8-12.9 12.9-12.9h32.3z\"\n        fill=\"#36c5f0\"\n      ></path>\n      <path\n        d=\"M97 45.2c0-7.1 5.8-12.9 12.9-12.9s12.9 5.8 12.9 12.9-5.8 12.9-12.9 12.9H97V45.2zm-6.5 0c0 7.1-5.8 12.9-12.9 12.9s-12.9-5.8-12.9-12.9V12.9C64.7 5.8 70.5 0 77.6 0s12.9 5.8 12.9 12.9v32.3z\"\n        fill=\"#2eb67d\"\n      ></path>\n      <path\n        d=\"M77.6 97c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9-12.9-5.8-12.9-12.9V97h12.9zm0-6.5c-7.1 0-12.9-5.8-12.9-12.9s5.8-12.9 12.9-12.9h32.3c7.1 0 12.9 5.8 12.9 12.9s-5.8 12.9-12.9 12.9H77.6z\"\n        fill=\"#ecb22e\"\n      ></path>\n    </svg>\n  );\n};\n"], "names": ["SlackIcon", "height", "width", "React"], "mappings": "wKAOaA,EAA+CA,CAAC,CAC3DC,OAAAA,EACAC,MAAAA,CACF,IAEKC,EAAAA,QAAA,cAAA,MAAA,CACC,MAAM,6BACN,MAAO,CAAEF,OAAAA,EAAQC,MAAAA,CAAAA,EACjB,QAAQ,mBAERC,EAAAA,QAAA,cAAC,OACC,CAAA,EAAE,4LACF,KAAK,SACN,CAAA,EACAA,UAAA,cAAA,OAAA,CACC,EAAE,2LACF,KAAK,SACN,CAAA,EACAA,EAAA,QAAA,cAAA,OAAA,CACC,EAAE,4LACF,KAAK,SAAA,CACN,EACDA,UAAA,cAAC,OACC,CAAA,EAAE,+LACF,KAAK,UACN,CACH"}