{"version": 3, "file": "SlackAddChannelInput.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackAddChannelInput/SlackAddChannelInput.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport { useTranslations } from \"@knocklabs/react-core\";\nimport { useState } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../../../core\";\nimport \"../../theme.css\";\nimport ConnectionErrorInfoBoxes from \"../SlackChannelCombobox/SlackConnectionError\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAddChannelInputProps {\n  inErrorState: boolean;\n  connectedChannels: SlackChannelConnection[];\n  updateConnectedChannels: (channels: SlackChannelConnection[]) => void;\n  connectedChannelsError: string | null;\n  connectedChannelsUpdating: boolean;\n}\n\nconst SlackAddChannelInput: FunctionComponent<SlackAddChannelInputProps> = ({\n  inErrorState,\n  connectedChannels = [],\n  updateConnectedChannels,\n  connectedChannelsError,\n  connectedChannelsUpdating,\n}) => {\n  const { t } = useTranslations();\n  const [value, setValue] = useState<string | null>(null);\n  const [localError, setLocalError] = useState<string | null>(null);\n\n  const submitChannel = () => {\n    if (!value) {\n      return;\n    }\n\n    if (localError) {\n      setLocalError(null);\n    }\n\n    const alreadyConnected = connectedChannels.find(\n      (channel) => channel.channel_id === value,\n    );\n\n    if (alreadyConnected) {\n      setValue(\"\");\n      return setLocalError(t(\"slackChannelAlreadyConnected\") || \"\");\n    }\n\n    const channelsToSendToKnock = [...connectedChannels, { channel_id: value }];\n    updateConnectedChannels(channelsToSendToKnock);\n    setValue(\"\");\n  };\n\n  return (\n    <div className=\"rsk-connect-channel\">\n      <input\n        className={`rsk-connect-channel__input ${(inErrorState || !!localError) && !value && \"rsk-connect-channel__input--error\"}`}\n        tabIndex={-1}\n        id=\"slack-channel-search\"\n        type=\"text\"\n        placeholder={\n          localError || connectedChannelsError || t(\"slackChannelId\")\n        }\n        onChange={(e) => setValue(e.target.value)}\n        value={value || \"\"}\n      />\n      <button className=\"rsk-connect-channel__button\" onClick={submitChannel}>\n        {connectedChannelsUpdating ? (\n          <Spinner size=\"15px\" thickness={3} />\n        ) : (\n          <SlackIcon height=\"16px\" width=\"16px\" />\n        )}\n        {t(\"slackConnectChannel\")}\n      </button>\n      <ConnectionErrorInfoBoxes />\n    </div>\n  );\n};\n\nexport default SlackAddChannelInput;\n"], "names": ["SlackAddChannelInput", "inErrorState", "connectedChannels", "updateConnectedChannels", "connectedChannelsError", "connectedChannelsUpdating", "t", "useTranslations", "value", "setValue", "useState", "localError", "setLocalError", "submitChannel", "find", "channel", "channel_id", "channelsToSendToKnock", "React", "e", "target", "Spinner", "SlackIcon", "ConnectionErrorInfoBoxes"], "mappings": ";;;;;;;;;AAoBA,MAAMA,IAAqEA,CAAC;AAAA,EAC1EC,cAAAA;AAAAA,EACAC,mBAAAA,IAAoB,CAAE;AAAA,EACtBC,yBAAAA;AAAAA,EACAC,wBAAAA;AAAAA,EACAC,2BAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC;AAAAA,MAAMC,EAAgB,GACxB,CAACC,GAAOC,CAAQ,IAAIC,EAAwB,IAAI,GAChD,CAACC,GAAYC,CAAa,IAAIF,EAAwB,IAAI,GAE1DG,IAAgBA,MAAM;AAC1B,QAAI,CAACL;AACH;AAWF,QARIG,KACFC,EAAc,IAAI,GAGKV,EAAkBY,KACxCC,CAAYA,MAAAA,EAAQC,eAAeR,CACtC;AAGEC,aAAAA,EAAS,EAAE,GACJG,EAAcN,EAAE,8BAA8B,KAAK,EAAE;AAGxDW,UAAAA,IAAwB,CAAC,GAAGf,GAAmB;AAAA,MAAEc,YAAYR;AAAAA,IAAAA,CAAO;AAC1EL,IAAAA,EAAwBc,CAAqB,GAC7CR,EAAS,EAAE;AAAA,EACb;AAEA,SACGS,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,sBAAA,GACZA,gBAAAA,EAAA,cAAA,SAAA,EACC,WAAW,+BAA+BjB,KAAgB,CAAC,CAACU,MAAe,CAACH,KAAS,mCAAmC,IACxH,UAAU,IACV,IAAG,wBACH,MAAK,QACL,aACEG,KAAcP,KAA0BE,EAAE,gBAAgB,GAE5D,UAAWa,CAAAA,MAAMV,EAASU,EAAEC,OAAOZ,KAAK,GACxC,OAAOA,KAAS,IAAG,GAEpBU,gBAAAA,EAAA,cAAA,UAAA,EAAO,WAAU,+BAA8B,SAASL,EAAAA,GACtDR,IACCa,gBAAAA,EAAA,cAACG,KAAQ,MAAK,QAAO,WAAW,EAAA,CAAK,IAErCH,gBAAAA,EAAA,cAACI,GAAU,EAAA,QAAO,QAAO,OAAM,OAAA,CAChC,GACAhB,EAAE,qBAAqB,CAC1B,GACAY,gBAAAA,EAAA,cAACK,OAAwB,CAC3B;AAEJ;"}