{"version": 3, "file": "ArchiveButton.js", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/ArchiveButton.tsx"], "sourcesContent": ["import { FeedItem } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { createPopper } from \"@popperjs/core\";\nimport { MouseEvent, useCallback, useEffect, useRef, useState } from \"react\";\n\nimport { CloseCircle } from \"../../../core/components/Icons\";\n\nexport interface ArchiveButtonProps {\n  item: FeedItem;\n}\n\nconst ArchiveButton: React.FC<ArchiveButtonProps> = ({ item }) => {\n  const { colorMode, feedClient } = useKnockFeed();\n  const { t } = useTranslations();\n  const [visible, setVisible] = useState(false);\n  const triggerRef = useRef<HTMLButtonElement>(null);\n  const tooltipRef = useRef<HTMLDivElement>(null);\n\n  const onClick = useCallback(\n    (e: MouseEvent<HTMLButtonElement>) => {\n      e.preventDefault();\n      e.stopPropagation();\n\n      feedClient.markAsArchived(item);\n    },\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [item],\n  );\n\n  useEffect(() => {\n    if (triggerRef.current && tooltipRef.current && visible) {\n      const popperInstance = createPopper(\n        triggerRef.current,\n        tooltipRef.current,\n        {\n          placement: \"top-end\",\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [visible]);\n\n  return (\n    <button\n      ref={triggerRef}\n      onClick={onClick}\n      onMouseEnter={() => setVisible(true)}\n      onMouseLeave={() => setVisible(false)}\n      type=\"button\"\n      aria-label={t(\"archiveNotification\")}\n      className={`rnf-archive-notification-btn rnf-archive-notification-btn--${colorMode}`}\n    >\n      <CloseCircle aria-hidden />\n\n      {visible && (\n        <div\n          ref={tooltipRef}\n          className={`rnf-tooltip rnf-tooltip--${colorMode}`}\n        >\n          {t(\"archiveNotification\")}\n        </div>\n      )}\n    </button>\n  );\n};\n\nexport { ArchiveButton };\n"], "names": ["ArchiveButton", "item", "colorMode", "feedClient", "useKnockFeed", "t", "useTranslations", "visible", "setVisible", "useState", "triggerRef", "useRef", "tooltipRef", "onClick", "useCallback", "e", "preventDefault", "stopPropagation", "markAsArchived", "useEffect", "current", "popperInstance", "createPopper", "placement", "modifiers", "name", "options", "offset", "destroy", "React", "CloseCircle"], "mappings": "kSAWMA,EAA8CA,CAAC,CAAEC,KAAAA,CAAK,IAAM,CAC1D,KAAA,CAAEC,UAAAA,EAAWC,WAAAA,GAAeC,eAAa,EACzC,CAAEC,EAAAA,GAAMC,kBAAgB,EACxB,CAACC,EAASC,CAAU,EAAIC,EAAAA,SAAS,EAAK,EACtCC,EAAaC,SAA0B,IAAI,EAC3CC,EAAaD,SAAuB,IAAI,EAExCE,EAAUC,EAAAA,YACbC,GAAqC,CACpCA,EAAEC,eAAe,EACjBD,EAAEE,gBAAgB,EAElBd,EAAWe,eAAejB,CAAI,CAChC,EAGA,CAACA,CAAI,CACP,EAEAkB,OAAAA,EAAAA,UAAU,IAAM,CACd,GAAIT,EAAWU,SAAWR,EAAWQ,SAAWb,EAAS,CACvD,MAAMc,EAAiBC,EAAAA,aACrBZ,EAAWU,QACXR,EAAWQ,QACX,CACEG,UAAW,UACXC,UAAW,CACT,CACEC,KAAM,SACNC,QAAS,CACPC,OAAQ,CAAC,EAAG,CAAC,CAAA,CAEhB,CAAA,CAAA,CAGP,EAEA,MAAO,IAAM,CACXN,EAAeO,QAAQ,CACzB,CAAA,CACF,EACC,CAACrB,CAAO,CAAC,0BAGT,SACC,CAAA,IAAKG,EACL,QAAAG,EACA,aAAc,IAAML,EAAW,EAAI,EACnC,aAAc,IAAMA,EAAW,EAAK,EACpC,KAAK,SACL,aAAYH,EAAE,qBAAqB,EACnC,UAAW,8DAA8DH,CAAS,IAElF2B,EAAAA,QAAA,cAACC,eAAY,cAAW,GAAA,EAEvBvB,GACCsB,EAAAA,QAAA,cAAC,OACC,IAAKjB,EACL,UAAW,4BAA4BV,CAAS,IAE/CG,EAAE,qBAAqB,CAC1B,CAEJ,CAEJ"}