import { MsTeamsChannelQueryOptions, RecipientObject } from '@knocklabs/react-core';
import { FunctionComponent } from 'react';
interface MsTeamsChannelInTeamComboboxProps {
    teamId?: string;
    msTeamsChannelsRecipientObject: RecipientObject;
    queryOptions?: MsTeamsChannelQueryOptions;
}
export declare const MsTeamsChannelInTeamCombobox: FunctionComponent<MsTeamsChannelInTeamComboboxProps>;
export {};
//# sourceMappingURL=MsTeamsChannelInTeamCombobox.d.ts.map