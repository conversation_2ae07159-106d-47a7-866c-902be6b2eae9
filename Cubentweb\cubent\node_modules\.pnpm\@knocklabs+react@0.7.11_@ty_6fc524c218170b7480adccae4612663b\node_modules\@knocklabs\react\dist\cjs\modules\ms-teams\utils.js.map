{"version": 3, "file": "utils.js", "sources": ["../../../../src/modules/ms-teams/utils.ts"], "sourcesContent": ["export const sortByDisplayName = <T extends { displayName: string }>(\n  items: T[],\n) =>\n  items.sort((a, b) =>\n    a.displayName.toLowerCase().localeCompare(b.displayName.toLowerCase()),\n  );\n"], "names": ["sortByDisplayName", "items", "sort", "a", "b", "displayName", "toLowerCase", "localeCompare"], "mappings": "gFAAO,MAAMA,EACXC,GAEAA,EAAMC,KAAK,CAACC,EAAGC,IACbD,EAAEE,YAAYC,cAAcC,cAAcH,EAAEC,YAAYC,aAAa,CACvE"}