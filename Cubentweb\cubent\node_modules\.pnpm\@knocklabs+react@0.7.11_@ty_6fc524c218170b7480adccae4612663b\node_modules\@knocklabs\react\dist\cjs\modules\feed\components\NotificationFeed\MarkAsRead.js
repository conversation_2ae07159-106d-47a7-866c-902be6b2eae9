"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const s=require("@knocklabs/react-core"),m=require("react"),f=require("../../../core/components/Icons/CheckmarkCircle.js");;/* empty css            */function b(e){if(e&&typeof e=="object"&&"default"in e)return e;const r=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const t in e)if(t!=="default"){const n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:()=>e[t]})}}return r.default=e,Object.freeze(r)}const o=b(m),k=({onClick:e})=>{const{useFeedStore:r,feedClient:t,colorMode:n}=s.useKnockFeed(),{t:l}=s.useTranslations(),c=r(a=>a.items.filter(i=>!i.read_at)),u=r(a=>a.metadata.unread_count),d=o.useCallback(a=>{t.markAllAsRead(),e&&e(a,c)},[t,c,e]);return o.createElement("button",{className:`rnf-mark-all-as-read rnf-mark-all-as-read--${n}`,disabled:u===0,onClick:d,type:"button"},l("markAllAsRead"),o.createElement(f.CheckmarkCircle,{"aria-hidden":!0}))};exports.MarkAsRead=k;
//# sourceMappingURL=MarkAsRead.js.map
