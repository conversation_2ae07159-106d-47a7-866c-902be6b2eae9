"use strict";const r=require("react"),n=require("@knocklabs/react-core"),c=require("./SlackErrorMessage.js"),o=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},s=o(r),a=()=>{const{t:e}=n.useTranslations(),{connectionStatus:t}=n.useKnockSlackClient();return t==="disconnected"||t==="error"?s.default.createElement(c,{message:e(t==="disconnected"?"slackConnectionErrorOccurred":"slackConnectionErrorExists")}):null};module.exports=a;
//# sourceMappingURL=SlackConnectionError.js.map
