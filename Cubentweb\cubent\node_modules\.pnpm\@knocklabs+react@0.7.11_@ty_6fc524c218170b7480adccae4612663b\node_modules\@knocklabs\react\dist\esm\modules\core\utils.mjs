const r = (e) => {
  const n = window.screenLeft ?? window.screenX, t = window.screenTop ?? window.screenY, o = window.innerWidth ?? document.documentElement.clientWidth ?? screen.width, i = window.innerHeight ?? document.documentElement.clientHeight ?? screen.height, c = o / 2 - 600 / 2 + n, h = `width=600,height=800,top=${i / 2 - 800 / 2 + t},left=${c}`;
  window.open(e, "_blank", h);
};
export {
  r as openPopupWindow
};
//# sourceMappingURL=utils.mjs.map
