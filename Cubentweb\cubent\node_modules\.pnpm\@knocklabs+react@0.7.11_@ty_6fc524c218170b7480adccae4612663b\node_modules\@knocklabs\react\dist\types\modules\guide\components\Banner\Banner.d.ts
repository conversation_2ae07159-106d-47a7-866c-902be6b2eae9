import { ColorMode } from '@knocklabs/react-core';
import { default as React } from 'react';
import { ButtonContent, TargetButton, TargetButtonWithGuide } from '../types';
declare const Root: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const Content: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const Title: React.FC<{
    title: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Body: React.FC<{
    body: string;
} & React.ComponentPropsWithRef<"div">>;
declare const Actions: React.FC<React.PropsWithChildren<React.ComponentPropsWithRef<"div">>>;
declare const PrimaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
declare const SecondaryButton: React.FC<ButtonContent & React.ComponentPropsWithRef<"button">>;
declare const DismissButton: React.FC<React.ComponentPropsWithRef<"button">>;
type BannerContent = {
    title: string;
    body: string;
    primary_button?: ButtonContent;
    secondary_button?: ButtonContent;
    dismissible?: boolean;
};
declare const DefaultView: React.FC<{
    content: BannerContent;
    colorMode?: ColorMode;
    onDismiss?: () => void;
    onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;
}>;
type BannerProps = {
    guideKey?: string;
    onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;
};
export declare const Banner: React.FC<BannerProps>;
export declare const BannerView: {
    Default: typeof DefaultView;
    Root: typeof Root;
    Content: typeof Content;
    Title: typeof Title;
    Body: typeof Body;
    Actions: typeof Actions;
    PrimaryButton: typeof PrimaryButton;
    SecondaryButton: typeof SecondaryButton;
    DismissButton: typeof DismissButton;
};
export {};
//# sourceMappingURL=Banner.d.ts.map