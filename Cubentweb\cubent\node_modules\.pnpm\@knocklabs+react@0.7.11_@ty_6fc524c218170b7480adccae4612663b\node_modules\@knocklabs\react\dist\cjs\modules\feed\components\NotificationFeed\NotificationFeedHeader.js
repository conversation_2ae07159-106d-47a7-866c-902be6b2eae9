"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("@knocklabs/react-core"),c=require("react"),l=require("./Dropdown.js"),d=require("./MarkAsRead.js"),s=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=s(c),u=[n.FilterStatus.All,n.FilterStatus.Unread,n.FilterStatus.Read],f=({onMarkAllAsReadClick:e,filterStatus:o,setFilterStatus:i})=>{const{t:r}=n.useTranslations();return t.default.createElement("header",{className:"rnf-notification-feed__header"},t.default.createElement("div",{className:"rnf-notification-feed__selector"},t.default.createElement("span",{className:"rnf-notification-feed__type"},r("notifications")),t.default.createElement(l.Dropdown,{value:o,onChange:a=>i(a.target.value)},u.map(a=>t.default.createElement("option",{key:a,value:a},r(a))))),t.default.createElement(d.MarkAsRead,{onClick:e}))};exports.NotificationFeedHeader=f;
//# sourceMappingURL=NotificationFeedHeader.js.map
