"use strict";const a=require("react"),r=require("@telegraph/icon"),o=require("@telegraph/typography"),c=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=c(a),n=({message:e})=>t.default.createElement("div",{className:"rtk-combobox__error"},t.default.createElement("span",null,t.default.createElement(r.Icon,{icon:r.Lucide.Info,color:"black",size:"1","aria-hidden":!0})),t.default.createElement(o.Text,{as:"div",color:"black",size:"1"},e));module.exports=n;
//# sourceMappingURL=MsTeamsErrorMessage.js.map
