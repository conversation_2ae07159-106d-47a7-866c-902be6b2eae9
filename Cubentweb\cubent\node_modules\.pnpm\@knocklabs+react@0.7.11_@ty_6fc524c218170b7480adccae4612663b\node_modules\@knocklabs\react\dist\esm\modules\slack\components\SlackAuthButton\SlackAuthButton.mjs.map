{"version": 3, "file": "SlackAuthButton.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackAuthButton/SlackAuthButton.tsx"], "sourcesContent": ["import {\n  useKnockClient,\n  useKnockSlackClient,\n  useSlackAuth,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { FunctionComponent, useMemo } from \"react\";\nimport { useEffect } from \"react\";\n\nimport { openPopupWindow } from \"../../../core/utils\";\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthButtonProps {\n  slackClientId: string;\n  redirectUrl?: string;\n  onAuthenticationComplete?: (authenticationResp: string) => void;\n  // When provided, the default scopes will be overridden with the provided scopes\n  scopes?: string[];\n  // Additional scopes to add to the default scopes\n  additionalScopes?: string[];\n}\n\nexport const SlackAuthButton: FunctionComponent<SlackAuthButtonProps> = ({\n  slackClientId,\n  redirectUrl,\n  onAuthenticationComplete,\n  scopes,\n  additionalScopes,\n}) => {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n\n  const {\n    setConnectionStatus,\n    connectionStatus,\n    setActionLabel,\n    actionLabel,\n    errorLabel,\n  } = useKnockSlackClient();\n\n  const useSlackAuthOptions = useMemo(\n    () => ({\n      scopes,\n      additionalScopes,\n    }),\n    [scopes, additionalScopes],\n  );\n\n  const { buildSlackAuthUrl, disconnectFromSlack } = useSlackAuth(\n    slackClientId,\n    redirectUrl,\n    useSlackAuthOptions,\n  );\n\n  useEffect(() => {\n    const receiveMessage = (event: MessageEvent) => {\n      if (event.origin !== knock.host) {\n        return;\n      }\n\n      try {\n        if (event.data === \"authComplete\") {\n          setConnectionStatus(\"connected\");\n        }\n\n        if (event.data === \"authFailed\") {\n          setConnectionStatus(\"error\");\n        }\n\n        if (onAuthenticationComplete) {\n          onAuthenticationComplete(event.data);\n        }\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    window.addEventListener(\"message\", receiveMessage, false);\n\n    // Cleanup the event listener when the component unmounts\n    return () => {\n      window.removeEventListener(\"message\", receiveMessage);\n    };\n  }, [knock.host, onAuthenticationComplete, setConnectionStatus]);\n\n  const disconnectLabel = t(\"slackDisconnect\") || null;\n  const reconnectLabel = t(\"slackReconnect\") || null;\n\n  // Loading states\n  if (\n    connectionStatus === \"connecting\" ||\n    connectionStatus === \"disconnecting\"\n  ) {\n    return (\n      <div className=\"rsk-connect__button rsk-connect__button--loading\">\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>\n          {connectionStatus === \"connecting\"\n            ? t(\"slackConnecting\")\n            : t(\"slackDisconnecting\")}\n        </span>\n      </div>\n    );\n  }\n\n  // Error state\n  if (connectionStatus === \"error\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--error\"\n        onMouseEnter={() => setActionLabel(reconnectLabel)}\n        onMouseLeave={() => setActionLabel(null)}\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span className=\"rsk-connect__button__text--error\">\n          {actionLabel || errorLabel || t(\"slackError\")}\n        </span>\n      </button>\n    );\n  }\n\n  // Disconnected state\n  if (connectionStatus === \"disconnected\") {\n    return (\n      <button\n        onClick={() => openPopupWindow(buildSlackAuthUrl())}\n        className=\"rsk-connect__button rsk-connect__button--disconnected\"\n      >\n        <SlackIcon height=\"16px\" width=\"16px\" />\n        <span>{t(\"slackConnect\")}</span>\n      </button>\n    );\n  }\n\n  // Connected state\n  return (\n    <button\n      onClick={disconnectFromSlack}\n      className=\"rsk-connect__button rsk-connect__button--connected\"\n      onMouseEnter={() => setActionLabel(disconnectLabel)}\n      onMouseLeave={() => setActionLabel(null)}\n    >\n      <SlackIcon height=\"16px\" width=\"16px\" />\n      <span className=\"rsk-connect__button__text--connected\">\n        {actionLabel || t(\"slackConnected\")}\n      </span>\n    </button>\n  );\n};\n"], "names": ["SlackAuthButton", "slackClientId", "redirectUrl", "onAuthenticationComplete", "scopes", "additionalScopes", "t", "useTranslations", "knock", "useKnockClient", "setConnectionStatus", "connectionStatus", "setActionLabel", "actionLabel", "error<PERSON><PERSON><PERSON>", "useKnockSlackClient", "useSlackAuthOptions", "useMemo", "buildSlackAuthUrl", "disconnectFromSlack", "useSlackAuth", "useEffect", "receiveMessage", "event", "origin", "host", "data", "addEventListener", "removeEventListener", "disconnectLabel", "reconnectLabel", "React", "SlackIcon", "openPopupWindow"], "mappings": ";;;;;;AAyBO,MAAMA,IAA2DA,CAAC;AAAA,EACvEC,eAAAA;AAAAA,EACAC,aAAAA;AAAAA,EACAC,0BAAAA;AAAAA,EACAC,QAAAA;AAAAA,EACAC,kBAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC;AAAAA,MAAMC,EAAgB,GACxBC,IAAQC,EAAe,GAEvB;AAAA,IACJC,qBAAAA;AAAAA,IACAC,kBAAAA;AAAAA,IACAC,gBAAAA;AAAAA,IACAC,aAAAA;AAAAA,IACAC,YAAAA;AAAAA,MACEC,EAAoB,GAElBC,IAAsBC,EAC1B,OAAO;AAAA,IACLb,QAAAA;AAAAA,IACAC,kBAAAA;AAAAA,EAAAA,IAEF,CAACD,GAAQC,CAAgB,CAC3B,GAEM;AAAA,IAAEa,mBAAAA;AAAAA,IAAmBC,qBAAAA;AAAAA,EAAwBC,IAAAA,EACjDnB,GACAC,GACAc,CACF;AAEAK,EAAAA,EAAU,MAAM;AACRC,UAAAA,IAAiBA,CAACC,MAAwB;AAC1CA,UAAAA,EAAMC,WAAWhB,EAAMiB;AAIvB,YAAA;AACEF,UAAAA,EAAMG,SAAS,kBACjBhB,EAAoB,WAAW,GAG7Ba,EAAMG,SAAS,gBACjBhB,EAAoB,OAAO,GAGzBP,KACFA,EAAyBoB,EAAMG,IAAI;AAAA,gBAEtB;AACfhB,UAAAA,EAAoB,OAAO;AAAA,QAAA;AAAA,IAE/B;AAEOiB,kBAAAA,iBAAiB,WAAWL,GAAgB,EAAK,GAGjD,MAAM;AACJM,aAAAA,oBAAoB,WAAWN,CAAc;AAAA,IACtD;AAAA,KACC,CAACd,EAAMiB,MAAMtB,GAA0BO,CAAmB,CAAC;AAExDmB,QAAAA,IAAkBvB,EAAE,iBAAiB,KAAK,MAC1CwB,IAAiBxB,EAAE,gBAAgB,KAAK;AAI5CK,SAAAA,MAAqB,gBACrBA,MAAqB,kBAGnBoB,gBAAAA,EAAA,cAAC,SAAI,WAAU,mDAAA,mCACZC,GAAU,EAAA,QAAO,QAAO,OAAM,OAAM,CAAA,GACpCD,gBAAAA,EAAA,cAAA,QAAA,MAEKzB,EADHK,MAAqB,eAChB,oBACA,oBADiB,CAEzB,CACF,IAKAA,MAAqB,0CAEpB,UACC,EAAA,SAAS,MAAMsB,EAAgBf,GAAmB,GAClD,WAAU,kDACV,cAAc,MAAMN,EAAekB,CAAc,GACjD,cAAc,MAAMlB,EAAe,IAAI,EAAA,mCAEtCoB,GAAU,EAAA,QAAO,QAAO,OAAM,QAAM,GACrCD,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,sCACblB,KAAeC,KAAcR,EAAE,YAAY,CAC9C,CACF,IAKAK,MAAqB,iBAErBoB,gBAAAA,EAAA,cAAC,YACC,SAAS,MAAME,EAAgBf,EAAkB,CAAC,GAClD,WAAU,wDAEV,GAAAa,gBAAAA,EAAA,cAACC,KAAU,QAAO,QAAO,OAAM,OAAM,CAAA,mCACpC,QAAM1B,MAAAA,EAAE,cAAc,CAAE,CAC3B,IAMDyB,gBAAAA,EAAA,cAAA,UAAA,EACC,SAASZ,GACT,WAAU,sDACV,cAAc,MAAMP,EAAeiB,CAAe,GAClD,cAAc,MAAMjB,EAAe,IAAI,KAEvCmB,gBAAAA,EAAA,cAACC,GAAU,EAAA,QAAO,QAAO,OAAM,OAAM,CAAA,GACrCD,gBAAAA,EAAA,cAAC,QAAK,EAAA,WAAU,uCACblB,GAAAA,KAAeP,EAAE,gBAAgB,CACpC,CACF;AAEJ;"}