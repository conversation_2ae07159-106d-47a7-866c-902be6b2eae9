"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const s=require("@knocklabs/react-core"),y=require("@popperjs/core"),i=require("react"),F=require("../../../core/hooks/useComponentVisible.js"),N=require("../NotificationFeed/NotificationFeed.js");require("../NotificationFeed/NotificationFeedHeader.js");;/* empty css                              */;/* empty css            */const _=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},n=_(i),E=({store:e,feedClient:t})=>{e.metadata.unseen_count>0&&t.markAllAsSeen()},g=({isVisible:e,onOpen:t=E,onClose:u,buttonRef:r,closeOnClickOutside:d=!0,placement:c="bottom-end",...l})=>{const{t:p}=s.useTranslations(),{colorMode:m,feedClient:a,useFeedStore:v}=s.useKnockFeed(),f=v(),{ref:o}=F(e,u,{closeOnClickOutside:d});return i.useEffect(()=>{e&&t&&t({store:f,feedClient:a})},[e,t,f,a]),i.useEffect(()=>{if(r.current&&o.current){const q=y.createPopper(r.current,o.current,{strategy:"fixed",placement:c,modifiers:[{name:"offset",options:{offset:[0,8]}}]});return()=>{q.destroy()}}},[r,o,c]),n.default.createElement("div",{className:`rnf-notification-feed-popover rnf-notification-feed-popover--${m}`,style:{visibility:e?"visible":"hidden",opacity:e?1:0},ref:o,role:"dialog","aria-label":p("notifications"),tabIndex:-1},n.default.createElement("div",{className:"rnf-notification-feed-popover__inner"},n.default.createElement(N.NotificationFeed,{...l})))};exports.NotificationFeedPopover=g;
//# sourceMappingURL=NotificationFeedPopover.js.map
