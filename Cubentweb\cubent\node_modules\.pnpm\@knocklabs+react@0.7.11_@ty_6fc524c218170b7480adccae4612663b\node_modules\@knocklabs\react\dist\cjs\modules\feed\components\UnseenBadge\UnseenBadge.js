"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const u=require("@knocklabs/react-core"),c=require("react");;/* empty css            */const o=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},a=o(c);function s(e,n){switch(e){case"all":return n.total_count;case"unread":return n.unread_count;case"unseen":return n.unseen_count}}const l=({badgeCountType:e="unseen"})=>{const{useFeedStore:n}=u.useKnockFeed(),t=n(r=>s(e,r.metadata));return t!==0?a.default.createElement("div",{className:"rnf-unseen-badge"},a.default.createElement("span",{className:"rnf-unseen-badge__count"},u.formatBadgeCount(t))):null};exports.UnseenBadge=l;
//# sourceMappingURL=UnseenBadge.js.map
