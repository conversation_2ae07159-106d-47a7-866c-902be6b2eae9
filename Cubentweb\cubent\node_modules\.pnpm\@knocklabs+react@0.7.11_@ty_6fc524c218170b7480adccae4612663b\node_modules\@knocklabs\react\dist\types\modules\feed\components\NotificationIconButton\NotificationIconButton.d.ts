import { default as React, SyntheticEvent } from 'react';
import { BadgeCountType } from '../UnseenBadge';
export interface NotificationIconButtonProps {
    badgeCountType?: BadgeCountType;
    onClick: (e: SyntheticEvent) => void;
}
export declare const NotificationIconButton: React.ForwardRefExoticComponent<NotificationIconButtonProps & React.RefAttributes<HTMLButtonElement>>;
//# sourceMappingURL=NotificationIconButton.d.ts.map