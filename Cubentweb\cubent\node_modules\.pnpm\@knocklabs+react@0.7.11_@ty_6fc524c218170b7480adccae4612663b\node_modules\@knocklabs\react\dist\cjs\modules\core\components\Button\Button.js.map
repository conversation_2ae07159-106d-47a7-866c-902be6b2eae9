{"version": 3, "file": "Button.js", "sources": ["../../../../../../src/modules/core/components/Button/Button.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { ButtonSpinner } from \"./ButtonSpinner\";\nimport \"./styles.css\";\n\nexport type ButtonProps = {\n  variant: \"primary\" | \"secondary\";\n  loadingText?: string;\n  isLoading?: boolean;\n  isDisabled?: boolean;\n  isFullWidth?: boolean;\n  onClick: (e: React.MouseEvent) => void;\n};\n\nexport const Button: FunctionComponent<PropsWithChildren<ButtonProps>> = ({\n  variant = \"primary\",\n  loadingText,\n  isLoading = false,\n  isDisabled = false,\n  isFullWidth = false,\n  onClick,\n  children,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  const classNames = [\n    \"rnf-button\",\n    `rnf-button--${variant}`,\n    isFullWidth ? \"rnf-button--full-width\" : \"\",\n    isLoading ? \"rnf-button--is-loading\" : \"\",\n    `rnf-button--${colorMode}`,\n  ].join(\" \");\n\n  // In this case when there's no loading text, we still want to display the original\n  // content of the button, but make it hidden. That allows us to keep the button width\n  // consistent and show the spinner in the middle, meaning no layout shift.\n  const textToShowWhileLoading = loadingText || (\n    <span className=\"rnf-button__button-text-hidden\">{children}</span>\n  );\n\n  return (\n    <button\n      onClick={onClick}\n      className={classNames}\n      disabled={isLoading || isDisabled}\n      type=\"button\"\n    >\n      {isLoading && <ButtonSpinner hasLabel={!!loadingText} />}\n      {isLoading ? textToShowWhileLoading : children}\n    </button>\n  );\n};\n"], "names": ["<PERSON><PERSON>", "variant", "loadingText", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "children", "colorMode", "useKnockFeed", "classNames", "join", "textToShowWhileLoading", "React", "<PERSON>ton<PERSON><PERSON>ner"], "mappings": "4QAgBaA,EAA4DA,CAAC,CACxEC,QAAAA,EAAU,UACVC,YAAAA,EACAC,UAAAA,EAAY,GACZC,WAAAA,EAAa,GACbC,YAAAA,EAAc,GACdC,QAAAA,EACAC,SAAAA,CACF,IAAM,CACE,KAAA,CAAEC,UAAAA,GAAcC,eAAa,EAE7BC,EAAa,CACjB,aACA,eAAeT,CAAO,GACtBI,EAAc,yBAA2B,GACzCF,EAAY,yBAA2B,GACvC,eAAeK,CAAS,EAAE,EAC1BG,KAAK,GAAG,EAKJC,EAAyBV,GAC7BW,EAAAA,QAAA,cAAC,OAAK,CAAA,UAAU,kCAAkCN,CAAS,EAI3D,OAAAM,UAAA,cAAC,UACC,QAAAP,EACA,UAAWI,EACX,SAAUP,GAAaC,EACvB,KAAK,UAEJD,GAAcU,EAAA,QAAA,cAAAC,EAAAA,cAAA,CAAc,SAAU,CAAC,CAACZ,EAAe,EACvDC,EAAYS,EAAyBL,CACxC,CAEJ"}