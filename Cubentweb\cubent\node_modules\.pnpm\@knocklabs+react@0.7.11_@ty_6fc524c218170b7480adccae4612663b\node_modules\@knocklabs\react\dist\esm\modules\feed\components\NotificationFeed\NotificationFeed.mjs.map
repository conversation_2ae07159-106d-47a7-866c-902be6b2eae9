{"version": 3, "file": "NotificationFeed.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/NotificationFeed.tsx"], "sourcesContent": ["import { FeedItem, NetworkStatus, isRequestInFlight } from \"@knocklabs/client\";\nimport {\n  ColorMode,\n  FilterStatus,\n  useFeedSettings,\n  useKnockFeed,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { GenericData } from \"@knocklabs/types\";\nimport React, {\n  ReactNode,\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from \"react\";\n\nimport { Spinner } from \"../../../core/components/Spinner\";\nimport useOnBottomScroll from \"../../../core/hooks/useOnBottomScroll\";\nimport { EmptyFeed } from \"../EmptyFeed\";\nimport { NotificationCell, NotificationCellProps } from \"../NotificationCell\";\n\nimport {\n  NotificationFeedHeader,\n  NotificationFeedHeaderProps,\n} from \"./NotificationFeedHeader\";\nimport \"./styles.css\";\n\nexport type RenderItemProps<T = GenericData> = {\n  item: FeedItem<T>;\n  onItemClick?: NotificationCellProps[\"onItemClick\"];\n  onButtonClick?: NotificationCellProps[\"onButtonClick\"];\n};\n\nexport type RenderItem = (props: RenderItemProps) => ReactNode;\n\nexport interface NotificationFeedProps {\n  EmptyComponent?: ReactNode;\n  /**\n   * @deprecated Use `renderHeader` instead to accept `NotificationFeedHeaderProps`\n   */\n  header?: ReactNode;\n  renderItem?: RenderItem;\n  renderHeader?: (props: NotificationFeedHeaderProps) => ReactNode;\n  onNotificationClick?: NotificationCellProps[\"onItemClick\"];\n  onNotificationButtonClick?: NotificationCellProps[\"onButtonClick\"];\n  onMarkAllAsReadClick?: (e: React.MouseEvent, unreadItems: FeedItem[]) => void;\n  initialFilterStatus?: FilterStatus;\n}\n\nconst defaultRenderItem = (props: RenderItemProps) => (\n  <NotificationCell key={props.item.id} {...props} />\n);\n\nconst defaultRenderHeader = (props: NotificationFeedHeaderProps) => (\n  <NotificationFeedHeader {...props} />\n);\n\nconst LoadingSpinner = ({ colorMode }: { colorMode: ColorMode }) => (\n  <div className=\"rnf-notification-feed__spinner-container\">\n    <Spinner\n      thickness={3}\n      size=\"16px\"\n      color={colorMode === \"dark\" ? \"rgba(255, 255, 255, 0.65)\" : undefined}\n    />\n  </div>\n);\n\nconst poweredByKnockUrl =\n  \"https://knock.app?utm_source=powered-by-knock&utm_medium=referral&utm_campaign=knock-branding-feed\";\n\nexport const NotificationFeed: React.FC<NotificationFeedProps> = ({\n  EmptyComponent = <EmptyFeed />,\n  renderItem = defaultRenderItem,\n  onNotificationClick,\n  onNotificationButtonClick,\n  onMarkAllAsReadClick,\n  initialFilterStatus = FilterStatus.All,\n  header,\n  renderHeader = defaultRenderHeader,\n}) => {\n  const [status, setStatus] = useState(initialFilterStatus);\n  const { feedClient, useFeedStore, colorMode } = useKnockFeed();\n  const { settings } = useFeedSettings(feedClient);\n  const { t } = useTranslations();\n\n  const { pageInfo, items, networkStatus } = useFeedStore();\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    setStatus(initialFilterStatus);\n  }, [initialFilterStatus]);\n\n  useEffect(() => {\n    // When the feed client changes, or the status changes issue a re-fetch\n    feedClient.fetch({ status });\n  }, [feedClient, status]);\n\n  const noItems = items.length === 0;\n  const requestInFlight = isRequestInFlight(networkStatus);\n\n  // Handle fetching more once we reach the bottom of the list\n  const onBottomCallback = useCallback(() => {\n    if (!requestInFlight && pageInfo.after) {\n      feedClient.fetchNextPage();\n    }\n  }, [requestInFlight, pageInfo, feedClient]);\n\n  // Once we scroll to the bottom of the view we want to automatically fetch\n  // more items for the feed and bring them into the list\n  useOnBottomScroll({\n    ref: containerRef,\n    callback: onBottomCallback,\n    offset: 70,\n  });\n\n  return (\n    <div\n      className={`rnf-notification-feed rnf-notification-feed--${colorMode}`}\n    >\n      {header ||\n        renderHeader({\n          setFilterStatus: setStatus,\n          filterStatus: status,\n          onMarkAllAsReadClick,\n        })}\n\n      <div className=\"rnf-notification-feed__container\" ref={containerRef}>\n        {networkStatus === NetworkStatus.loading && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        <div className=\"rnf-notification-feed__feed-items-container\">\n          {networkStatus !== NetworkStatus.loading &&\n            items.map((item: FeedItem) =>\n              renderItem({\n                item,\n                onItemClick: onNotificationClick,\n                onButtonClick: onNotificationButtonClick,\n              }),\n            )}\n        </div>\n\n        {networkStatus === NetworkStatus.fetchMore && (\n          <LoadingSpinner colorMode={colorMode} />\n        )}\n\n        {!requestInFlight && noItems && EmptyComponent}\n      </div>\n\n      {settings?.features.branding_required && (\n        <div className=\"rnf-notification-feed__knock-branding\">\n          <a href={poweredByKnockUrl} target=\"_blank\">\n            {t(\"poweredBy\") || \"Powered by Knock\"}\n          </a>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": ["defaultRenderItem", "props", "React", "NotificationCell", "item", "id", "defaultRenderHeader", "NotificationFeedHeader", "LoadingSpinner", "colorMode", "Spinner", "undefined", "poweredByKnockUrl", "NotificationFeed", "EmptyComponent", "EmptyFeed", "renderItem", "onNotificationClick", "onNotificationButtonClick", "onMarkAllAsReadClick", "initialFilterStatus", "FilterStatus", "All", "header", "renderHeader", "status", "setStatus", "useState", "feedClient", "useFeedStore", "useKnockFeed", "settings", "useFeedSettings", "t", "useTranslations", "pageInfo", "items", "networkStatus", "containerRef", "useRef", "useEffect", "fetch", "noItems", "length", "requestInFlight", "isRequestInFlight", "onBottomCallback", "useCallback", "after", "fetchNextPage", "useOnBottomScroll", "ref", "callback", "offset", "setFilterStatus", "filterStatus", "NetworkStatus", "loading", "map", "onItemClick", "onButtonClick", "fetchMore", "features", "branding_required"], "mappings": ";;;;;;;;;;AAkDA,MAAMA,IAAoBA,CAACC,MACxBC,gBAAAA,EAAA,cAAAC,GAAA,EAAiB,KAAKF,EAAMG,KAAKC,IAAI,GAAIJ,EAC3C,CAAA,GAEKK,IAAsBA,CAACL,MAC1BC,gBAAAA,EAAA,cAAAK,GAAA,EAA2BN,GAAAA,GAC7B,GAEKO,IAAiBA,CAAC;AAAA,EAAEC,WAAAA;AAAoC,MAC3DP,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,2CAAA,mCACZQ,GACC,EAAA,WAAW,GACX,MAAK,QACL,OAAOD,MAAc,SAAS,8BAA8BE,QAAU,CAE1E,GAGIC,IACJ,sGAEWC,IAAoDA,CAAC;AAAA,EAChEC,gBAAAA,oCAAkBC,GAAY,IAAA;AAAA,EAC9BC,YAAAA,IAAahB;AAAAA,EACbiB,qBAAAA;AAAAA,EACAC,2BAAAA;AAAAA,EACAC,sBAAAA;AAAAA,EACAC,qBAAAA,IAAsBC,EAAaC;AAAAA,EACnCC,QAAAA;AAAAA,EACAC,cAAAA,IAAelB;AACjB,MAAM;AACJ,QAAM,CAACmB,GAAQC,CAAS,IAAIC,EAASP,CAAmB,GAClD;AAAA,IAAEQ,YAAAA;AAAAA,IAAYC,cAAAA;AAAAA,IAAcpB,WAAAA;AAAAA,MAAcqB,EAAa,GACvD;AAAA,IAAEC,UAAAA;AAAAA,EAAAA,IAAaC,EAAgBJ,CAAU,GACzC;AAAA,IAAEK,GAAAA;AAAAA,MAAMC,EAAgB,GAExB;AAAA,IAAEC,UAAAA;AAAAA,IAAUC,OAAAA;AAAAA,IAAOC,eAAAA;AAAAA,MAAkBR,EAAa,GAClDS,IAAeC,EAAuB,IAAI;AAEhDC,EAAAA,EAAU,MAAM;AACdd,IAAAA,EAAUN,CAAmB;AAAA,EAAA,GAC5B,CAACA,CAAmB,CAAC,GAExBoB,EAAU,MAAM;AAEdZ,IAAAA,EAAWa,MAAM;AAAA,MAAEhB,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA,GAC1B,CAACG,GAAYH,CAAM,CAAC;AAEjBiB,QAAAA,IAAUN,EAAMO,WAAW,GAC3BC,IAAkBC,EAAkBR,CAAa,GAGjDS,IAAmBC,EAAY,MAAM;AACrC,IAAA,CAACH,KAAmBT,EAASa,SAC/BpB,EAAWqB,cAAc;AAAA,EAE1B,GAAA,CAACL,GAAiBT,GAAUP,CAAU,CAAC;AAIxB,SAAAsB,EAAA;AAAA,IAChBC,KAAKb;AAAAA,IACLc,UAAUN;AAAAA,IACVO,QAAQ;AAAA,EAAA,CACT,mCAGE,OACC,EAAA,WAAW,gDAAgD5C,CAAS,MAEnEc,KACCC,EAAa;AAAA,IACX8B,iBAAiB5B;AAAAA,IACjB6B,cAAc9B;AAAAA,IACdN,sBAAAA;AAAAA,EAAAA,CACD,GAEHjB,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,oCAAmC,KAAKoC,EACpDD,GAAAA,MAAkBmB,EAAcC,WAC9BvD,gBAAAA,EAAA,cAAAM,GAAA,EAAe,WAAAC,EACjB,CAAA,GAEDP,gBAAAA,EAAA,cAAC,OAAI,EAAA,WAAU,8CACZmC,GAAAA,MAAkBmB,EAAcC,WAC/BrB,EAAMsB,IAAI,CAACtD,MACTY,EAAW;AAAA,IACTZ,MAAAA;AAAAA,IACAuD,aAAa1C;AAAAA,IACb2C,eAAe1C;AAAAA,EAAAA,CAChB,CACH,CACJ,GAECmB,MAAkBmB,EAAcK,aAC9B3D,gBAAAA,EAAA,cAAAM,GAAA,EAAe,WAAAC,EACjB,CAAA,GAEA,CAACmC,KAAmBF,KAAW5B,CAClC,IAECiB,KAAAA,gBAAAA,EAAU+B,SAASC,sBACjB7D,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,wCAAA,mCACZ,KAAE,EAAA,MAAMU,GAAmB,QAAO,YAChCqB,EAAE,WAAW,KAAK,kBACrB,CACF,CAEJ;AAEJ;"}