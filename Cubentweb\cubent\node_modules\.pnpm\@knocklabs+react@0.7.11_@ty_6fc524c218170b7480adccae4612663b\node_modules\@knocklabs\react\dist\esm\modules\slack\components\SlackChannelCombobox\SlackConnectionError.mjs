import n from "react";
import { useTranslations as o, useKnockSlackClient as t } from "@knocklabs/react-core";
import c from "./SlackErrorMessage.mjs";
const l = () => {
  const {
    t: e
  } = o(), {
    connectionStatus: r
  } = t();
  return r === "disconnected" || r === "error" ? /* @__PURE__ */ n.createElement(c, { message: e(r === "disconnected" ? "slackConnectionErrorOccurred" : "slackConnectionErrorExists") }) : null;
};
export {
  l as default
};
//# sourceMappingURL=SlackConnectionError.mjs.map
