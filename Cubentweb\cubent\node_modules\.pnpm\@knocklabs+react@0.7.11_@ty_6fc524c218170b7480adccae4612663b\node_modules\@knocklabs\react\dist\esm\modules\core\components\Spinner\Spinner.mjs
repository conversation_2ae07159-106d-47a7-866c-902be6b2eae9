import e from "react";
function s(t) {
  return t === "fast" ? 600 : t === "slow" ? 900 : 750;
}
const l = ({
  color: t = "rgba(0,0,0,0.4)",
  speed: r = "medium",
  gap: a = 4,
  thickness: n = 4,
  size: i = "1em",
  ...o
}) => /* @__PURE__ */ e.createElement("svg", { height: i, width: i, ...o, style: {
  animationDuration: `${s(r)}ms`
}, className: "__react-svg-spinner_circle", role: "img", "aria-labelledby": "title desc", viewBox: "0 0 32 32" }, /* @__PURE__ */ e.createElement("title", { id: "title" }, "Circle loading spinner"), /* @__PURE__ */ e.createElement("desc", { id: "desc" }, 'Image of a partial circle indicating "loading."'), /* @__PURE__ */ e.createElement("style", { dangerouslySetInnerHTML: {
  __html: `
      .__react-svg-spinner_circle{
          transition-property: transform;
          animation-name: __react-svg-spinner_infinite-spin;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
      }
      @keyframes __react-svg-spinner_infinite-spin {
          from {transform: rotate(0deg)}
          to {transform: rotate(360deg)}
      }
    `
} }), /* @__PURE__ */ e.createElement("circle", { role: "presentation", cx: 16, cy: 16, r: 14 - n / 2, stroke: t, fill: "none", strokeWidth: n, strokeDasharray: Math.PI * 2 * (11 - a), strokeLinecap: "round" }));
export {
  l as Spinner
};
//# sourceMappingURL=Spinner.mjs.map
