import { useRef as i, useEffect as s } from "react";
function d(e, t) {
  return e ? e === t || e.contains(t) : !1;
}
function a(e, t, o) {
  const r = i(null), u = (n) => {
    n.key === "Escape" && t(n);
  }, c = (n) => {
    o.closeOnClickOutside && !d(r.current, n.target) && t(n);
  };
  return s(() => (e && (document.addEventListener("keydown", u, !0), document.addEventListener("click", c, !0)), () => {
    document.removeEventListener("keydown", u, !0), document.removeEventListener("click", c, !0);
  }), [e]), {
    ref: r
  };
}
export {
  a as default
};
//# sourceMappingURL=useComponentVisible.mjs.map
