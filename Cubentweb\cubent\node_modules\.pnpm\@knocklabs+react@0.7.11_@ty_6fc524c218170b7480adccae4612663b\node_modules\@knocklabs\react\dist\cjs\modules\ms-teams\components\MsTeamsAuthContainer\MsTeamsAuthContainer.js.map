{"version": 3, "file": "MsTeamsAuthContainer.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const MsTeamsAuthContainer: FunctionComponent<\n  MsTeamsAuthContainerProps\n> = ({ actionButton }) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rtk-auth\">\n      <div className=\"rtk-auth__header\">\n        <MsTeamsIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rtk-auth__title\">Microsoft Teams</div>\n      <div className=\"rtk-auth__description\">\n        {t(\"msTeamsConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["MsTeamsAuthContainer", "actionButton", "t", "useTranslations", "React", "MsTeamsIcon"], "mappings": "2TAYaA,EAETA,CAAC,CAAEC,aAAAA,CAAa,IAAM,CAClB,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EAE9B,OACGC,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,oCACZ,MAAI,CAAA,UAAU,kBACb,EAAAA,UAAA,cAACC,EAAY,YAAA,CAAA,OAAO,OAAO,MAAM,OAAM,EACvCD,EAAAA,QAAA,cAAC,MAAKH,KAAAA,CAAa,CACrB,EACCG,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,iBAAkB,EAAA,iBAAe,EAChDA,EAAAA,QAAA,cAAC,OAAI,UAAU,yBACZF,EAAE,oCAAoC,CACzC,CACF,CAEJ"}