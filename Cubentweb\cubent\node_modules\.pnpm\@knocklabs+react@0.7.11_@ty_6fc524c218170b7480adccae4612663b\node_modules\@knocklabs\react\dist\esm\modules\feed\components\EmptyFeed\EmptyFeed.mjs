import e from "react";
import { useKnockFeed as r, useTranslations as n } from "@knocklabs/react-core";
/* empty css            */
const s = () => {
  const {
    colorMode: m
  } = r(), {
    t
  } = n();
  return /* @__PURE__ */ e.createElement("div", { className: `rnf-empty-feed rnf-empty-feed--${m}` }, /* @__PURE__ */ e.createElement("div", { className: "rnf-empty-feed__inner" }, /* @__PURE__ */ e.createElement("h2", { className: "rnf-empty-feed__header" }, t("emptyFeedTitle")), /* @__PURE__ */ e.createElement("p", { className: "rnf-empty-feed__body" }, t("emptyFeedBody"))));
};
export {
  s as EmptyFeed
};
//# sourceMappingURL=EmptyFeed.mjs.map
