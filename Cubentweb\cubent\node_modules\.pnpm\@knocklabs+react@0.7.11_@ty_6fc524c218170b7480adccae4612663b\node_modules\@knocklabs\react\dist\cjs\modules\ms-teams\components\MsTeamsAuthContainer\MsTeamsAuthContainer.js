"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const r=require("react"),s=require("@knocklabs/react-core");;/* empty css               */const n=require("../MsTeamsIcon/MsTeamsIcon.js");;/* empty css            */const c=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=c(r),i=({actionButton:e})=>{const{t:a}=s.useTranslations();return t.default.createElement("div",{className:"rtk-auth"},t.default.createElement("div",{className:"rtk-auth__header"},t.default.createElement(n.MsTeamsIcon,{height:"32px",width:"32px"}),t.default.createElement("div",null,e)),t.default.createElement("div",{className:"rtk-auth__title"},"Microsoft Teams"),t.default.createElement("div",{className:"rtk-auth__description"},a("msTeamsConnectContainerDescription")))};exports.MsTeamsAuthContainer=i;
//# sourceMappingURL=MsTeamsAuthContainer.js.map
