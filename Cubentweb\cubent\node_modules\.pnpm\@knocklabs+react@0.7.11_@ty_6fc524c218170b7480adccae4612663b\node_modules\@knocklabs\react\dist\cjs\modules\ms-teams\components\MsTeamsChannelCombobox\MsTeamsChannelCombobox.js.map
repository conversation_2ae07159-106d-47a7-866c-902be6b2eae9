{"version": 3, "file": "MsTeamsChannelCombobox.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.tsx"], "sourcesContent": ["import { MsTea<PERSON>Team } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  MsTeamsTeamQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent, useCallback, useState } from \"react\";\n\nimport \"../../theme.css\";\n\nimport { MsTeamsChannelInTeamCombobox } from \"./MsTeamsChannelInTeamCombobox\";\nimport MsTeamsConnectionError from \"./MsTeamsConnectionError\";\nimport { MsTeamsTeamCombobox } from \"./MsTeamsTeamCombobox\";\nimport \"./styles.css\";\n\ninterface Props {\n  msTeamsChannelsRecipientObject: RecipientObject;\n  teamQueryOptions?: MsTeamsTeamQueryOptions;\n  channelQueryOptions?: MsTeamsChannelQueryOptions;\n}\n\nconst MsTeamsChannelCombobox: FunctionComponent<Props> = ({\n  msTeamsChannelsRecipientObject,\n  teamQueryOptions,\n  channelQueryOptions,\n}) => {\n  const [selectedTeam, setSelectedTeam] = useState<MsTeamsTeam | null>(null);\n\n  const { data: currentConnections } = useConnectedMsTeamsChannels({\n    msTeamsChannelsRecipientObject,\n  });\n\n  const getChannelCount = useCallback(\n    (teamId: string) =>\n      currentConnections?.filter(\n        (connection) =>\n          connection.ms_teams_team_id === teamId &&\n          !!connection.ms_teams_channel_id,\n      ).length ?? 0,\n    [currentConnections],\n  );\n\n  return (\n    <Stack className=\"tgph rtk-combobox__grid\" gap=\"3\">\n      <Text color=\"gray\" size=\"2\" as=\"div\">\n        Team\n      </Text>\n      <MsTeamsTeamCombobox\n        team={selectedTeam}\n        onTeamChange={setSelectedTeam}\n        getChannelCount={getChannelCount}\n        queryOptions={teamQueryOptions}\n      />\n      <Stack\n        alignItems=\"center\"\n        gap=\"3\"\n        minHeight=\"8\"\n        style={{ alignSelf: \"start\" }}\n      >\n        <Icon color=\"gray\" size=\"1\" icon={Lucide.CornerDownRight} aria-hidden />\n        <Text color=\"gray\" size=\"2\" as=\"div\">\n          Channels\n        </Text>\n      </Stack>\n      <MsTeamsChannelInTeamCombobox\n        teamId={selectedTeam?.id}\n        msTeamsChannelsRecipientObject={msTeamsChannelsRecipientObject}\n        queryOptions={channelQueryOptions}\n      />\n      <MsTeamsConnectionError />\n    </Stack>\n  );\n};\n\nexport default MsTeamsChannelCombobox;\n"], "names": ["MsTeamsChannelCombobox", "msTeamsChannelsRecipientObject", "teamQueryOptions", "channelQueryOptions", "selectedTeam", "setSelectedTeam", "useState", "data", "currentConnections", "useConnectedMsTeamsChannels", "getChannelCount", "useCallback", "teamId", "filter", "connection", "ms_teams_team_id", "ms_teams_channel_id", "length", "React", "<PERSON><PERSON>", "Text", "MsTeamsTeamCombobox", "alignSelf", "Icon", "Lucide", "CornerDownRight", "MsTeamsChannelInTeamCombobox", "id", "MsTeamsConnectionError"], "mappings": "0aAyBMA,EAAmDA,CAAC,CACxDC,+BAAAA,EACAC,iBAAAA,EACAC,oBAAAA,CACF,IAAM,CACJ,KAAM,CAACC,EAAcC,CAAe,EAAIC,EAAAA,SAA6B,IAAI,EAEnE,CAAEC,KAAMC,GAAuBC,8BAA4B,CAC/DR,+BAAAA,CAAAA,CACD,EAEKS,EAAkBC,EAAAA,YACrBC,IACCJ,GAAAA,YAAAA,EAAoBK,UAEhBC,EAAWC,mBAAqBH,GAChC,CAAC,CAACE,EAAWE,qBACfC,SAAU,EACd,CAACT,CAAkB,CACrB,EAEA,OACGU,EAAA,QAAA,cAAAC,QAAA,CAAM,UAAU,0BAA0B,IAAI,GAC7C,EAAAD,EAAA,QAAA,cAACE,OAAK,CAAA,MAAM,OAAO,KAAK,IAAI,GAAG,OAAK,MAEpC,EACAF,EAAAA,QAAA,cAACG,EACC,oBAAA,CAAA,KAAMjB,EACN,aAAcC,EACd,gBAAAK,EACA,aAAcR,EAAiB,EAEhCgB,EAAA,QAAA,cAAAC,EAAAA,MAAA,CACC,WAAW,SACX,IAAI,IACJ,UAAU,IACV,MAAO,CAAEG,UAAW,OAAA,GAEpBJ,EAAAA,QAAA,cAACK,EAAK,KAAA,CAAA,MAAM,OAAO,KAAK,IAAI,KAAMC,EAAAA,OAAOC,gBAAiB,cAAW,EAAA,CAAA,EACrEP,EAAAA,QAAA,cAACE,QAAK,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,EAAA,UAEpC,CACF,0BACCM,EACC,6BAAA,CAAA,OAAQtB,GAAAA,YAAAA,EAAcuB,GACtB,+BAAA1B,EACA,aAAcE,CAAoB,CAAA,EAEpCe,EAAAA,QAAA,cAACU,MAAsB,CACzB,CAEJ"}