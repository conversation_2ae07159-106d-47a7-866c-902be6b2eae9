import { FilterStatus as a, useTranslations as c } from "@knocklabs/react-core";
import e from "react";
import { Dropdown as l } from "./Dropdown.mjs";
import { MarkAsRead as m } from "./MarkAsRead.mjs";
const s = [a.All, a.Unread, a.Read], u = ({
  onMarkAllAsReadClick: r,
  filterStatus: o,
  setFilterStatus: i
}) => {
  const {
    t: n
  } = c();
  return /* @__PURE__ */ e.createElement("header", { className: "rnf-notification-feed__header" }, /* @__PURE__ */ e.createElement("div", { className: "rnf-notification-feed__selector" }, /* @__PURE__ */ e.createElement("span", { className: "rnf-notification-feed__type" }, n("notifications")), /* @__PURE__ */ e.createElement(l, { value: o, onChange: (t) => i(t.target.value) }, s.map((t) => /* @__PURE__ */ e.createElement("option", { key: t, value: t }, n(t))))), /* @__PURE__ */ e.createElement(m, { onClick: r }));
};
export {
  u as NotificationFeedHeader
};
//# sourceMappingURL=NotificationFeedHeader.mjs.map
