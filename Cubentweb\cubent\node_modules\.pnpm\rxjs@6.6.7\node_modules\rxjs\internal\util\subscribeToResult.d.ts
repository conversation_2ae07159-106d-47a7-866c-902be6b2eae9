import { Subscription } from '../Subscription';
import { InnerSubscriber } from '../InnerSubscriber';
import { OuterSubscriber } from '../OuterSubscriber';
export declare function subscribeToResult<T, R>(outerSubscriber: OuterSubscriber<T, R>, result: any, outerValue: undefined, outerIndex: undefined, innerSubscriber: InnerSubscriber<T, R>): Subscription | undefined;
export declare function subscribeToResult<T, R>(outerSubscriber: OuterSubscriber<T, R>, result: any, outerValue?: T, outerIndex?: number): Subscription | undefined;
