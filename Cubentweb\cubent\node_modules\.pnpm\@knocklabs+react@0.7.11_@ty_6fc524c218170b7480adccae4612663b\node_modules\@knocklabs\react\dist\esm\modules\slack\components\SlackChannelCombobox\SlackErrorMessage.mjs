import e from "react";
import { Icon as o, Lucide as t } from "@telegraph/icon";
import { Text as a } from "@telegraph/typography";
const n = ({
  message: r
}) => /* @__PURE__ */ e.createElement("div", { className: "rsk-combobox__error" }, /* @__PURE__ */ e.createElement("span", null, /* @__PURE__ */ e.createElement(o, { icon: t.Info, color: "black", size: "1", "aria-hidden": !0 })), /* @__PURE__ */ e.createElement(a, { as: "div", color: "black", size: "1" }, r));
export {
  n as default
};
//# sourceMappingURL=SlackErrorMessage.mjs.map
