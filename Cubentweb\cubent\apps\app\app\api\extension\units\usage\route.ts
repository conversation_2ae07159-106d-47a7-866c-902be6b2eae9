import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { getRemainingUnits, getUsagePercentage } from '@repo/database/src/cubent-units';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    let userId: string | null = null;
    
    // Check for Bearer token in Authorization header (extension)
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Find the pending login with this token to get the user
      const pendingLogin = await database.pendingLogin.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() }, // Not expired
        },
      });

      if (pendingLogin) {
        // Token is valid, get user ID from Clerk session
        try {
          const { clerkClient } = await import('@repo/auth/server');
          const client = await clerkClient();
          const session = await client.sessions.getSession(token);
          userId = session.userId;
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid token' },
            { status: 401 }
          );
        }
      } else {
        return NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        );
      }
    } else {
      // Fallback to regular Clerk auth for web requests
      const authResult = await auth();
      userId = authResult.userId;
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find user in database
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
      include: {
        usageMetrics: {
          orderBy: { date: 'desc' },
          take: 30, // Last 30 days
        },
        usageAnalytics: {
          orderBy: { createdAt: 'desc' },
          take: 100, // Last 100 requests
        },
      },
    });

    if (!dbUser) {
      // User not found, return default values
      return NextResponse.json({
        unitsUsed: 0,
        unitsLimit: 50,
        unitsRemaining: 50,
        usagePercentage: 0,
        recentUsage: [],
        modelBreakdown: {},
        dailyUsage: [],
      });
    }

    // Calculate recent usage metrics
    const totalUnitsUsed = dbUser.cubentUnitsUsed;
    const unitsLimit = dbUser.cubentUnitsLimit;
    const unitsRemaining = getRemainingUnits(totalUnitsUsed, unitsLimit);
    const usagePercentage = getUsagePercentage(totalUnitsUsed, unitsLimit);

    // Calculate model breakdown
    const modelBreakdown: Record<string, { units: number; requests: number }> = {};
    dbUser.usageAnalytics.forEach(usage => {
      if (!modelBreakdown[usage.modelId]) {
        modelBreakdown[usage.modelId] = { units: 0, requests: 0 };
      }
      modelBreakdown[usage.modelId].units += usage.cubentUnitsUsed;
      modelBreakdown[usage.modelId].requests += usage.requestsMade;
    });

    // Calculate daily usage for the last 7 days
    const dailyUsage: Array<{ date: string; units: number; requests: number }> = [];
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    last7Days.forEach(dateStr => {
      const dayUsage = dbUser.usageMetrics.filter(metric => 
        metric.date.toISOString().split('T')[0] === dateStr
      );
      
      const dayUnits = dayUsage.reduce((sum, metric) => sum + metric.cubentUnitsUsed, 0);
      const dayRequests = dayUsage.reduce((sum, metric) => sum + metric.requestsMade, 0);
      
      dailyUsage.push({
        date: dateStr,
        units: dayUnits,
        requests: dayRequests,
      });
    });

    // Get recent usage (last 10 requests)
    const recentUsage = dbUser.usageAnalytics.slice(0, 10).map(usage => ({
      modelId: usage.modelId,
      unitsUsed: usage.cubentUnitsUsed,
      timestamp: usage.createdAt,
      metadata: usage.metadata,
    }));

    return NextResponse.json({
      unitsUsed: totalUnitsUsed,
      unitsLimit,
      unitsRemaining,
      usagePercentage: Math.round(usagePercentage * 100) / 100,
      recentUsage,
      modelBreakdown,
      dailyUsage,
      lastResetDate: dbUser.unitsResetDate,
      subscriptionTier: dbUser.subscriptionTier,
    });

  } catch (error) {
    console.error('Unit usage retrieval error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
