{"version": 3, "file": "MsTeamsAuthContainer.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsAuthContainer/MsTeamsAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { MsTeamsIcon } from \"../MsTeamsIcon\";\n\nimport \"./styles.css\";\n\nexport interface MsTeamsAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const MsTeamsAuthContainer: FunctionComponent<\n  MsTeamsAuthContainerProps\n> = ({ actionButton }) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rtk-auth\">\n      <div className=\"rtk-auth__header\">\n        <MsTeamsIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rtk-auth__title\">Microsoft Teams</div>\n      <div className=\"rtk-auth__description\">\n        {t(\"msTeamsConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["MsTeamsAuthContainer", "actionButton", "t", "useTranslations", "React", "MsTeamsIcon"], "mappings": ";;;;;AAYO,MAAMA,IAETA,CAAC;AAAA,EAAEC,cAAAA;AAAa,MAAM;AAClB,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB;AAE9B,SACGC,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,8CACZ,OAAI,EAAA,WAAU,mBACb,GAAAA,gBAAAA,EAAA,cAACC,GAAY,EAAA,QAAO,QAAO,OAAM,QAAM,GACvCD,gBAAAA,EAAA,cAAC,OAAKH,MAAAA,CAAa,CACrB,GACCG,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,kBAAkB,GAAA,iBAAe,GAChDA,gBAAAA,EAAA,cAAC,SAAI,WAAU,2BACZF,EAAE,oCAAoC,CACzC,CACF;AAEJ;"}