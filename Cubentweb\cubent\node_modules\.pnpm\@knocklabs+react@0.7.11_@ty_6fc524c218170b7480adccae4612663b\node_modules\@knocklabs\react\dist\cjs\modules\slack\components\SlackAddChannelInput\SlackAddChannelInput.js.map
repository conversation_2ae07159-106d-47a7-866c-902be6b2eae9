{"version": 3, "file": "SlackAddChannelInput.js", "sources": ["../../../../../../src/modules/slack/components/SlackAddChannelInput/SlackAddChannelInput.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport { useTranslations } from \"@knocklabs/react-core\";\nimport { useState } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport { Spinner } from \"../../../core\";\nimport \"../../theme.css\";\nimport ConnectionErrorInfoBoxes from \"../SlackChannelCombobox/SlackConnectionError\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAddChannelInputProps {\n  inErrorState: boolean;\n  connectedChannels: SlackChannelConnection[];\n  updateConnectedChannels: (channels: SlackChannelConnection[]) => void;\n  connectedChannelsError: string | null;\n  connectedChannelsUpdating: boolean;\n}\n\nconst SlackAddChannelInput: FunctionComponent<SlackAddChannelInputProps> = ({\n  inErrorState,\n  connectedChannels = [],\n  updateConnectedChannels,\n  connectedChannelsError,\n  connectedChannelsUpdating,\n}) => {\n  const { t } = useTranslations();\n  const [value, setValue] = useState<string | null>(null);\n  const [localError, setLocalError] = useState<string | null>(null);\n\n  const submitChannel = () => {\n    if (!value) {\n      return;\n    }\n\n    if (localError) {\n      setLocalError(null);\n    }\n\n    const alreadyConnected = connectedChannels.find(\n      (channel) => channel.channel_id === value,\n    );\n\n    if (alreadyConnected) {\n      setValue(\"\");\n      return setLocalError(t(\"slackChannelAlreadyConnected\") || \"\");\n    }\n\n    const channelsToSendToKnock = [...connectedChannels, { channel_id: value }];\n    updateConnectedChannels(channelsToSendToKnock);\n    setValue(\"\");\n  };\n\n  return (\n    <div className=\"rsk-connect-channel\">\n      <input\n        className={`rsk-connect-channel__input ${(inErrorState || !!localError) && !value && \"rsk-connect-channel__input--error\"}`}\n        tabIndex={-1}\n        id=\"slack-channel-search\"\n        type=\"text\"\n        placeholder={\n          localError || connectedChannelsError || t(\"slackChannelId\")\n        }\n        onChange={(e) => setValue(e.target.value)}\n        value={value || \"\"}\n      />\n      <button className=\"rsk-connect-channel__button\" onClick={submitChannel}>\n        {connectedChannelsUpdating ? (\n          <Spinner size=\"15px\" thickness={3} />\n        ) : (\n          <SlackIcon height=\"16px\" width=\"16px\" />\n        )}\n        {t(\"slackConnectChannel\")}\n      </button>\n      <ConnectionErrorInfoBoxes />\n    </div>\n  );\n};\n\nexport default SlackAddChannelInput;\n"], "names": ["SlackAddChannelInput", "inErrorState", "connectedChannels", "updateConnectedChannels", "connectedChannelsError", "connectedChannelsUpdating", "t", "useTranslations", "value", "setValue", "useState", "localError", "setLocalError", "submitChannel", "find", "channel", "channel_id", "channelsToSendToKnock", "React", "e", "target", "Spinner", "SlackIcon", "ConnectionErrorInfoBoxes"], "mappings": "8bAoBMA,EAAqEA,CAAC,CAC1EC,aAAAA,EACAC,kBAAAA,EAAoB,CAAE,EACtBC,wBAAAA,EACAC,uBAAAA,EACAC,0BAAAA,CACF,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxB,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAAwB,IAAI,EAChD,CAACC,EAAYC,CAAa,EAAIF,EAAAA,SAAwB,IAAI,EAE1DG,EAAgBA,IAAM,CAC1B,GAAI,CAACL,EACH,OAWF,GARIG,GACFC,EAAc,IAAI,EAGKV,EAAkBY,KAC5BC,GAAAA,EAAQC,aAAeR,CACtC,EAGEC,OAAAA,EAAS,EAAE,EACJG,EAAcN,EAAE,8BAA8B,GAAK,EAAE,EAGxDW,MAAAA,EAAwB,CAAC,GAAGf,EAAmB,CAAEc,WAAYR,CAAAA,CAAO,EAC1EL,EAAwBc,CAAqB,EAC7CR,EAAS,EAAE,CACb,EAEA,OACGS,EAAAA,QAAA,cAAA,MAAA,CAAI,UAAU,qBAAA,EACZA,EAAAA,QAAA,cAAA,QAAA,CACC,UAAW,+BAA+BjB,GAAgB,CAAC,CAACU,IAAe,CAACH,GAAS,mCAAmC,GACxH,SAAU,GACV,GAAG,uBACH,KAAK,OACL,YACEG,GAAcP,GAA0BE,EAAE,gBAAgB,EAE5D,SAAWa,GAAMV,EAASU,EAAEC,OAAOZ,KAAK,EACxC,MAAOA,GAAS,GAAG,EAEpBU,EAAA,QAAA,cAAA,SAAA,CAAO,UAAU,8BAA8B,QAASL,CAAAA,EACtDR,EACCa,EAAAA,QAAA,cAACG,WAAQ,KAAK,OAAO,UAAW,CAAA,CAAK,EAErCH,UAAA,cAACI,EAAAA,UAAU,CAAA,OAAO,OAAO,MAAM,MAAA,CAChC,EACAhB,EAAE,qBAAqB,CAC1B,EACAY,EAAA,QAAA,cAACK,MAAwB,CAC3B,CAEJ"}