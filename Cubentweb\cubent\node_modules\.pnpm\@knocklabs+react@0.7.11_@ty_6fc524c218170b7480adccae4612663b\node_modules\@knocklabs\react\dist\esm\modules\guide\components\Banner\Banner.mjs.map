{"version": 3, "file": "Banner.mjs", "sources": ["../../../../../../src/modules/guide/components/Banner/Banner.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport { <PERSON>tonContent, TargetButton, TargetButtonWithGuide } from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"banner\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"BannerView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"BannerView.Content\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"BannerView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-banner__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"BannerView.Body\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"BannerView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\"knock-guide-banner__action\", className)}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"BannerView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-banner__action knock-guide-banner__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"BannerView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-banner__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"BannerView.DismissButton\";\n\ntype BannerContent = {\n  title: string;\n  body: string;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: BannerContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n}> = ({ content, colorMode = \"light\", onDismiss, onButtonClick }) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      <Actions>\n        {content.secondary_button && (\n          <SecondaryButton\n            text={content.secondary_button.text}\n            action={content.secondary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.secondary_button!;\n                onButtonClick(e, { name: \"secondary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.primary_button && (\n          <PrimaryButton\n            text={content.primary_button.text}\n            action={content.primary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.primary_button!;\n                onButtonClick(e, { name: \"primary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.dismissible && <DismissButton onClick={onDismiss} />}\n      </Actions>\n    </Root>\n  );\n};\nDefaultView.displayName = \"BannerView.Default\";\n\ntype BannerProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n};\n\nexport const Banner: React.FC<BannerProps> = ({ guideKey, onButtonClick }) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as BannerContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n    />\n  );\n};\nBanner.displayName = \"Banner\";\n\nexport const BannerView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(BannerView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Title", "title", "Body", "body", "__html", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "secondary_button", "e", "name", "primary_button", "dismissible", "Banner", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;AASA,MAAMA,IAAe,UAEfC,IAEFA,CAAC;AAAA,EAAEC,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,sBAAsBH,CAAS,GAAG,GAAIC,EAAAA,GACxDF,CACH;AAGJD,EAAKM,cAAc;AAEnB,MAAMC,IAEFA,CAAC;AAAA,EAAEN,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,+BAA+BH,CAAS,GAAG,GAAIC,EAAAA,GACjEF,CACH;AAGJM,EAAQD,cAAc;AAEtB,MAAME,IAEFA,CAAC;AAAA,EAAEC,OAAAA;AAAAA,EAAOP,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAE9BC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,6BAA6BH,CAAS,GAAG,GAAIC,EAAAA,GAC/DM,CACH;AAGJD,EAAMF,cAAc;AAEpB,MAAMI,IAAwEA,CAAC;AAAA,EAC7EC,MAAAA;AAAAA,EACAT,WAAAA;AAAAA,EACA,GAAGC;AACL,sCAEK,OACC,EAAA,WAAWE,EAAK,4BAA4BH,CAAS,GACrD,yBAAyB;AAAA,EAAEU,QAAQD;AAAAA,GAC/BR,GAAAA,EACJ,CAAA;AAGNO,EAAKJ,cAAc;AAEnB,MAAMO,IAEFA,CAAC;AAAA,EAAEZ,UAAAA;AAAAA,EAAUC,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWC,EAAK,+BAA+BH,CAAS,GAAG,GAAIC,EAAAA,GACjEF,CACH;AAGJY,EAAQP,cAAc;AAEtB,MAAMQ,IAEFA,CAAC;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQd,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YACC,WAAWC,EAAK,8BAA8BH,CAAS,GACvD,GAAIC,EAAAA,GAEHY,CACH;AAGJD,EAAcR,cAAc;AAE5B,MAAMW,IAEFA,CAAC;AAAA,EAAEF,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQd,WAAAA;AAAAA,EAAW,GAAGC;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YACC,WAAWC,EACT,oEACAH,CACF,GACA,GAAIC,EAAAA,GAEHY,CACH;AAGJE,EAAgBX,cAAc;AAE9B,MAAMY,IAAiEA,CAAC;AAAA,EACtEhB,WAAAA;AAAAA,EACA,GAAGC;AACL,sCAEK,UAAO,EAAA,WAAWE,EAAK,6BAA6BH,CAAS,GAAG,GAAIC,KACnEC,gBAAAA,EAAA,cAAC,SACC,OAAM,8BACN,OAAM,MACN,QAAO,MACP,MAAK,UAEJA,gBAAAA,EAAA,cAAA,KAAA,EAAE,MAAK,WAAU,UAAS,WAAU,UAAS,aAC3CA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,uFAAA,CAAsF,GAC7FA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,uFAAqF,CAC/F,CACF,CACF;AAGJc,EAAcZ,cAAc;AAU5B,MAAMa,IAKDA,CAAC;AAAA,EAAEC,SAAAA;AAAAA,EAASC,WAAAA,IAAY;AAAA,EAASC,WAAAA;AAAAA,EAAWC,eAAAA;AAAc,MAE1DnB,gBAAAA,EAAA,cAAAJ,GAAA,EAAK,yBAAuBqB,EAAAA,mCAC1Bd,GACC,MAAAH,gBAAAA,EAAA,cAACI,GAAM,EAAA,OAAOY,EAAQX,OAAM,GAC3BL,gBAAAA,EAAA,cAAAM,GAAA,EAAK,MAAMU,EAAQT,MAAK,CAC3B,GACCP,gBAAAA,EAAA,cAAAS,GAAA,MACEO,EAAQI,oDACNP,GACC,EAAA,MAAMG,EAAQI,iBAAiBT,MAC/B,QAAQK,EAAQI,iBAAiBR,QACjC,SAAUS,CAAMA,MAAA;AACd,MAAIF,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQI;AACjCD,IAAAA,EAAcE,GAAG;AAAA,MAAEC,MAAM;AAAA,MAAoBX,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAC7D,EAGL,CAAA,GAEAI,EAAQO,kDACNb,GACC,EAAA,MAAMM,EAAQO,eAAeZ,MAC7B,QAAQK,EAAQO,eAAeX,QAC/B,SAAUS,CAAMA,MAAA;AACd,MAAIF,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQO;AACjCJ,IAAAA,EAAcE,GAAG;AAAA,MAAEC,MAAM;AAAA,MAAkBX,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAE7D,EAAA,CAEH,GAEAI,EAAQQ,+CAAgBV,GAAc,EAAA,SAASI,EAAa,CAAA,CAC/D,CACF;AAGJH,EAAYb,cAAc;AAOnB,MAAMuB,IAAgCA,CAAC;AAAA,EAAEC,UAAAA;AAAAA,EAAUP,eAAAA;AAAc,MAAM;AACtE,QAAA;AAAA,IAAEQ,OAAAA;AAAAA,IAAOC,MAAAA;AAAAA,IAAMX,WAAAA;AAAAA,MAAcY,EAAS;AAAA,IAC1CC,KAAKJ;AAAAA,IACLK,MAAMpC;AAAAA,EAAAA,CACP;AAMD,SAJAK,EAAMgC,UAAU,MAAM;AAChBJ,IAAAA,OAAWK,WAAW;AAAA,EAAA,GACzB,CAACL,CAAI,CAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzB5B,gBAAAA,EAAA,cAAAe,GAAA,EACC,SAASa,EAAKZ,SACd,WAAAC,GACA,WAAW,MAAMW,EAAKM,eAAe,GACrC,eAAe,CAACb,GAAGc,MAAW;AAC5B,UAAMC,IAAW;AAAA,MAAE,GAAGD;AAAAA,MAAQJ,MAAM;AAAA,IAAe;AACnDH,WAAAA,EAAKS,iBAAiB;AAAA,MAAED,UAAAA;AAAAA,IAAAA,CAAU,GAE3BjB,IACHA,EAAcE,GAAG;AAAA,MAAEc,QAAAA;AAAAA,MAAQP,MAAAA;AAAAA,MAAMD,OAAAA;AAAAA,IAAAA,CAAO,IACxCW,EAA4BH,EAAOvB,MAAM;AAAA,EAAA,GAE/C;AAEN;AACAa,EAAOvB,cAAc;AAEd,MAAMqC,IAAa,CAAA;AAY1BC,OAAOC,OAAOF,GAAY;AAAA,EACxBG,SAAS3B;AAAAA,EACTnB,MAAAA;AAAAA,EACAO,SAAAA;AAAAA,EACAC,OAAAA;AAAAA,EACAE,MAAAA;AAAAA,EACAG,SAAAA;AAAAA,EACAC,eAAAA;AAAAA,EACAG,iBAAAA;AAAAA,EACAC,eAAAA;AACF,CAAC;"}