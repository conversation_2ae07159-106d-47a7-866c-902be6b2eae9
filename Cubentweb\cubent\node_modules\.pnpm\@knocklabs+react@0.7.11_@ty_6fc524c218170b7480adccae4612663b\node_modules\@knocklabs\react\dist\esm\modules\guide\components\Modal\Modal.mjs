import { useGuide as V } from "@knocklabs/react-core";
import * as d from "@radix-ui/react-dialog";
import n from "clsx";
import a from "react";
import { isValidHttpUrl as v, maybeNavigateToUrlWithDelay as h } from "../helpers.mjs";
/* empty css            */
const x = "modal", u = ({
  children: e,
  onOpenChange: t,
  ...l
}) => /* @__PURE__ */ a.createElement(d.<PERSON>, { defaultOpen: !0, onOpenChange: t, ...l }, /* @__PURE__ */ a.createElement(d.Portal, null, e));
u.displayName = "ModalView.Root";
const y = a.forwardRef(({
  className: e,
  ...t
}, l) => /* @__PURE__ */ a.createElement(d.Overlay, { className: n("knock-guide-modal__overlay", e), ref: l, ...t }));
y.displayName = "ModalView.Overlay";
const _ = a.forwardRef(({
  children: e,
  className: t,
  ...l
}, r) => /* @__PURE__ */ a.createElement(d.Content, { className: n("knock-guide-modal", t), ref: r, ...l }, e));
_.displayName = "ModalView.Content";
const M = ({
  children: e,
  className: t,
  ...l
}) => /* @__PURE__ */ a.createElement("div", { className: n("knock-guide-modal__header", t), ...l }, e);
M.displayName = "ModalView.Header";
const p = ({
  title: e,
  className: t,
  ...l
}) => /* @__PURE__ */ a.createElement(d.Title, { className: n("knock-guide-modal__title", t), ...l }, e);
p.displayName = "ModalView.Title";
const E = ({
  body: e,
  className: t,
  ...l
}) => /* @__PURE__ */ a.createElement(d.Description, { className: n("knock-guide-modal__body", t), dangerouslySetInnerHTML: {
  __html: e
}, ...l });
E.displayName = "ModalView.Body";
const g = ({
  children: e,
  className: t,
  alt: l,
  ...r
}) => /* @__PURE__ */ a.createElement("img", { className: n("knock-guide-modal__img", t), alt: l || "", ...r }, e);
g.displayName = "ModalView.Img";
const k = ({
  children: e,
  className: t,
  ...l
}) => /* @__PURE__ */ a.createElement("div", { className: n("knock-guide-modal__actions", t), ...l }, e);
k.displayName = "ModalView.Actions";
const f = ({
  text: e,
  action: t,
  className: l,
  ...r
}) => /* @__PURE__ */ a.createElement("button", { className: n("knock-guide-modal__action", l), ...r }, e);
f.displayName = "ModalView.PrimaryButton";
const N = ({
  text: e,
  action: t,
  className: l,
  ...r
}) => /* @__PURE__ */ a.createElement("button", { className: n("knock-guide-modal__action knock-guide-modal__action--secondary", l), ...r }, e);
N.displayName = "ModalView.SecondaryButton";
const w = ({
  className: e,
  ...t
}) => /* @__PURE__ */ a.createElement(d.Close, { className: n("knock-guide-modal__close", e), ...t }, /* @__PURE__ */ a.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "18", height: "18", fill: "none" }, /* @__PURE__ */ a.createElement("g", { fill: "#60646C", fillRule: "evenodd", clipRule: "evenodd" }, /* @__PURE__ */ a.createElement("path", { d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z" }), /* @__PURE__ */ a.createElement("path", { d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z" }))));
w.displayName = "ModalView.Close";
const b = ({
  content: e,
  colorMode: t = "light",
  onOpenChange: l,
  onDismiss: r,
  onButtonClick: o,
  onImageClick: s
}) => /* @__PURE__ */ a.createElement(u, { onOpenChange: l }, /* @__PURE__ */ a.createElement(y, null), /* @__PURE__ */ a.createElement(_, { "data-knock-color-mode": t, onPointerDownOutside: r }, /* @__PURE__ */ a.createElement(M, null, /* @__PURE__ */ a.createElement(p, { title: e.title }), e.dismissible && /* @__PURE__ */ a.createElement(w, { onClick: r })), /* @__PURE__ */ a.createElement(E, { body: e.body }), e.image && /* @__PURE__ */ a.createElement("a", { href: v(e.image.action) ? e.image.action : void 0, target: "_blank" }, /* @__PURE__ */ a.createElement(g, { src: e.image.url, alt: e.image.alt, onClick: (m) => {
  s && s(m, e.image);
} })), (e.primary_button || e.secondary_button) && /* @__PURE__ */ a.createElement(k, null, e.secondary_button && /* @__PURE__ */ a.createElement(N, { text: e.secondary_button.text, action: e.secondary_button.action, onClick: (m) => {
  if (o) {
    const {
      text: i,
      action: c
    } = e.secondary_button;
    o(m, {
      name: "secondary_button",
      text: i,
      action: c
    });
  }
} }), e.primary_button && /* @__PURE__ */ a.createElement(f, { text: e.primary_button.text, action: e.primary_button.action, onClick: (m) => {
  if (o) {
    const {
      text: i,
      action: c
    } = e.primary_button;
    o(m, {
      name: "primary_button",
      text: i,
      action: c
    });
  }
} }))));
b.displayName = "ModalView.Default";
const A = ({
  guideKey: e,
  onButtonClick: t,
  onImageClick: l
}) => {
  const {
    guide: r,
    step: o,
    colorMode: s
  } = V({
    key: e,
    type: x
  });
  return a.useEffect(() => {
    o && o.markAsSeen();
  }, [o]), !r || !o ? null : /* @__PURE__ */ a.createElement(b, { content: o.content, colorMode: s, onDismiss: () => o.markAsArchived(), onButtonClick: (m, i) => {
    const c = {
      ...i,
      type: "button_click"
    };
    return o.markAsInteracted({
      metadata: c
    }), t ? t(m, {
      button: i,
      step: o,
      guide: r
    }) : h(i.action);
  }, onImageClick: (m, i) => {
    const c = {
      ...i,
      type: "image_click"
    };
    if (o.markAsInteracted({
      metadata: c
    }), l)
      return l(m, {
        image: i,
        step: o,
        guide: r
      });
  } });
};
A.displayName = "Modal";
const R = {};
Object.assign(R, {
  Default: b,
  Root: u,
  Overlay: y,
  Content: _,
  Title: p,
  Body: E,
  Img: g,
  Actions: k,
  PrimaryButton: f,
  SecondaryButton: N,
  Close: w
});
export {
  A as Modal,
  R as ModalView
};
//# sourceMappingURL=Modal.mjs.map
