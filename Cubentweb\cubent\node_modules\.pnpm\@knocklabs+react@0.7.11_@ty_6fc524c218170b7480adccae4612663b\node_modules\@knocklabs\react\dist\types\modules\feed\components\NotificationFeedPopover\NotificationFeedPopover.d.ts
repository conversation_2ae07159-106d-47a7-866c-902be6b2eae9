import { Feed, FeedStoreState } from '@knocklabs/client';
import { Placement } from '@popperjs/core';
import { default as React, RefObject } from 'react';
import { NotificationFeedProps } from '../NotificationFeed';
type OnOpenOptions = {
    store: FeedStoreState;
    feedClient: Feed;
};
export interface NotificationFeedPopoverProps extends NotificationFeedProps {
    isVisible: boolean;
    onOpen?: (arg: OnOpenOptions) => void;
    onClose: (e: Event) => void;
    buttonRef: RefObject<HTMLElement | null>;
    closeOnClickOutside?: boolean;
    placement?: Placement;
}
export declare const NotificationFeedPopover: React.FC<NotificationFeedPopoverProps>;
export {};
//# sourceMappingURL=NotificationFeedPopover.d.ts.map