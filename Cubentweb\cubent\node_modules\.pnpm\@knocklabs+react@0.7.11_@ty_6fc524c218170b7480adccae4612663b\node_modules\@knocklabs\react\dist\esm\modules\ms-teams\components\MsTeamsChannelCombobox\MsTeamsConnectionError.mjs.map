{"version": 3, "file": "MsTeamsConnectionError.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsConnectionError.tsx"], "sourcesContent": ["import { useKnockMsTeamsClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\nconst MsTeamsConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <MsTeamsErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"msTeamsConnectionErrorOccurred\")\n            : t(\"msTeamsConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default MsTeamsConnectionError;\n"], "names": ["MsTeamsConnectionError", "t", "useTranslations", "connectionStatus", "useKnockMsTeamsClient", "React", "MsTeamsErrorMessage"], "mappings": ";;;AAKA,MAAMA,IAA4CA,MAAM;AAChD,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GACxB;AAAA,IAAEC,kBAAAA;AAAAA,MAAqBC,EAAsB;AAE/CD,SAAAA,MAAqB,kBAAkBA,MAAqB,UAE5DE,gBAAAA,EAAA,cAACC,GACC,EAAA,SAEML,EADJE,MAAqB,iBACf,mCACA,8BADgC,EAGxC,CAAA,IAIC;AACT;"}