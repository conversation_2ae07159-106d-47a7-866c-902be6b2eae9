import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';

interface UnitUsage {
  unitsUsed: number;
  unitsLimit: number;
  unitsRemaining: number;
  usagePercentage: number;
  subscriptionTier: string;
  lastResetDate?: string;
}

interface CubentUnitsSettingsProps {
  authToken?: string;
}

export const CubentUnitsSettings: React.FC<CubentUnitsSettingsProps> = ({ authToken }) => {
  const [usage, setUsage] = useState<UnitUsage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsage = async () => {
    if (!authToken) {
      setError('Not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('https://cubent.dev/api/extension/units/usage', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch usage: ${response.status}`);
      }

      const data = await response.json();
      setUsage(data);
    } catch (err) {
      console.error('Error fetching unit usage:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch usage');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsage();
  }, [authToken]);

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUsageStatus = (percentage: number) => {
    if (percentage >= 90) return { icon: AlertTriangle, text: 'Critical', color: 'destructive' };
    if (percentage >= 75) return { icon: AlertTriangle, text: 'Warning', color: 'warning' };
    return { icon: CheckCircle, text: 'Good', color: 'success' };
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Cubent Units
          </CardTitle>
          <CardDescription>Loading usage information...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cubent Units</CardTitle>
          <CardDescription>Your AI usage tracking</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchUsage} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!usage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cubent Units</CardTitle>
          <CardDescription>Please sign in to view your usage</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const status = getUsageStatus(usage.usagePercentage);
  const StatusIcon = status.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Cubent Units</span>
          <Button onClick={fetchUsage} variant="ghost" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardTitle>
        <CardDescription>Your AI usage tracking</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Usage Overview */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Usage</span>
            <div className="flex items-center gap-2">
              <StatusIcon className={`h-4 w-4 ${getUsageColor(usage.usagePercentage)}`} />
              <Badge variant={status.color as any}>{status.text}</Badge>
            </div>
          </div>
          
          <Progress 
            value={usage.usagePercentage} 
            className="h-2"
          />
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{usage.unitsUsed.toFixed(2)} used</span>
            <span>{usage.unitsRemaining.toFixed(2)} remaining</span>
          </div>
        </div>

        {/* Usage Stats */}
        <div className="grid grid-cols-2 gap-4 pt-2">
          <div className="text-center">
            <div className="text-2xl font-bold">{usage.unitsUsed.toFixed(1)}</div>
            <div className="text-xs text-muted-foreground">Units Used</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{usage.unitsLimit}</div>
            <div className="text-xs text-muted-foreground">Unit Limit</div>
          </div>
        </div>

        {/* Subscription Info */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Plan</span>
            <Badge variant="outline">{usage.subscriptionTier}</Badge>
          </div>
          {usage.lastResetDate && (
            <div className="flex items-center justify-between text-sm mt-1">
              <span className="text-muted-foreground">Last Reset</span>
              <span className="text-xs">
                {new Date(usage.lastResetDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {/* Warning Messages */}
        {usage.usagePercentage >= 90 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">Usage Critical</span>
            </div>
            <p className="text-xs text-red-700 mt-1">
              You've used {usage.usagePercentage.toFixed(1)}% of your Cubent units. 
              Consider upgrading your plan to continue using AI features.
            </p>
          </div>
        )}

        {usage.usagePercentage >= 75 && usage.usagePercentage < 90 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">Usage Warning</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              You've used {usage.usagePercentage.toFixed(1)}% of your Cubent units.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
