{"version": 3, "file": "bufferTime.js", "sources": ["../../src/internal/operators/bufferTime.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,4CAA2C;AAE3C,4CAA2C;AAE3C,mDAAkD;AAkElD,SAAgB,UAAU,CAAI,cAAsB;IAClD,IAAI,MAAM,GAAW,SAAS,CAAC,MAAM,CAAC;IAEtC,IAAI,SAAS,GAAkB,aAAK,CAAC;IACrC,IAAI,yBAAW,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;QAChD,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,MAAM,EAAE,CAAC;KACV;IAED,IAAI,sBAAsB,GAAW,IAAI,CAAC;IAC1C,IAAI,MAAM,IAAI,CAAC,EAAE;QACf,sBAAsB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KACvC;IAED,IAAI,aAAa,GAAW,MAAM,CAAC,iBAAiB,CAAC;IACrD,IAAI,MAAM,IAAI,CAAC,EAAE;QACf,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAI,cAAc,EAAE,sBAAsB,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAClH,CAAC,CAAC;AACJ,CAAC;AAtBD,gCAsBC;AAED;IACE,4BAAoB,cAAsB,EACtB,sBAA8B,EAC9B,aAAqB,EACrB,SAAwB;QAHxB,mBAAc,GAAd,cAAc,CAAQ;QACtB,2BAAsB,GAAtB,sBAAsB,CAAQ;QAC9B,kBAAa,GAAb,aAAa,CAAQ;QACrB,cAAS,GAAT,SAAS,CAAe;IAC5C,CAAC;IAED,iCAAI,GAAJ,UAAK,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAC9C,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CACjG,CAAC,CAAC;IACL,CAAC;IACH,yBAAC;AAAD,CAAC,AAZD,IAYC;AAED;IAAA;QACE,WAAM,GAAQ,EAAE,CAAC;IAEnB,CAAC;IAAD,cAAC;AAAD,CAAC,AAHD,IAGC;AAmBD;IAAsC,wCAAa;IAIjD,8BAAY,WAA4B,EACpB,cAAsB,EACtB,sBAA8B,EAC9B,aAAqB,EACrB,SAAwB;QAJ5C,YAKE,kBAAM,WAAW,CAAC,SAYnB;QAhBmB,oBAAc,GAAd,cAAc,CAAQ;QACtB,4BAAsB,GAAtB,sBAAsB,CAAQ;QAC9B,mBAAa,GAAb,aAAa,CAAQ;QACrB,eAAS,GAAT,SAAS,CAAe;QAPpC,cAAQ,GAAsB,EAAE,CAAC;QASvC,IAAM,OAAO,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC;QACnC,KAAI,CAAC,YAAY,GAAG,sBAAsB,IAAI,IAAI,IAAI,sBAAsB,GAAG,CAAC,CAAC;QACjF,IAAI,KAAI,CAAC,YAAY,EAAE;YACrB,IAAM,iBAAiB,GAAG,EAAE,UAAU,EAAE,KAAI,EAAE,OAAO,SAAA,EAAE,cAAc,gBAAA,EAAE,CAAC;YACxE,KAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACnH;aAAM;YACL,IAAM,UAAU,GAAG,EAAE,UAAU,EAAE,KAAI,EAAE,OAAO,SAAA,EAAE,CAAC;YACjD,IAAM,aAAa,GAAyB,EAAE,cAAc,gBAAA,EAAE,sBAAsB,wBAAA,EAAE,UAAU,EAAE,KAAI,EAAE,SAAS,WAAA,EAAE,CAAC;YACpH,KAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAsB,mBAAmB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC;YACzH,KAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAuB,sBAAsB,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC;SACnH;;IACH,CAAC;IAES,oCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,IAAI,mBAA+B,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAM,SAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAM,MAAM,GAAG,SAAO,CAAC,MAAM,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;gBACvC,mBAAmB,GAAG,SAAO,CAAC;aAC/B;SACF;QAED,IAAI,mBAAmB,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;SACxC;IACH,CAAC;IAES,qCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,iBAAM,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,wCAAS,GAAnB;QACQ,IAAA,SAAgC,EAA9B,sBAAQ,EAAE,4BAAW,CAAU;QACvC,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,SAAO,CAAC,MAAM,CAAC,CAAC;SAClC;QACD,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IAGD,2CAAY,GAAZ;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAES,2CAAY,GAAtB,UAAuB,OAAmB;QACxC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC3C,IAAM,iBAAiB,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,SAAA,EAAE,cAAc,gBAAA,EAAE,CAAC;YACxE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,0BAA0B,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACxH;IACH,CAAC;IAED,0CAAW,GAAX;QACE,IAAM,OAAO,GAAe,IAAI,OAAO,EAAK,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,2CAAY,GAAZ,UAAa,OAAmB;QAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,IAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,WAAW,IAAI,CAAC,EAAE;YACpB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/C;IACH,CAAC;IACH,2BAAC;AAAD,CAAC,AAzFD,CAAsC,uBAAU,GAyF/C;AAED,SAAS,0BAA0B,CAA6B,KAAU;IACxE,IAAM,UAAU,GAA8B,KAAK,CAAC,UAAU,CAAC;IAE/D,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;IAClC,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACtC;IAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACzC,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;KACxE;AACH,CAAC;AAED,SAAS,sBAAsB,CAAiD,KAA2B;IACjG,IAAA,qDAAsB,EAAE,qCAAc,EAAE,6BAAU,EAAE,2BAAS,CAAW;IAChF,IAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;IACzC,IAAM,MAAM,GAA0C,IAAI,CAAC;IAC3D,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAsB,mBAAmB,EAAE,cAAc,EAAE,EAAE,UAAU,YAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC;QAC5I,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;KAChD;AACH,CAAC;AAED,SAAS,mBAAmB,CAAI,GAAwB;IAC9C,IAAA,2BAAU,EAAE,qBAAO,CAAS;IACpC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC"}