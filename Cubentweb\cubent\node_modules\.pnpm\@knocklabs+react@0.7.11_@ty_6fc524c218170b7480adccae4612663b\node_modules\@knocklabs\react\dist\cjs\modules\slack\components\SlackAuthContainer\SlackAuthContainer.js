"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const r=require("react"),c=require("@knocklabs/react-core");;/* empty css               */const l=require("../SlackIcon/SlackIcon.js");;/* empty css            */const n=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=n(r),s=({actionButton:e})=>{const{t:a}=c.useTranslations();return t.default.createElement("div",{className:"rsk-auth"},t.default.createElement("div",{className:"rsk-auth__header"},t.default.createElement(l.SlackIcon,{height:"32px",width:"32px"}),t.default.createElement("div",null,e)),t.default.createElement("div",{className:"rsk-auth__title"},"Slack"),t.default.createElement("div",{className:"rsk-auth__description"},a("slackConnectContainerDescription")))};exports.SlackAuthContainer=s;
//# sourceMappingURL=SlackAuthContainer.js.map
