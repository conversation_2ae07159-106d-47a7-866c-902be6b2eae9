{"version": 3, "file": "Dropdown.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeed/Dropdown.tsx"], "sourcesContent": ["import { useKnockFeed } from \"@knocklabs/react-core\";\nimport React, { PropsWithChildren } from \"react\";\n\nimport { ChevronDown } from \"../../../core/components/Icons\";\n\nimport \"./styles.css\";\n\nexport type DropdownProps = {\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n};\n\nexport const Dropdown: React.FC<PropsWithChildren<DropdownProps>> = ({\n  children,\n  value,\n  onChange,\n}) => {\n  const { colorMode } = useKnockFeed();\n\n  return (\n    <div className={`rnf-dropdown rnf-dropdown--${colorMode}`}>\n      <select\n        aria-label=\"Select notification filter\"\n        value={value}\n        onChange={onChange}\n      >\n        {children}\n      </select>\n      <ChevronDown aria-hidden />\n    </div>\n  );\n};\n"], "names": ["Dropdown", "children", "value", "onChange", "colorMode", "useKnockFeed", "ChevronDown"], "mappings": ";;;;AAYO,MAAMA,IAAuDA,CAAC;AAAA,EACnEC,UAAAA;AAAAA,EACAC,OAAAA;AAAAA,EACAC,UAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEC,WAAAA;AAAAA,MAAcC,EAAa;AAEnC,yCACG,OAAI,EAAA,WAAW,8BAA8BD,CAAS,GAAA,mCACpD,UACC,EAAA,cAAW,8BACX,OAAAF,GACA,UAAAC,KAECF,CACH,mCACCK,GAAY,EAAA,eAAW,GAAA,CAAA,CAC1B;AAEJ;"}