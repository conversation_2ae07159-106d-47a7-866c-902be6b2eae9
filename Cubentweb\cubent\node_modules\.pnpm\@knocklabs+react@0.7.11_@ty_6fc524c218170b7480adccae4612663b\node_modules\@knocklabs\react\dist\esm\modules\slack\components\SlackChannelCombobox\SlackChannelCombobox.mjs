import n, { useMemo as d } from "react";
import { useTranslations as y, useKnockSlackClient as F, useSlackChannels as T, useConnectedSlackChannels as g } from "@knocklabs/react-core";
import { Combobox as l } from "@telegraph/combobox";
import { Icon as v, Lucide as f } from "@telegraph/icon";
import { Stack as u } from "@telegraph/layout";
import { Text as H } from "@telegraph/typography";
/* empty css               */
import { sortSlackChannelsAlphabetically as O } from "../../utils.mjs";
import D from "../SlackAddChannelInput/SlackAddChannelInput.mjs";
import I from "./SlackConnectionError.mjs";
import U from "./SlackErrorMessage.mjs";
/* empty css            */
const w = 1e3, Q = ({
  slackChannelsRecipientObject: _,
  queryOptions: p,
  inputMessages: o
}) => {
  const {
    t: m
  } = y(), {
    connectionStatus: r,
    errorLabel: s
  } = F(), {
    data: k,
    isLoading: E
  } = T({
    queryOptions: p
  }), t = d(() => O(k), [k]), {
    data: i,
    updateConnectedChannels: S,
    error: h,
    updating: L
  } = g({
    slackChannelsRecipientObject: _
  }), a = d(() => {
    const e = new Map(t.map((c) => [c.id, c]));
    return (i == null ? void 0 : i.filter((c) => e.has(c.channel_id || ""))) || [];
  }, [i, t]), C = d(() => r === "disconnected" || r === "error" || !!h, [h, r]), b = d(() => r === "connecting" || r === "disconnecting" || E, [r, E]), x = d(() => {
    const e = {
      disconnected: m("slackSearchbarDisconnected"),
      noChannelsConnected: m("slackSearchbarNoChannelsConnected"),
      noSlackChannelsFound: m("slackSearchbarNoChannelsFound"),
      channelsError: m("slackSearchbarChannelsError")
    };
    if (r === "disconnected")
      return (o == null ? void 0 : o.disconnected) || e.disconnected;
    if (r === "error")
      return (o == null ? void 0 : o.error) || s;
    if (!b && t.length === 0)
      return (o == null ? void 0 : o.noSlackChannelsFound) || e.noSlackChannelsFound;
    const c = (a == null ? void 0 : a.length) || 0;
    return a && c === 0 ? (o == null ? void 0 : o.noChannelsConnected) || e.noChannelsConnected : "";
  }, [r, b, t, a, o, s, m]), N = d(() => a.map((e) => e.channel_id), [a]);
  return t.length > w ? /* @__PURE__ */ n.createElement(D, { inErrorState: C, connectedChannels: a || [], updateConnectedChannels: S, connectedChannelsError: h, connectedChannelsUpdating: L }) : /* @__PURE__ */ n.createElement(u, { className: "tgph rsk-combobox__grid", gap: "3" }, /* @__PURE__ */ n.createElement(H, { color: "gray", size: "2", as: "div", minHeight: "8", className: "rsk-combobox__label" }, "Channels"), /* @__PURE__ */ n.createElement(l.Root, { value: N, onValueChange: (e) => {
    const c = e.map((A) => ({
      channel_id: A
    }));
    S(c).catch(console.error);
  }, placeholder: x ?? "", disabled: C || t.length === 0, errored: C, closeOnSelect: !1, layout: "wrap", modal: (
    // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
    !1
  ) }, /* @__PURE__ */ n.createElement(l.Trigger, null), /* @__PURE__ */ n.createElement(l.Content, null, /* @__PURE__ */ n.createElement(l.Search, { label: m("slackSearchChannels"), className: "rsk-combobox__search" }), /* @__PURE__ */ n.createElement(l.Options, { maxHeight: "36" }, t.map((e) => /* @__PURE__ */ n.createElement(l.Option, { key: e.id, value: e.id }, /* @__PURE__ */ n.createElement(u, { align: "center", gap: "1" }, /* @__PURE__ */ n.createElement(v, { icon: e.is_private ? f.Lock : f.Hash, size: "0", "aria-hidden": !0 }), e.name)))), /* @__PURE__ */ n.createElement(l.Empty, null))), /* @__PURE__ */ n.createElement(I, null), !!h && /* @__PURE__ */ n.createElement(U, { message: h }));
};
export {
  Q as SlackChannelCombobox
};
//# sourceMappingURL=SlackChannelCombobox.mjs.map
