import e from"./c.js";const r=Object.freeze(JSON.parse('{"displayName":"GLSL","fileTypes":["vs","fs","gs","vsh","fsh","gsh","vshader","fshader","gshader","vert","frag","geom","f.glsl","v.glsl","g.glsl"],"foldingStartMarker":"/\\\\*\\\\*|\\\\{\\\\s*$","foldingStopMarker":"\\\\*\\\\*/|^\\\\s*}","name":"glsl","patterns":[{"match":"\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\b","name":"keyword.control.glsl"},{"match":"\\\\b(void|bool|int|uint|float|vec2|vec3|vec4|bvec2|bvec3|bvec4|ivec2|ivec3|uvec2|uvec3|mat2|mat3|mat4|mat2x2|mat2x3|mat2x4|mat3x2|mat3x3|mat3x4|mat4x2|mat4x3|mat4x4|sampler[123|]D|samplerCube|sampler2DRect|sampler[12|]DShadow|sampler2DRectShadow|sampler[12|]DArray|sampler[12|]DArrayShadow|samplerBuffer|sampler2DMS|sampler2DMSArray|struct|isampler[123|]D|isamplerCube|isampler2DRect|isampler[12|]DArray|isamplerBuffer|isampler2DMS|isampler2DMSArray|usampler[123|]D|usamplerCube|usampler2DRect|usampler[12|]DArray|usamplerBuffer|usampler2DMS|usampler2DMSArray)\\\\b","name":"storage.type.glsl"},{"match":"\\\\b(attribute|centroid|const|flat|in|inout|invariant|noperspective|out|smooth|uniform|varying)\\\\b","name":"storage.modifier.glsl"},{"match":"\\\\b(gl_(?:BackColor|BackLightModelProduct|BackLightProduct|BackMaterial|BackSecondaryColor|ClipDistance|ClipPlane|ClipVertex|Color|DepthRange|DepthRangeParameters|EyePlaneQ|EyePlaneR|EyePlaneS|EyePlaneT|Fog|FogCoord|FogFragCoord|FogParameters|FragColor|FragCoord|FragDat|FragDept|FrontColor|FrontFacing|FrontLightModelProduct|FrontLightProduct|FrontMaterial|FrontSecondaryColor|InstanceID|Layer|LightModel|LightModelParameters|LightModelProducts|LightProducts|LightSource|LightSourceParameters|MaterialParameters|ModelViewMatrix|ModelViewMatrixInverse|ModelViewMatrixInverseTranspose|ModelViewMatrixTranspose|ModelViewProjectionMatrix|ModelViewProjectionMatrixInverse|ModelViewProjectionMatrixInverseTranspose|ModelViewProjectionMatrixTranspose|MultiTexCoord[0-7]|Normal|NormalMatrix|NormalScale|ObjectPlaneQ|ObjectPlaneR|ObjectPlaneS|ObjectPlaneT|Point|PointCoord|PointParameters|PointSize|Position|PrimitiveIDIn|ProjectionMatrix|ProjectionMatrixInverse|ProjectionMatrixInverseTranspose|ProjectionMatrixTranspose|SecondaryColor|TexCoord|TextureEnvColor|TextureMatrix|TextureMatrixInverse|TextureMatrixInverseTranspose|TextureMatrixTranspose|Vertex|VertexIDh))\\\\b","name":"support.variable.glsl"},{"match":"\\\\b(gl_Max(?:ClipPlane|CombinedTextureImageUnit|DrawBuffer|FragmentUniformComponent|Light|TextureCoord|TextureImageUnit|TextureUnit|VaryingFloat|VertexAttrib|VertexTextureImageUnit|VertexUniformComponent)s)\\\\b","name":"support.constant.glsl"},{"match":"\\\\b(abs|acos|all|any|asin|atan|ceil|clamp|cos|cross|degrees|dFdx|dFdy|distance|dot|equal|exp2??|faceforward|floor|fract|ftransform|fwidth|greaterThan|greaterThanEqual|inversesqrt|length|lessThan|lessThanEqual|log2??|matrixCompMult|max|min|mix|mod|noise[1-4]|normalize|not|notEqual|outerProduct|pow|radians|reflect|refract|shadow1D|shadow1DLod|shadow1DProj|shadow1DProjLod|shadow2D|shadow2DLod|shadow2DProj|shadow2DProjLod|sign|sin|smoothstep|sqrt|step|tan|texture1D|texture1DLod|texture1DProj|texture1DProjLod|texture2D|texture2DLod|texture2DProj|texture2DProjLod|texture3D|texture3DLod|texture3DProj|texture3DProjLod|textureCube|textureCubeLod|transpose)\\\\b","name":"support.function.glsl"},{"match":"\\\\b(asm|double|enum|extern|goto|inline|long|short|sizeof|static|typedef|union|unsigned|volatile)\\\\b","name":"invalid.illegal.glsl"},{"include":"source.c"}],"scopeName":"source.glsl","embeddedLangs":["c"]}')),a=[...e,r];export{a as default};
//# sourceMappingURL=glsl.js.map
