{"version": 3, "file": "helpers.mjs", "sources": ["../../../../../src/modules/guide/components/helpers.ts"], "sourcesContent": ["export const isValidHttpUrl = (input: string) => {\n  let url;\n\n  try {\n    url = new URL(input);\n  } catch (_) {\n    return false;\n  }\n\n  return url.protocol === \"http:\" || url.protocol === \"https:\";\n};\n\nexport const maybeNavigateToUrlWithDelay = (\n  url: string,\n  delay: number = 200,\n) => {\n  if (!window?.location) return;\n  if (!isValidHttpUrl(url)) return;\n\n  setTimeout(() => window.location.assign(url), delay);\n};\n"], "names": ["isValidHttpUrl", "input", "url", "URL", "protocol", "maybeNavigateToUrlWithDelay", "delay", "window", "location", "setTimeout", "assign"], "mappings": "AAAaA,MAAAA,IAAiBA,CAACC,MAAkB;AAC3CC,MAAAA;AAEA,MAAA;AACI,IAAAA,IAAA,IAAIC,IAAIF,CAAK;AAAA,UACT;AACH,WAAA;AAAA,EAAA;AAGT,SAAOC,EAAIE,aAAa,WAAWF,EAAIE,aAAa;AACtD,GAEaC,IAA8BA,CACzCH,GACAI,IAAgB,QACb;AACC,EAACC,yBAAQC,YACRR,EAAeE,CAAG,KAEvBO,WAAW,MAAMF,OAAOC,SAASE,OAAOR,CAAG,GAAGI,CAAK;AACrD;"}