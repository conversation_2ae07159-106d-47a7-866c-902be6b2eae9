import n, { use<PERSON>emo as c, useCallback as b } from "react";
import { useKnockMsTeamsClient as g, useMsTeamsChannels as x, useConnectedMsTeamsChannels as M } from "@knocklabs/react-core";
import { Combobox as t } from "@telegraph/combobox";
import { Box as y } from "@telegraph/layout";
import { sortByDisplayName as S } from "../../utils.mjs";
import O from "./MsTeamsErrorMessage.mjs";
const V = ({
  teamId: r,
  msTeamsChannelsRecipientObject: h,
  queryOptions: _
}) => {
  const {
    connectionStatus: s
  } = g(), {
    data: o = []
  } = x({
    teamId: r,
    queryOptions: _
  }), p = c(() => S(o), [o]), {
    data: a,
    updateConnectedChannels: u,
    error: l
  } = M({
    msTeamsChannelsRecipientObject: h
  }), C = c(() => s === "disconnected" || s === "error" || !!l, [s, l]), E = c(() => s === "connecting" || s === "disconnecting", [s]), i = b((e) => o.some((d) => d.id === e), [o]), T = c(() => a == null ? void 0 : a.filter((e) => e.ms_teams_channel_id && i(e.ms_teams_channel_id)).map((e) => e.ms_teams_channel_id), [a, i]);
  return /* @__PURE__ */ n.createElement(n.Fragment, null, /* @__PURE__ */ n.createElement(y, { w: "full", minW: "0" }, /* @__PURE__ */ n.createElement(t.Root, { value: T, onValueChange: (e) => {
    const d = e.map((m) => ({
      ms_teams_team_id: r,
      ms_teams_channel_id: m
    })), f = [...(a == null ? void 0 : a.filter((m) => !m.ms_teams_channel_id || !i(m.ms_teams_channel_id))) ?? [], ...d];
    u(f).catch(console.error);
  }, placeholder: "Select channels", disabled: r === void 0 || C || E || o.length === 0, closeOnSelect: !1, layout: "wrap", modal: (
    // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
    !1
  ) }, /* @__PURE__ */ n.createElement(t.Trigger, null), /* @__PURE__ */ n.createElement(t.Content, null, /* @__PURE__ */ n.createElement(t.Search, { className: "rtk-combobox__search" }), /* @__PURE__ */ n.createElement(t.Options, { maxHeight: "36" }, p.map((e) => /* @__PURE__ */ n.createElement(t.Option, { key: e.id, value: e.id }, e.displayName))), /* @__PURE__ */ n.createElement(t.Empty, null)))), !!l && /* @__PURE__ */ n.createElement(O, { message: l }));
};
export {
  V as MsTeamsChannelInTeamCombobox
};
//# sourceMappingURL=MsTeamsChannelInTeamCombobox.mjs.map
