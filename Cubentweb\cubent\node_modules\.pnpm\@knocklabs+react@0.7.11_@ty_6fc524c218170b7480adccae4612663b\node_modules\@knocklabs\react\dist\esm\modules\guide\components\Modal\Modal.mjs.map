{"version": 3, "file": "Modal.mjs", "sources": ["../../../../../../src/modules/guide/components/Modal/Modal.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { isValidHttpUrl, maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport {\n  ButtonContent,\n  ImageContent,\n  TargetButton,\n  TargetButtonWithGuide,\n  TargetImage,\n  TargetImageWithGuide,\n} from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"modal\";\n\ntype RootProps = Omit<\n  React.ComponentPropsWithoutRef<typeof Dialog.Root>,\n  \"modal\"\n> &\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>;\n\nconst Root = ({ children, onOpenChange, ...props }: RootProps) => {\n  return (\n    <Dialog.Root defaultOpen onOpenChange={onOpenChange} {...props}>\n      <Dialog.Portal>{children}</Dialog.Portal>\n    </Dialog.Root>\n  );\n};\nRoot.displayName = \"ModalView.Root\";\n\ntype OverlayProps = React.ComponentPropsWithoutRef<typeof Dialog.Overlay> &\n  React.ComponentPropsWithRef<\"div\">;\ntype OverlayRef = React.ElementRef<\"div\">;\n\nconst Overlay = React.forwardRef<OverlayRef, OverlayProps>(\n  ({ className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Overlay\n        className={clsx(\"knock-guide-modal__overlay\", className)}\n        ref={forwardedRef}\n        {...props}\n      />\n    );\n  },\n);\nOverlay.displayName = \"ModalView.Overlay\";\n\ntype ContentProps = React.ComponentPropsWithoutRef<typeof Dialog.Content> &\n  React.ComponentPropsWithRef<\"div\">;\ntype ContentRef = React.ElementRef<\"div\">;\n\nconst Content = React.forwardRef<ContentRef, ContentProps>(\n  ({ children, className, ...props }, forwardedRef) => {\n    return (\n      <Dialog.Content\n        className={clsx(\"knock-guide-modal\", className)}\n        ref={forwardedRef}\n        {...props}\n      >\n        {children}\n      </Dialog.Content>\n    );\n  },\n);\nContent.displayName = \"ModalView.Content\";\n\nconst Header: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__header\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nHeader.displayName = \"ModalView.Header\";\n\ntype TitleProps = React.ComponentPropsWithoutRef<typeof Dialog.Title> &\n  React.ComponentPropsWithRef<\"div\"> & {\n    title: string;\n  };\n\nconst Title = ({ title, className, ...props }: TitleProps) => {\n  return (\n    <Dialog.Title\n      className={clsx(\"knock-guide-modal__title\", className)}\n      {...props}\n    >\n      {title}\n    </Dialog.Title>\n  );\n};\nTitle.displayName = \"ModalView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <Dialog.Description\n      className={clsx(\"knock-guide-modal__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"ModalView.Body\";\n\nconst Img: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"img\">>\n> = ({ children, className, alt, ...props }) => {\n  return (\n    <img\n      className={clsx(\"knock-guide-modal__img\", className)}\n      alt={alt || \"\"}\n      {...props}\n    >\n      {children}\n    </img>\n  );\n};\nImg.displayName = \"ModalView.Img\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-modal__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"ModalView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button className={clsx(\"knock-guide-modal__action\", className)} {...props}>\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"ModalView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-modal__action knock-guide-modal__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"ModalView.SecondaryButton\";\n\ntype CloseProps = React.ComponentPropsWithoutRef<typeof Dialog.Close> &\n  React.ComponentPropsWithRef<\"button\">;\n\nconst Close = ({ className, ...props }: CloseProps) => {\n  return (\n    <Dialog.Close\n      className={clsx(\"knock-guide-modal__close\", className)}\n      {...props}\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </Dialog.Close>\n  );\n};\nClose.displayName = \"ModalView.Close\";\n\ntype ModalContent = {\n  title: string;\n  body: string;\n  image?: ImageContent;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: ModalContent;\n  colorMode?: ColorMode;\n  onOpenChange?: (open: boolean) => void;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n  onImageClick?: (e: React.MouseEvent, image: TargetImage) => void;\n}> = ({\n  content,\n  colorMode = \"light\",\n  onOpenChange,\n  onDismiss,\n  onButtonClick,\n  onImageClick,\n}) => {\n  return (\n    <Root onOpenChange={onOpenChange}>\n      <Overlay />\n      {/* Must pass color mode to content for css variables to be set properly */}\n      <Content\n        data-knock-color-mode={colorMode}\n        onPointerDownOutside={onDismiss}\n      >\n        <Header>\n          <Title title={content.title} />\n          {content.dismissible && <Close onClick={onDismiss} />}\n        </Header>\n\n        <Body body={content.body} />\n\n        {content.image && (\n          <a\n            href={\n              isValidHttpUrl(content.image.action)\n                ? content.image.action\n                : undefined\n            }\n            target=\"_blank\"\n          >\n            <Img\n              src={content.image.url}\n              alt={content.image.alt}\n              onClick={(e) => {\n                if (onImageClick) {\n                  onImageClick(e, content.image!);\n                }\n              }}\n            />\n          </a>\n        )}\n\n        {(content.primary_button || content.secondary_button) && (\n          <Actions>\n            {content.secondary_button && (\n              <SecondaryButton\n                text={content.secondary_button.text}\n                action={content.secondary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.secondary_button!;\n                    onButtonClick(e, {\n                      name: \"secondary_button\",\n                      text,\n                      action,\n                    });\n                  }\n                }}\n              />\n            )}\n            {content.primary_button && (\n              <PrimaryButton\n                text={content.primary_button.text}\n                action={content.primary_button.action}\n                onClick={(e) => {\n                  if (onButtonClick) {\n                    const { text, action } = content.primary_button!;\n                    onButtonClick(e, { name: \"primary_button\", text, action });\n                  }\n                }}\n              />\n            )}\n          </Actions>\n        )}\n      </Content>\n    </Root>\n  );\n};\nDefaultView.displayName = \"ModalView.Default\";\n\ntype ModalProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n  onImageClick?: (e: React.MouseEvent, target: TargetImageWithGuide) => void;\n};\n\nexport const Modal: React.FC<ModalProps> = ({\n  guideKey,\n  onButtonClick,\n  onImageClick,\n}) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as ModalContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n      onImageClick={(e, image) => {\n        const metadata = { ...image, type: \"image_click\" };\n        step.markAsInteracted({ metadata });\n\n        if (onImageClick) {\n          return onImageClick(e, { image, step, guide });\n        }\n      }}\n    />\n  );\n};\nModal.displayName = \"Modal\";\n\nexport const ModalView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Overlay: typeof Overlay;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Img: typeof Img;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  Close: typeof Close;\n};\n\nObject.assign(ModalView, {\n  Default: DefaultView,\n  Root,\n  Overlay,\n  Content,\n  Title,\n  Body,\n  Img,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  Close,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "onOpenChange", "props", "React", "Dialog", "displayName", "Overlay", "forwardRef", "className", "forwardedRef", "clsx", "Content", "Header", "Title", "title", "Body", "body", "__html", "Img", "alt", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "Close", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "onImageClick", "dismissible", "image", "isValidHttpUrl", "undefined", "url", "e", "primary_button", "secondary_button", "name", "Modal", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON>iew", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAiBA,MAAMA,IAAe,SAQfC,IAAOA,CAAC;AAAA,EAAEC,UAAAA;AAAAA,EAAUC,cAAAA;AAAAA,EAAc,GAAGC;AAAiB,MAEvDC,gBAAAA,EAAA,cAAAC,EAAO,MAAP,EAAY,aAAW,IAAC,cAAAH,GAA4B,GAAIC,EAAAA,GACtDC,gBAAAA,EAAA,cAAAC,EAAO,QAAP,MAAeJ,CAAS,CAC3B;AAGJD,EAAKM,cAAc;AAMnB,MAAMC,IAAUH,EAAMI,WACpB,CAAC;AAAA,EAAEC,WAAAA;AAAAA,EAAW,GAAGN;AAAM,GAAGO,MAErBN,gBAAAA,EAAA,cAAAC,EAAO,SAAP,EACC,WAAWM,EAAK,8BAA8BF,CAAS,GACvD,KAAKC,GACL,GAAIP,EACJ,CAAA,CAGR;AACAI,EAAQD,cAAc;AAMtB,MAAMM,IAAUR,EAAMI,WACpB,CAAC;AAAA,EAAEP,UAAAA;AAAAA,EAAUQ,WAAAA;AAAAA,EAAW,GAAGN;AAAM,GAAGO,MAE/BN,gBAAAA,EAAA,cAAAC,EAAO,SAAP,EACC,WAAWM,EAAK,qBAAqBF,CAAS,GAC9C,KAAKC,GACDP,GAAAA,KAEHF,CACH,CAGN;AACAW,EAAQN,cAAc;AAEtB,MAAMO,IAEFA,CAAC;AAAA,EAAEZ,UAAAA;AAAAA,EAAUQ,WAAAA;AAAAA,EAAW,GAAGN;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWO,EAAK,6BAA6BF,CAAS,GAAG,GAAIN,EAAAA,GAC/DF,CACH;AAGJY,EAAOP,cAAc;AAOrB,MAAMQ,IAAQA,CAAC;AAAA,EAAEC,OAAAA;AAAAA,EAAON,WAAAA;AAAAA,EAAW,GAAGN;AAAkB,MAEpDC,gBAAAA,EAAA,cAACC,EAAO,OAAP,EACC,WAAWM,EAAK,4BAA4BF,CAAS,GACrD,GAAIN,EAAAA,GAEHY,CACH;AAGJD,EAAMR,cAAc;AAEpB,MAAMU,IAAwEA,CAAC;AAAA,EAC7EC,MAAAA;AAAAA,EACAR,WAAAA;AAAAA,EACA,GAAGN;AACL,MAEIC,gBAAAA,EAAA,cAACC,EAAO,aAAP,EACC,WAAWM,EAAK,2BAA2BF,CAAS,GACpD,yBAAyB;AAAA,EAAES,QAAQD;AAAAA,GAC/Bd,GAAAA,EACJ,CAAA;AAGNa,EAAKV,cAAc;AAEnB,MAAMa,IAEFA,CAAC;AAAA,EAAElB,UAAAA;AAAAA,EAAUQ,WAAAA;AAAAA,EAAWW,KAAAA;AAAAA,EAAK,GAAGjB;AAAM,MAErCC,gBAAAA,EAAA,cAAA,OAAA,EACC,WAAWO,EAAK,0BAA0BF,CAAS,GACnD,KAAKW,KAAO,IACRjB,GAAAA,EAAAA,GAEHF,CACH;AAGJkB,EAAIb,cAAc;AAElB,MAAMe,IAEFA,CAAC;AAAA,EAAEpB,UAAAA;AAAAA,EAAUQ,WAAAA;AAAAA,EAAW,GAAGN;AAAM,MAEjCC,gBAAAA,EAAA,cAAC,SAAI,WAAWO,EAAK,8BAA8BF,CAAS,GAAG,GAAIN,EAAAA,GAChEF,CACH;AAGJoB,EAAQf,cAAc;AAEtB,MAAMgB,IAEFA,CAAC;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQf,WAAAA;AAAAA,EAAW,GAAGN;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YAAO,WAAWO,EAAK,6BAA6BF,CAAS,GAAG,GAAIN,EAAAA,GAClEoB,CACH;AAGJD,EAAchB,cAAc;AAE5B,MAAMmB,IAEFA,CAAC;AAAA,EAAEF,MAAAA;AAAAA,EAAMC,QAAAA;AAAAA,EAAQf,WAAAA;AAAAA,EAAW,GAAGN;AAAM,MAErCC,gBAAAA,EAAA,cAAC,YACC,WAAWO,EACT,kEACAF,CACF,GACA,GAAIN,EAAAA,GAEHoB,CACH;AAGJE,EAAgBnB,cAAc;AAK9B,MAAMoB,IAAQA,CAAC;AAAA,EAAEjB,WAAAA;AAAAA,EAAW,GAAGN;AAAkB,sCAE5CE,EAAO,OAAP,EACC,WAAWM,EAAK,4BAA4BF,CAAS,GACrD,GAAIN,EAAAA,mCAEH,OACC,EAAA,OAAM,8BACN,OAAM,MACN,QAAO,MACP,MAAK,OAEL,GAAAC,gBAAAA,EAAA,cAAC,OAAE,MAAK,WAAU,UAAS,WAAU,UAAS,aAC3CA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,wFAAsF,GAC7FA,gBAAAA,EAAA,cAAA,QAAA,EAAK,GAAE,sFAAqF,CAAA,CAC/F,CACF,CACF;AAGJsB,EAAMpB,cAAc;AAWpB,MAAMqB,IAODA,CAAC;AAAA,EACJC,SAAAA;AAAAA,EACAC,WAAAA,IAAY;AAAA,EACZ3B,cAAAA;AAAAA,EACA4B,WAAAA;AAAAA,EACAC,eAAAA;AAAAA,EACAC,cAAAA;AACF,MAEK5B,gBAAAA,EAAA,cAAAJ,GAAA,EAAK,cAAAE,KACJE,gBAAAA,EAAA,cAACG,GAAO,IAAA,GAEPH,gBAAAA,EAAA,cAAAQ,GAAA,EACC,yBAAuBiB,GACvB,sBAAsBC,KAErB1B,gBAAAA,EAAA,cAAAS,GAAA,MACET,gBAAAA,EAAA,cAAAU,GAAA,EAAM,OAAOc,EAAQb,MAAM,CAAA,GAC3Ba,EAAQK,eAAgB7B,gBAAAA,EAAA,cAAAsB,GAAA,EAAM,SAASI,EAAAA,CAAa,CACvD,GAEC1B,gBAAAA,EAAA,cAAAY,GAAA,EAAK,MAAMY,EAAQX,KAAK,CAAA,GAExBW,EAAQM,SACN9B,gBAAAA,EAAA,cAAA,KAAA,EACC,MACE+B,EAAeP,EAAQM,MAAMV,MAAM,IAC/BI,EAAQM,MAAMV,SACdY,QAEN,QAAO,SAAA,GAENhC,gBAAAA,EAAA,cAAAe,GAAA,EACC,KAAKS,EAAQM,MAAMG,KACnB,KAAKT,EAAQM,MAAMd,KACnB,SAAUkB,CAAMA,MAAA;AACd,EAAIN,KACWM,EAAAA,GAAGV,EAAQM,KAAM;AAElC,EAAA,CAAE,CAEN,IAGAN,EAAQW,kBAAkBX,EAAQY,qBACjCpC,gBAAAA,EAAA,cAAAiB,GAAA,MACEO,EAAQY,oDACNf,GACC,EAAA,MAAMG,EAAQY,iBAAiBjB,MAC/B,QAAQK,EAAQY,iBAAiBhB,QACjC,SAAUc,CAAMA,MAAA;AACd,MAAIP,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQY;AACjCT,IAAAA,EAAcO,GAAG;AAAA,MACfG,MAAM;AAAA,MACNlB,MAAAA;AAAAA,MACAC,QAAAA;AAAAA,IAAAA,CACD;AAAA,EAAA;AACH,EAGL,CAAA,GACAI,EAAQW,kDACNjB,GACC,EAAA,MAAMM,EAAQW,eAAehB,MAC7B,QAAQK,EAAQW,eAAef,QAC/B,SAAUc,CAAMA,MAAA;AACd,MAAIP,GAAe;AACX,UAAA;AAAA,MAAER,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,QAAWI,EAAQW;AACjCR,IAAAA,EAAcO,GAAG;AAAA,MAAEG,MAAM;AAAA,MAAkBlB,MAAAA;AAAAA,MAAMC,QAAAA;AAAAA,IAAAA,CAAQ;AAAA,EAAA;AAC3D,EAGL,CAAA,CACH,CAEJ,CACF;AAGJG,EAAYrB,cAAc;AAQnB,MAAMoC,IAA8BA,CAAC;AAAA,EAC1CC,UAAAA;AAAAA,EACAZ,eAAAA;AAAAA,EACAC,cAAAA;AACF,MAAM;AACE,QAAA;AAAA,IAAEY,OAAAA;AAAAA,IAAOC,MAAAA;AAAAA,IAAMhB,WAAAA;AAAAA,MAAciB,EAAS;AAAA,IAC1CC,KAAKJ;AAAAA,IACLK,MAAMjD;AAAAA,EAAAA,CACP;AAMD,SAJAK,EAAM6C,UAAU,MAAM;AAChBJ,IAAAA,OAAWK,WAAW;AAAA,EAAA,GACzB,CAACL,CAAI,CAAC,GAEL,CAACD,KAAS,CAACC,IAAa,OAGzBzC,gBAAAA,EAAA,cAAAuB,GAAA,EACC,SAASkB,EAAKjB,SACd,WAAAC,GACA,WAAW,MAAMgB,EAAKM,eAAe,GACrC,eAAe,CAACb,GAAGc,MAAW;AAC5B,UAAMC,IAAW;AAAA,MAAE,GAAGD;AAAAA,MAAQJ,MAAM;AAAA,IAAe;AACnDH,WAAAA,EAAKS,iBAAiB;AAAA,MAAED,UAAAA;AAAAA,IAAAA,CAAU,GAE3BtB,IACHA,EAAcO,GAAG;AAAA,MAAEc,QAAAA;AAAAA,MAAQP,MAAAA;AAAAA,MAAMD,OAAAA;AAAAA,IAAAA,CAAO,IACxCW,EAA4BH,EAAO5B,MAAM;AAAA,EAAA,GAE/C,cAAc,CAACc,GAAGJ,MAAU;AAC1B,UAAMmB,IAAW;AAAA,MAAE,GAAGnB;AAAAA,MAAOc,MAAM;AAAA,IAAc;AAGjD,QAFAH,EAAKS,iBAAiB;AAAA,MAAED,UAAAA;AAAAA,IAAAA,CAAU,GAE9BrB;AACF,aAAOA,EAAaM,GAAG;AAAA,QAAEJ,OAAAA;AAAAA,QAAOW,MAAAA;AAAAA,QAAMD,OAAAA;AAAAA,MAAAA,CAAO;AAAA,EAC/C,GAEF;AAEN;AACAF,EAAMpC,cAAc;AAEb,MAAMkD,IAAY,CAAA;AAczBC,OAAOC,OAAOF,GAAW;AAAA,EACvBG,SAAShC;AAAAA,EACT3B,MAAAA;AAAAA,EACAO,SAAAA;AAAAA,EACAK,SAAAA;AAAAA,EACAE,OAAAA;AAAAA,EACAE,MAAAA;AAAAA,EACAG,KAAAA;AAAAA,EACAE,SAAAA;AAAAA,EACAC,eAAAA;AAAAA,EACAG,iBAAAA;AAAAA,EACAC,OAAAA;AACF,CAAC;"}