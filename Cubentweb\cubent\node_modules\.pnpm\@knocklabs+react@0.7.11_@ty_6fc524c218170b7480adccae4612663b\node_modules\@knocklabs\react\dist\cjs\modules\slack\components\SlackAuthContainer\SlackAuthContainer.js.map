{"version": 3, "file": "SlackAuthContainer.js", "sources": ["../../../../../../src/modules/slack/components/SlackAuthContainer/SlackAuthContainer.tsx"], "sourcesContent": ["import { useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { SlackIcon } from \"../SlackIcon\";\n\nimport \"./styles.css\";\n\nexport interface SlackAuthContainerProps {\n  actionButton: React.ReactElement;\n}\n\nexport const SlackAuthContainer: FunctionComponent<SlackAuthContainerProps> = ({\n  actionButton,\n}) => {\n  const { t } = useTranslations();\n\n  return (\n    <div className=\"rsk-auth\">\n      <div className=\"rsk-auth__header\">\n        <SlackIcon height=\"32px\" width=\"32px\" />\n        <div>{actionButton}</div>\n      </div>\n      <div className=\"rsk-auth__title\">Slack</div>\n      <div className=\"rsk-auth__description\">\n        {t(\"slackConnectContainerDescription\")}\n      </div>\n    </div>\n  );\n};\n"], "names": ["SlackAuthContainer", "actionButton", "t", "useTranslations", "React", "SlackIcon"], "mappings": "uTAYaA,EAAiEA,CAAC,CAC7EC,aAAAA,CACF,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EAE9B,OACGC,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,oCACZ,MAAI,CAAA,UAAU,kBACb,EAAAA,UAAA,cAACC,EAAU,UAAA,CAAA,OAAO,OAAO,MAAM,OAAM,EACrCD,EAAAA,QAAA,cAAC,MAAKH,KAAAA,CAAa,CACrB,EACCG,EAAA,QAAA,cAAA,MAAA,CAAI,UAAU,iBAAkB,EAAA,OAAK,EACtCA,EAAAA,QAAA,cAAC,OAAI,UAAU,yBACZF,EAAE,kCAAkC,CACvC,CACF,CAEJ"}