import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Manual extension registration endpoint
 * This endpoint can be called by the extension when it connects to Cubent Cloud
 * to register itself with the webapp
 */
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Parse request body for session info (optional)
    let sessionData: any = {};
    try {
      const body = await request.json();
      sessionData = body;
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const sessionId = sessionData.sessionId || `manual_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const extensionVersion = sessionData.extensionVersion || 'unknown';
    const vscodeVersion = sessionData.vscodeVersion || 'unknown';
    const platform = sessionData.platform || process.platform || 'unknown';

    // Create or update extension session to register the connection
    const session = await database.extensionSession.upsert({
      where: {
        userId_sessionId: {
          userId: dbUser.id,
          sessionId,
        },
      },
      update: {
        isActive: true,
        lastActiveAt: new Date(),
        extensionVersion,
        vscodeVersion,
        platform,
      },
      create: {
        userId: dbUser.id,
        sessionId,
        isActive: true,
        lastActiveAt: new Date(),
        extensionVersion,
        vscodeVersion,
        platform,
        tokensUsed: 0,
        requestsMade: 0,
      },
    });

    // Update user's last active timestamp and extension sync
    await database.user.update({
      where: { id: dbUser.id },
      data: {
        lastActiveAt: new Date(),
        lastExtensionSync: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Extension registered successfully',
      timestamp: new Date().toISOString(),
      session: {
        id: session.id,
        sessionId: session.sessionId,
        isActive: session.isActive,
        lastActiveAt: session.lastActiveAt,
      },
      user: {
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
        subscriptionTier: dbUser.subscriptionTier,
        subscriptionStatus: dbUser.subscriptionStatus,
        cubentUnitsUsed: dbUser.cubentUnitsUsed,
        cubentUnitsLimit: dbUser.cubentUnitsLimit,
      },
    });
  } catch (error) {
    console.error('Extension registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get registration status
 */
export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
      include: {
        extensionSessions: {
          where: { isActive: true },
          orderBy: { lastActiveAt: 'desc' },
        },
      },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      registered: dbUser.extensionSessions.length > 0,
      activeSessions: dbUser.extensionSessions.length,
      lastSync: dbUser.lastExtensionSync,
      sessions: dbUser.extensionSessions.map(session => ({
        id: session.id,
        sessionId: session.sessionId,
        lastActiveAt: session.lastActiveAt,
        extensionVersion: session.extensionVersion,
        vscodeVersion: session.vscodeVersion,
        platform: session.platform,
      })),
    });
  } catch (error) {
    console.error('Extension registration status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
