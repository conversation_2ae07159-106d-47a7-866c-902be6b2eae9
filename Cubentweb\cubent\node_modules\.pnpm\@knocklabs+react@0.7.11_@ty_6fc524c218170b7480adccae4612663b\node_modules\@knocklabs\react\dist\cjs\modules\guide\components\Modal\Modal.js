"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const C=require("@knocklabs/react-core"),D=require("@radix-ui/react-dialog"),S=require("clsx"),x=require("react"),M=require("../helpers.js");;/* empty css            */const V=e=>e&&typeof e=="object"&&"default"in e?e:{default:e};function R(e){if(e&&typeof e=="object"&&"default"in e)return e;const l=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const a in e)if(a!=="default"){const o=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(l,a,o.get?o:{enumerable:!0,get:()=>e[a]})}}return l.default=e,Object.freeze(l)}const c=R(D),n=V(S),t=V(x),j="modal",m=({children:e,onOpenChange:l,...a})=>t.default.createElement(c.Root,{defaultOpen:!0,onOpenChange:l,...a},t.default.createElement(c.Portal,null,e));m.displayName="ModalView.Root";const f=t.default.forwardRef(({className:e,...l},a)=>t.default.createElement(c.Overlay,{className:n.default("knock-guide-modal__overlay",e),ref:a,...l}));f.displayName="ModalView.Overlay";const y=t.default.forwardRef(({children:e,className:l,...a},o)=>t.default.createElement(c.Content,{className:n.default("knock-guide-modal",l),ref:o,...a},e));y.displayName="ModalView.Content";const v=({children:e,className:l,...a})=>t.default.createElement("div",{className:n.default("knock-guide-modal__header",l),...a},e);v.displayName="ModalView.Header";const _=({title:e,className:l,...a})=>t.default.createElement(c.Title,{className:n.default("knock-guide-modal__title",l),...a},e);_.displayName="ModalView.Title";const p=({body:e,className:l,...a})=>t.default.createElement(c.Description,{className:n.default("knock-guide-modal__body",l),dangerouslySetInnerHTML:{__html:e},...a});p.displayName="ModalView.Body";const g=({children:e,className:l,alt:a,...o})=>t.default.createElement("img",{className:n.default("knock-guide-modal__img",l),alt:a||"",...o},e);g.displayName="ModalView.Img";const k=({children:e,className:l,...a})=>t.default.createElement("div",{className:n.default("knock-guide-modal__actions",l),...a},e);k.displayName="ModalView.Actions";const E=({text:e,action:l,className:a,...o})=>t.default.createElement("button",{className:n.default("knock-guide-modal__action",a),...o},e);E.displayName="ModalView.PrimaryButton";const b=({text:e,action:l,className:a,...o})=>t.default.createElement("button",{className:n.default("knock-guide-modal__action knock-guide-modal__action--secondary",a),...o},e);b.displayName="ModalView.SecondaryButton";const N=({className:e,...l})=>t.default.createElement(c.Close,{className:n.default("knock-guide-modal__close",e),...l},t.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},t.default.createElement("g",{fill:"#60646C",fillRule:"evenodd",clipRule:"evenodd"},t.default.createElement("path",{d:"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z"}),t.default.createElement("path",{d:"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z"}))));N.displayName="ModalView.Close";const w=({content:e,colorMode:l="light",onOpenChange:a,onDismiss:o,onButtonClick:r,onImageClick:s})=>t.default.createElement(m,{onOpenChange:a},t.default.createElement(f,null),t.default.createElement(y,{"data-knock-color-mode":l,onPointerDownOutside:o},t.default.createElement(v,null,t.default.createElement(_,{title:e.title}),e.dismissible&&t.default.createElement(N,{onClick:o})),t.default.createElement(p,{body:e.body}),e.image&&t.default.createElement("a",{href:M.isValidHttpUrl(e.image.action)?e.image.action:void 0,target:"_blank"},t.default.createElement(g,{src:e.image.url,alt:e.image.alt,onClick:i=>{s&&s(i,e.image)}})),(e.primary_button||e.secondary_button)&&t.default.createElement(k,null,e.secondary_button&&t.default.createElement(b,{text:e.secondary_button.text,action:e.secondary_button.action,onClick:i=>{if(r){const{text:d,action:u}=e.secondary_button;r(i,{name:"secondary_button",text:d,action:u})}}}),e.primary_button&&t.default.createElement(E,{text:e.primary_button.text,action:e.primary_button.action,onClick:i=>{if(r){const{text:d,action:u}=e.primary_button;r(i,{name:"primary_button",text:d,action:u})}}}))));w.displayName="ModalView.Default";const h=({guideKey:e,onButtonClick:l,onImageClick:a})=>{const{guide:o,step:r,colorMode:s}=C.useGuide({key:e,type:j});return t.default.useEffect(()=>{r&&r.markAsSeen()},[r]),!o||!r?null:t.default.createElement(w,{content:r.content,colorMode:s,onDismiss:()=>r.markAsArchived(),onButtonClick:(i,d)=>{const u={...d,type:"button_click"};return r.markAsInteracted({metadata:u}),l?l(i,{button:d,step:r,guide:o}):M.maybeNavigateToUrlWithDelay(d.action)},onImageClick:(i,d)=>{const u={...d,type:"image_click"};if(r.markAsInteracted({metadata:u}),a)return a(i,{image:d,step:r,guide:o})}})};h.displayName="Modal";const O={};Object.assign(O,{Default:w,Root:m,Overlay:f,Content:y,Title:_,Body:p,Img:g,Actions:k,PrimaryButton:E,SecondaryButton:b,Close:N});exports.Modal=h;exports.ModalView=O;
//# sourceMappingURL=Modal.js.map
