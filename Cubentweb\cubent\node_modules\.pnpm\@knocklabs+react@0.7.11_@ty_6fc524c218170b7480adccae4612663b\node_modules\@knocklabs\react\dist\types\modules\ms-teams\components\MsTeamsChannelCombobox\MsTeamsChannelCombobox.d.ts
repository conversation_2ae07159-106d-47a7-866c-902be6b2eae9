import { MsTeamsChannelQueryOptions, MsTeamsTeamQueryOptions, RecipientObject } from '@knocklabs/react-core';
import { FunctionComponent } from 'react';
interface Props {
    msTeamsChannelsRecipientObject: RecipientObject;
    teamQueryOptions?: MsTeamsTeamQueryOptions;
    channelQueryOptions?: MsTeamsChannelQueryOptions;
}
declare const MsTeamsChannelCombobox: FunctionComponent<Props>;
export default MsTeamsChannelCombobox;
//# sourceMappingURL=MsTeamsChannelCombobox.d.ts.map