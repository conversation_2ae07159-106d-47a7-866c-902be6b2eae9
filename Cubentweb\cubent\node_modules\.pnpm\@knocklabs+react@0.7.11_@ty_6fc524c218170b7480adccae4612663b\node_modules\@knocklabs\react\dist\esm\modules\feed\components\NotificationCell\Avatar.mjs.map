{"version": 3, "file": "Avatar.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/Avatar.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport \"./styles.css\";\n\nexport interface AvatarProps {\n  name: string;\n  src?: string | null;\n}\n\nexport const Avatar: React.FC<AvatarProps> = ({ name, src }) => {\n  function getInitials(name: string) {\n    const [firstName, lastName] = name.split(\" \");\n    return firstName && lastName\n      ? `${firstName.charAt(0)}${lastName.charAt(0)}`\n      : firstName\n        ? firstName.charAt(0)\n        : \"\";\n  }\n\n  return (\n    <div className=\"rnf-avatar\">\n      {src ? (\n        <img src={src} alt={name} className=\"rnf-avatar__image\" />\n      ) : (\n        <span className=\"rnf-avatar__initials\">{getInitials(name)}</span>\n      )}\n    </div>\n  );\n};\n"], "names": ["Avatar", "name", "src", "getInitials", "firstName", "lastName", "split", "char<PERSON>t", "React"], "mappings": ";;AASO,MAAMA,IAAgCA,CAAC;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,KAAAA;AAAI,MAAM;AAC9D,WAASC,EAAYF,GAAc;AACjC,UAAM,CAACG,GAAWC,CAAQ,IAAIJ,EAAKK,MAAM,GAAG;AAC5C,WAAOF,KAAaC,IAChB,GAAGD,EAAUG,OAAO,CAAC,CAAC,GAAGF,EAASE,OAAO,CAAC,CAAC,KAC3CH,IACEA,EAAUG,OAAO,CAAC,IAClB;AAAA,EAAA;AAIN,SAAAC,gBAAAA,EAAA,cAAC,SAAI,WAAU,gBACZN,IACEM,gBAAAA,EAAA,cAAA,OAAA,EAAI,KAAAN,GAAU,KAAKD,GAAM,WAAU,yDAEnC,QAAK,EAAA,WAAU,0BAAwBE,EAAYF,CAAI,CAAE,CAE9D;AAEJ;"}