/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 18h10", key: "1y5s8o" }],
  ["path", { d: "m17 21 3-3-3-3", key: "1ammt0" }],
  ["path", { d: "M3 11h.01", key: "1eifu7" }],
  ["rect", { x: "15", y: "3", width: "5", height: "8", rx: "2.5", key: "76md6a" }],
  ["rect", { x: "6", y: "3", width: "5", height: "8", rx: "2.5", key: "v9paqo" }]
];
const DecimalsArrowRight = createLucideIcon("decimals-arrow-right", __iconNode);

export { __iconNode, DecimalsArrowRight as default };
//# sourceMappingURL=decimals-arrow-right.js.map
