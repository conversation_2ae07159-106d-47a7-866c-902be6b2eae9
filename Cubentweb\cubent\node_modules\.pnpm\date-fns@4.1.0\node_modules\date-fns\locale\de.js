import { formatDistance } from "./de/_lib/formatDistance.js";
import { formatLong } from "./de/_lib/formatLong.js";
import { formatRelative } from "./de/_lib/formatRelative.js";
import { localize } from "./de/_lib/localize.js";
import { match } from "./de/_lib/match.js";

/**
 * @category Locales
 * @summary German locale.
 * @language German
 * @iso-639-2 deu
 * <AUTHOR> [@DeMuu](https://github.com/DeMuu)
 * <AUTHOR> [@asia-t](https://github.com/asia-t)
 * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)
 * <AUTHOR> [@pex](https://github.com/pex)
 * <AUTHOR> [@Philipp91](https://github.com/Philipp91)
 */
export const de = {
  code: "de",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default de;
