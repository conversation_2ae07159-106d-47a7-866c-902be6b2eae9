import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@repo/auth/server';
import { z } from 'zod';

const telemetryEventSchema = z.object({
  eventName: z.string(),
  properties: z.record(z.any()).optional(),
  timestamp: z.string().optional(),
  sessionId: z.string().optional(),
});

const telemetryBatchSchema = z.object({
  events: z.array(telemetryEventSchema),
  sessionId: z.string().optional(),
  userId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication (optional for telemetry)
    let userId: string | null = null;
    
    try {
      const authResult = await auth();
      userId = authResult.userId;
    } catch {
      // Telemetry can work without authentication
    }

    const body = await request.json();
    
    // Validate the telemetry data
    let events: any[] = [];
    
    if (Array.isArray(body)) {
      // Handle array of events
      events = body;
    } else if (body.events) {
      // Handle batch format
      const batch = telemetryBatchSchema.parse(body);
      events = batch.events;
    } else {
      // Handle single event
      events = [telemetryEventSchema.parse(body)];
    }

    // Log telemetry events (in production, you might want to store these in a database)
    console.log('Telemetry events received:', {
      userId,
      eventCount: events.length,
      events: events.map(e => ({
        eventName: e.eventName,
        timestamp: e.timestamp || new Date().toISOString(),
        sessionId: e.sessionId,
      })),
    });

    // For now, just acknowledge receipt
    // In the future, you could store these in a telemetry database
    return NextResponse.json({
      success: true,
      eventsProcessed: events.length,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Telemetry error:', error);
    
    // Don't fail telemetry requests - just log and return success
    return NextResponse.json({
      success: true,
      eventsProcessed: 0,
      error: 'Failed to process telemetry data',
    });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return telemetry configuration
    return NextResponse.json({
      enabled: true,
      endpoints: {
        events: '/api/extension/telemetry',
        batch: '/api/extension/telemetry',
      },
      settings: {
        batchSize: 10,
        flushInterval: 30000, // 30 seconds
        enabledEvents: ['*'], // All events enabled
      },
    });

  } catch (error) {
    console.error('Telemetry config error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
