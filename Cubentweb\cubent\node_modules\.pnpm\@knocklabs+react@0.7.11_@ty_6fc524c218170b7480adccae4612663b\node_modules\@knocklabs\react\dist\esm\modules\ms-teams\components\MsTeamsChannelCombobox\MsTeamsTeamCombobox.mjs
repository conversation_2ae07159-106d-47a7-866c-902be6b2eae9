import e, { use<PERSON>em<PERSON> as c } from "react";
import { useKnockMsTeamsClient as E, useMsTeamsTeams as T } from "@knocklabs/react-core";
import { Combobox as o } from "@telegraph/combobox";
import { Box as b } from "@telegraph/layout";
import { sortByDisplayName as h } from "../../utils.mjs";
const N = ({
  team: s,
  onTeamChange: m,
  getChannelCount: d,
  queryOptions: u
}) => {
  const {
    connectionStatus: n
  } = E(), {
    data: i,
    isLoading: l
  } = T({
    queryOptions: u
  }), r = c(() => h(i), [i]), p = c(() => n === "disconnected" || n === "error", [n]), f = c(() => n === "connecting" || n === "disconnecting" || l, [n, l]);
  return /* @__PURE__ */ e.createElement(b, { w: "full", minW: "0" }, /* @__PURE__ */ e.createElement(o.Root, { value: s == null ? void 0 : s.id, onValueChange: (t) => {
    const a = r.find((g) => g.id === t);
    a && m(a);
  }, placeholder: "Select team", disabled: p || f || r.length === 0, modal: (
    // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.
    !1
  ) }, /* @__PURE__ */ e.createElement(o.Trigger, null), /* @__PURE__ */ e.createElement(o.Content, null, /* @__PURE__ */ e.createElement(o.Search, { className: "rtk-combobox__search" }), /* @__PURE__ */ e.createElement(o.Options, { maxHeight: "36" }, r.map((t) => {
    const a = d(t.id);
    return /* @__PURE__ */ e.createElement(o.Option, { key: t.id, value: t.id }, a > 0 ? `${t.displayName} (${a})` : t.displayName);
  })), /* @__PURE__ */ e.createElement(o.Empty, null))));
};
export {
  N as MsTeamsTeamCombobox
};
//# sourceMappingURL=MsTeamsTeamCombobox.mjs.map
