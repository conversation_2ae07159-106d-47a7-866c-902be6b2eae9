"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const u=require("react"),h=require("@knocklabs/react-core"),d=require("@telegraph/combobox"),b=require("@telegraph/icon"),x=require("@telegraph/layout"),p=require("@telegraph/typography");;/* empty css               */const A=require("../../utils.js"),T=require("../SlackAddChannelInput/SlackAddChannelInput.js"),v=require("./SlackConnectionError.js"),F=require("./SlackErrorMessage.js");;/* empty css            */const O=c=>c&&typeof c=="object"&&"default"in c?c:{default:c},n=O(u),D=1e3,H=({slackChannelsRecipientObject:c,queryOptions:q,inputMessages:o})=>{const{t:i}=h.useTranslations(),{connectionStatus:a,errorLabel:S}=h.useKnockSlackClient(),{data:k,isLoading:f}=h.useSlackChannels({queryOptions:q}),l=u.useMemo(()=>A.sortSlackChannelsAlphabetically(k),[k]),{data:m,updateConnectedChannels:E,error:s,updating:L}=h.useConnectedSlackChannels({slackChannelsRecipientObject:c}),t=u.useMemo(()=>{const e=new Map(l.map(r=>[r.id,r]));return(m==null?void 0:m.filter(r=>e.has(r.channel_id||"")))||[]},[m,l]),C=u.useMemo(()=>a==="disconnected"||a==="error"||!!s,[s,a]),_=u.useMemo(()=>a==="connecting"||a==="disconnecting"||f,[a,f]),y=u.useMemo(()=>{const e={disconnected:i("slackSearchbarDisconnected"),noChannelsConnected:i("slackSearchbarNoChannelsConnected"),noSlackChannelsFound:i("slackSearchbarNoChannelsFound"),channelsError:i("slackSearchbarChannelsError")};if(a==="disconnected")return(o==null?void 0:o.disconnected)||e.disconnected;if(a==="error")return(o==null?void 0:o.error)||S;if(!_&&l.length===0)return(o==null?void 0:o.noSlackChannelsFound)||e.noSlackChannelsFound;const r=(t==null?void 0:t.length)||0;return t&&r===0?(o==null?void 0:o.noChannelsConnected)||e.noChannelsConnected:""},[a,_,l,t,o,S,i]),N=u.useMemo(()=>t.map(e=>e.channel_id),[t]);return l.length>D?n.default.createElement(T,{inErrorState:C,connectedChannels:t||[],updateConnectedChannels:E,connectedChannelsError:s,connectedChannelsUpdating:L}):n.default.createElement(x.Stack,{className:"tgph rsk-combobox__grid",gap:"3"},n.default.createElement(p.Text,{color:"gray",size:"2",as:"div",minHeight:"8",className:"rsk-combobox__label"},"Channels"),n.default.createElement(d.Combobox.Root,{value:N,onValueChange:e=>{const r=e.map(g=>({channel_id:g}));E(r).catch(console.error)},placeholder:y??"",disabled:C||l.length===0,errored:C,closeOnSelect:!1,layout:"wrap",modal:!1},n.default.createElement(d.Combobox.Trigger,null),n.default.createElement(d.Combobox.Content,null,n.default.createElement(d.Combobox.Search,{label:i("slackSearchChannels"),className:"rsk-combobox__search"}),n.default.createElement(d.Combobox.Options,{maxHeight:"36"},l.map(e=>n.default.createElement(d.Combobox.Option,{key:e.id,value:e.id},n.default.createElement(x.Stack,{align:"center",gap:"1"},n.default.createElement(b.Icon,{icon:e.is_private?b.Lucide.Lock:b.Lucide.Hash,size:"0","aria-hidden":!0}),e.name)))),n.default.createElement(d.Combobox.Empty,null))),n.default.createElement(v,null),!!s&&n.default.createElement(F,{message:s}))};exports.SlackChannelCombobox=H;
//# sourceMappingURL=SlackChannelCombobox.js.map
