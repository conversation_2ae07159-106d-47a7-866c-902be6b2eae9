import t from "react";
/* empty css            */
const m = ({
  name: e,
  src: r
}) => {
  function i(s) {
    const [a, n] = s.split(" ");
    return a && n ? `${a.charAt(0)}${n.charAt(0)}` : a ? a.charAt(0) : "";
  }
  return /* @__PURE__ */ t.createElement("div", { className: "rnf-avatar" }, r ? /* @__PURE__ */ t.createElement("img", { src: r, alt: e, className: "rnf-avatar__image" }) : /* @__PURE__ */ t.createElement("span", { className: "rnf-avatar__initials" }, i(e)));
};
export {
  m as Avatar
};
//# sourceMappingURL=Avatar.mjs.map
