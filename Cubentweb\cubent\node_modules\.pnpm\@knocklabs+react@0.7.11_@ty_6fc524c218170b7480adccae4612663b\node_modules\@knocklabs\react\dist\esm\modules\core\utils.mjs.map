{"version": 3, "file": "utils.mjs", "sources": ["../../../../src/modules/core/utils.ts"], "sourcesContent": ["export const openPopupWindow = (url: string) => {\n  const width = 600;\n  const height = 800;\n  // Calculate the position to center the window\n  const screenLeft = window.screenLeft ?? window.screenX;\n  const screenTop = window.screenTop ?? window.screenY;\n\n  const innerWidth =\n    window.innerWidth ?? document.documentElement.clientWidth ?? screen.width;\n  const innerHeight =\n    window.innerHeight ??\n    document.documentElement.clientHeight ??\n    screen.height;\n\n  const left = innerWidth / 2 - width / 2 + screenLeft;\n  const top = innerHeight / 2 - height / 2 + screenTop;\n\n  // Window features\n  const features = `width=${width},height=${height},top=${top},left=${left}`;\n\n  window.open(url, \"_blank\", features);\n};\n"], "names": ["openPopupWindow", "url", "screenLeft", "window", "screenX", "screenTop", "screenY", "innerWidth", "document", "documentElement", "clientWidth", "screen", "width", "innerHeight", "clientHeight", "height", "left", "features", "open"], "mappings": "AAAaA,MAAAA,IAAkBA,CAACC,MAAgB;AAIxCC,QAAAA,IAAaC,OAAOD,cAAcC,OAAOC,SACzCC,IAAYF,OAAOE,aAAaF,OAAOG,SAEvCC,IACJJ,OAAOI,cAAcC,SAASC,gBAAgBC,eAAeC,OAAOC,OAChEC,IACJV,OAAOU,eACPL,SAASC,gBAAgBK,gBACzBH,OAAOI,QAEHC,IAAOT,IAAa,IAAIK,MAAQ,IAAIV,GAIpCe,IAAW,4BAHLJ,IAAc,IAAIE,MAAS,IAAIV,CAGgB,SAASW,CAAI;AAEjEE,SAAAA,KAAKjB,GAAK,UAAUgB,CAAQ;AACrC;"}