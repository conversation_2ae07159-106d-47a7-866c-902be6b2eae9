{"version": 3, "file": "MsTeamsChannelInTeamCombobox.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelInTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsChannelConnection } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n  useKnockMsTeamsClient,\n  useMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useCallback, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\ninterface MsTeamsChannelInTeamComboboxProps {\n  teamId?: string;\n  msTeamsChannelsRecipientObject: RecipientObject;\n  queryOptions?: MsTeamsChannelQueryOptions;\n}\n\nexport const MsTeamsChannelInTeamCombobox: FunctionComponent<\n  MsTeamsChannelInTeamComboboxProps\n> = ({ teamId, msTeamsChannelsRecipientObject, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: availableChannels = [] } = useMsTeamsChannels({\n    teamId,\n    queryOptions,\n  });\n\n  const sortedChannels = useMemo(\n    () => sortByDisplayName(availableChannels),\n    [availableChannels],\n  );\n\n  const {\n    data: currentConnections,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n  } = useConnectedMsTeamsChannels({ msTeamsChannelsRecipientObject });\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectionStatus, connectedChannelsError],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" || connectionStatus === \"disconnecting\",\n    [connectionStatus],\n  );\n\n  const isChannelInThisTeam = useCallback(\n    (channelId: string) =>\n      availableChannels.some((channel) => channel.id === channelId),\n    [availableChannels],\n  );\n\n  const comboboxValue = useMemo(\n    () =>\n      currentConnections\n        ?.filter(\n          (connection) =>\n            connection.ms_teams_channel_id &&\n            isChannelInThisTeam(connection.ms_teams_channel_id),\n        )\n        .map((connection) => connection.ms_teams_channel_id),\n    [currentConnections, isChannelInThisTeam],\n  );\n\n  return (\n    <>\n      <Box w=\"full\" minW=\"0\">\n        <Combobox.Root\n          value={comboboxValue}\n          onValueChange={(channelIds) => {\n            const connectedChannelsInThisTeam =\n              channelIds.map<MsTeamsChannelConnection>((channelId) => ({\n                ms_teams_team_id: teamId,\n                ms_teams_channel_id: channelId,\n              }));\n            const connectedChannelsInOtherTeams =\n              currentConnections?.filter(\n                (connection) =>\n                  !connection.ms_teams_channel_id ||\n                  !isChannelInThisTeam(connection.ms_teams_channel_id),\n              ) ?? [];\n\n            const updatedConnections = [\n              ...connectedChannelsInOtherTeams,\n              ...connectedChannelsInThisTeam,\n            ];\n\n            updateConnectedChannels(updatedConnections).catch(console.error);\n          }}\n          placeholder=\"Select channels\"\n          disabled={\n            teamId === undefined ||\n            inErrorState ||\n            inLoadingState ||\n            availableChannels.length === 0\n          }\n          closeOnSelect={false}\n          layout=\"wrap\"\n          modal={\n            // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n            false\n          }\n        >\n          <Combobox.Trigger />\n          <Combobox.Content>\n            <Combobox.Search className=\"rtk-combobox__search\" />\n            <Combobox.Options maxHeight=\"36\">\n              {sortedChannels.map((channel) => (\n                <Combobox.Option key={channel.id} value={channel.id}>\n                  {channel.displayName}\n                </Combobox.Option>\n              ))}\n            </Combobox.Options>\n            <Combobox.Empty />\n          </Combobox.Content>\n        </Combobox.Root>\n      </Box>\n      {!!connectedChannelsError && (\n        <MsTeamsErrorMessage message={connectedChannelsError} />\n      )}\n    </>\n  );\n};\n"], "names": ["MsTeamsChannelInTeamCombobox", "teamId", "msTeamsChannelsRecipientObject", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "availableChannels", "useMsTeamsChannels", "sortedChannels", "useMemo", "sortByDisplayName", "currentConnections", "updateConnectedChannels", "error", "connectedChannelsError", "useConnectedMsTeamsChannels", "inErrorState", "inLoadingState", "isChannelInThisTeam", "useCallback", "channelId", "some", "channel", "id", "comboboxValue", "filter", "connection", "ms_teams_channel_id", "map", "React", "Box", "Combobox", "channelIds", "connectedChannelsInThisTeam", "ms_teams_team_id", "updatedConnections", "catch", "console", "undefined", "length", "displayName", "MsTeamsErrorMessage"], "mappings": "6UAsBaA,EAETA,CAAC,CAAEC,OAAAA,EAAQC,+BAAAA,EAAgCC,aAAAA,CAAa,IAAM,CAC1D,KAAA,CAAEC,iBAAAA,GAAqBC,wBAAsB,EAE7C,CAAEC,KAAMC,EAAoB,CAAA,GAAOC,qBAAmB,CAC1DP,OAAAA,EACAE,aAAAA,CAAAA,CACD,EAEKM,EAAiBC,EAAAA,QACrB,IAAMC,EAAAA,kBAAkBJ,CAAiB,EACzC,CAACA,CAAiB,CACpB,EAEM,CACJD,KAAMM,EACNC,wBAAAA,EACAC,MAAOC,GACLC,8BAA4B,CAAEd,+BAAAA,CAAAA,CAAgC,EAE5De,EAAeP,EAAAA,QACnB,IACEN,IAAqB,gBACrBA,IAAqB,SACrB,CAAC,CAACW,EACJ,CAACX,EAAkBW,CAAsB,CAC3C,EAEMG,EAAiBR,UACrB,IACEN,IAAqB,cAAgBA,IAAqB,gBAC5D,CAACA,CAAgB,CACnB,EAEMe,EAAsBC,EAAAA,YACzBC,GACCd,EAAkBe,KAAkBC,GAAAA,EAAQC,KAAOH,CAAS,EAC9D,CAACd,CAAiB,CACpB,EAEMkB,EAAgBf,UACpB,IACEE,GAAAA,YAAAA,EACIc,OAEEC,GAAAA,EAAWC,qBACXT,EAAoBQ,EAAWC,mBAAmB,GAErDC,IAAoBF,GAAAA,EAAWC,qBACpC,CAAChB,EAAoBO,CAAmB,CAC1C,EAEA,OAEIW,UAAA,cAAAA,EAAA,QAAA,SAAA,KAAAA,EAAA,QAAA,cAACC,EAAAA,IAAI,CAAA,EAAE,OAAO,KAAK,KAChBD,EAAAA,QAAA,cAAAE,WAAS,KAAT,CACC,MAAOP,EACP,cAA+BQ,GAAA,CACvBC,MAAAA,EACJD,EAAWJ,IAA8CR,IAAA,CACvDc,iBAAkBlC,EAClB2B,oBAAqBP,CAAAA,EACrB,EAQEe,EAAqB,CACzB,IAPAxB,GAAAA,YAAAA,EAAoBc,OACjBC,GACC,CAACA,EAAWC,qBACZ,CAACT,EAAoBQ,EAAWC,mBAAmB,KAClD,CAAE,EAIP,GAAGM,CAA2B,EAGhCrB,EAAwBuB,CAAkB,EAAEC,MAAMC,QAAQxB,KAAK,CAAA,EAEjE,YAAY,kBACZ,SACEb,IAAWsC,QACXtB,GACAC,GACAX,EAAkBiC,SAAW,EAE/B,cAAe,GACf,OAAO,OACP,MAEE,EAGF,EAAAV,EAAAA,QAAA,cAACE,EAAAA,SAAS,QAAT,IAAgB,0BAChBA,EAAAA,SAAS,QAAT,KACCF,EAAAA,QAAA,cAACE,EAAAA,SAAS,OAAT,CAAgB,UAAU,sBAAsB,CAAA,EACjDF,EAAAA,QAAA,cAACE,EAAAA,SAAS,QAAT,CAAiB,UAAU,IACzBvB,EAAAA,EAAeoB,IAAKN,GAClBO,UAAA,cAAAE,EAAAA,SAAS,OAAT,CAAgB,IAAKT,EAAQC,GAAI,MAAOD,EAAQC,EAC9CD,EAAAA,EAAQkB,WACX,CACD,CACH,EACCX,EAAAA,QAAA,cAAAE,EAAA,SAAS,MAAT,IAAc,CACjB,CACF,CACF,EACC,CAAC,CAACjB,GACAe,EAAA,QAAA,cAAAY,EAAA,CAAoB,QAAS3B,CAC/B,CAAA,CACH,CAEJ"}