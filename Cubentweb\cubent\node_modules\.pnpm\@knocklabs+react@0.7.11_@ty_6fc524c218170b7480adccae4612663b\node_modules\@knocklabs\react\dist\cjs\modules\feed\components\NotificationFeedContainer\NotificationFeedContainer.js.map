{"version": 3, "file": "NotificationFeedContainer.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeedContainer/NotificationFeedContainer.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const NotificationFeedContainer: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-feed-provider\">{children}</div>;\n"], "names": ["NotificationFeedContainer", "children", "React"], "mappings": "yMAIaA,EAERA,CAAC,CAAEC,SAAAA,CAAS,IAAOC,EAAAA,QAAA,cAAA,MAAA,CAAI,UAAU,mBAAA,EAAqBD,CAAS"}