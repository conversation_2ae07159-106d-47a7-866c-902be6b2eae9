{"version": 3, "file": "MsTeamsTeamCombobox.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsTeamCombobox.tsx"], "sourcesContent": ["import { MsTeamsTeam } from \"@knocklabs/client\";\nimport {\n  MsTeamsTeamQueryOptions,\n  useKnockMsTeamsClient,\n  useMsTeamsTeams,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Box } from \"@telegraph/layout\";\nimport { FunctionComponent, useMemo } from \"react\";\n\nimport { sortByDisplayName } from \"../../utils\";\n\ninterface MsTeamsTeamComboboxProps {\n  team: MsTeamsTeam | null;\n  onTeamChange: (team: MsTeamsTeam) => void;\n  getChannelCount: (teamId: string) => number;\n  queryOptions?: MsTeamsTeamQueryOptions;\n}\n\nexport const MsTeamsTeamCombobox: FunctionComponent<\n  MsTeamsTeamComboboxProps\n> = ({ team, onTeamChange, getChannelCount, queryOptions }) => {\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  const { data: teams, isLoading: isLoadingTeams } = useMsTeamsTeams({\n    queryOptions,\n  });\n\n  const sortedTeams = useMemo(() => sortByDisplayName(teams), [teams]);\n\n  const inErrorState = useMemo(\n    () => connectionStatus === \"disconnected\" || connectionStatus === \"error\",\n    [connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      isLoadingTeams,\n    [connectionStatus, isLoadingTeams],\n  );\n\n  return (\n    <Box w=\"full\" minW=\"0\">\n      <Combobox.Root\n        value={team?.id}\n        onValueChange={(teamId) => {\n          const selectedTeam = sortedTeams.find((team) => team.id === teamId);\n          if (selectedTeam) {\n            onTeamChange(selectedTeam);\n          }\n        }}\n        placeholder=\"Select team\"\n        disabled={inErrorState || inLoadingState || sortedTeams.length === 0}\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search className=\"rtk-combobox__search\" />\n          <Combobox.Options maxHeight=\"36\">\n            {sortedTeams.map((team) => {\n              const channelCount = getChannelCount(team.id);\n              return (\n                <Combobox.Option key={team.id} value={team.id}>\n                  {channelCount > 0\n                    ? `${team.displayName} (${channelCount})`\n                    : team.displayName}\n                </Combobox.Option>\n              );\n            })}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n    </Box>\n  );\n};\n"], "names": ["MsTeamsTeamCombobox", "team", "onTeamChange", "getChannelCount", "queryOptions", "connectionStatus", "useKnockMsTeamsClient", "data", "teams", "isLoading", "isLoadingTeams", "useMsTeamsTeams", "sortedTeams", "useMemo", "sortByDisplayName", "inErrorState", "inLoadingState", "React", "Box", "Combobox", "id", "teamId", "selectedTeam", "find", "length", "map", "channelCount", "displayName"], "mappings": ";;;;;AAmBO,MAAMA,IAETA,CAAC;AAAA,EAAEC,MAAAA;AAAAA,EAAMC,cAAAA;AAAAA,EAAcC,iBAAAA;AAAAA,EAAiBC,cAAAA;AAAa,MAAM;AACvD,QAAA;AAAA,IAAEC,kBAAAA;AAAAA,MAAqBC,EAAsB,GAE7C;AAAA,IAAEC,MAAMC;AAAAA,IAAOC,WAAWC;AAAAA,MAAmBC,EAAgB;AAAA,IACjEP,cAAAA;AAAAA,EAAAA,CACD,GAEKQ,IAAcC,EAAQ,MAAMC,EAAkBN,CAAK,GAAG,CAACA,CAAK,CAAC,GAE7DO,IAAeF,EACnB,MAAMR,MAAqB,kBAAkBA,MAAqB,SAClE,CAACA,CAAgB,CACnB,GAEMW,IAAiBH,EACrB,MACER,MAAqB,gBACrBA,MAAqB,mBACrBK,GACF,CAACL,GAAkBK,CAAc,CACnC;AAEA,SACGO,gBAAAA,EAAA,cAAAC,GAAA,EAAI,GAAE,QAAO,MAAK,OACjBD,gBAAAA,EAAA,cAACE,EAAS,MAAT,EACC,OAAOlB,KAAAA,gBAAAA,EAAMmB,IACb,eAAgBC,CAAWA,MAAA;AACzB,UAAMC,IAAeV,EAAYW,KAAMtB,CAAAA,MAASA,EAAKmB,OAAOC,CAAM;AAClE,IAAIC,KACFpB,EAAaoB,CAAY;AAAA,EAC3B,GAEF,aAAY,eACZ,UAAUP,KAAgBC,KAAkBJ,EAAYY,WAAW,GACnE;AAAA;AAAA,IAEE;AAAA,IAGF,GAAAP,gBAAAA,EAAA,cAACE,EAAS,SAAT,IAAgB,mCAChBA,EAAS,SAAT,MACCF,gBAAAA,EAAA,cAACE,EAAS,QAAT,EAAgB,WAAU,uBAAA,CAAsB,GACjDF,gBAAAA,EAAA,cAACE,EAAS,SAAT,EAAiB,WAAU,KACzBP,GAAAA,EAAYa,IAAKxB,CAAAA,MAAS;AACnByB,UAAAA,IAAevB,EAAgBF,EAAKmB,EAAE;AAC5C,2CACGD,EAAS,QAAT,EAAgB,KAAKlB,EAAKmB,IAAI,OAAOnB,EAAKmB,MACxCM,IAAe,IACZ,GAAGzB,EAAK0B,WAAW,KAAKD,CAAY,MACpCzB,EAAK0B,WACX;AAAA,EAAA,CAEH,CACH,GACAV,gBAAAA,EAAA,cAACE,EAAS,OAAT,IAAc,CACjB,CACF,CACF;AAEJ;"}