"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const a=require("@knocklabs/react-core"),c=require("react"),l=require("../../../core/components/Icons/ChevronDown.js");;/* empty css            */const d=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=d(c),i=({children:e,value:o,onChange:r})=>{const{colorMode:n}=a.useKnockFeed();return t.default.createElement("div",{className:`rnf-dropdown rnf-dropdown--${n}`},t.default.createElement("select",{"aria-label":"Select notification filter",value:o,onChange:r},e),t.default.createElement(l.ChevronDown,{"aria-hidden":!0}))};exports.Dropdown=i;
//# sourceMappingURL=Dropdown.js.map
