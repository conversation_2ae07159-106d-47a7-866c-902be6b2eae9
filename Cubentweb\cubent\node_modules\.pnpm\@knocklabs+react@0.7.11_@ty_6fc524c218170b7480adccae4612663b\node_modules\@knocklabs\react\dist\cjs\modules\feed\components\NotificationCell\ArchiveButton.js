"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const t=require("react"),u=require("@knocklabs/react-core"),d=require("@popperjs/core"),v=require("../../../core/components/Icons/CloseCircle.js"),b=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},a=b(t),m=({item:e})=>{const{colorMode:i,feedClient:f}=u.useKnockFeed(),{t:s}=u.useTranslations(),[r,l]=t.useState(!1),n=t.useRef(null),c=t.useRef(null),p=t.useCallback(o=>{o.preventDefault(),o.stopPropagation(),f.markAsArchived(e)},[e]);return t.useEffect(()=>{if(n.current&&c.current&&r){const o=d.createPopper(n.current,c.current,{placement:"top-end",modifiers:[{name:"offset",options:{offset:[0,8]}}]});return()=>{o.destroy()}}},[r]),a.default.createElement("button",{ref:n,onClick:p,onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),type:"button","aria-label":s("archiveNotification"),className:`rnf-archive-notification-btn rnf-archive-notification-btn--${i}`},a.default.createElement(v.CloseCircle,{"aria-hidden":!0}),r&&a.default.createElement("div",{ref:c,className:`rnf-tooltip rnf-tooltip--${i}`},s("archiveNotification")))};exports.ArchiveButton=m;
//# sourceMappingURL=ArchiveButton.js.map
