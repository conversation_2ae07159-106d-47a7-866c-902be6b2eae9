"use strict";const r=require("react"),C=require("@knocklabs/react-core"),m=require("../../../core/components/Spinner/Spinner.js");;/* empty css                                          */require("lodash.debounce");;/* empty css               */const _=require("../SlackChannelCombobox/SlackConnectionError.js"),q=require("../SlackIcon/SlackIcon.js");;/* empty css            */const E=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},n=E(r),S=({inErrorState:e,connectedChannels:o=[],updateConnectedChannels:i,connectedChannelsError:d,connectedChannelsUpdating:h})=>{const{t:a}=C.useTranslations(),[t,c]=r.useState(null),[l,s]=r.useState(null),f=()=>{if(!t)return;if(l&&s(null),o.find(p=>p.channel_id===t))return c(""),s(a("slackChannelAlreadyConnected")||"");const k=[...o,{channel_id:t}];i(k),c("")};return n.default.createElement("div",{className:"rsk-connect-channel"},n.default.createElement("input",{className:`rsk-connect-channel__input ${(e||!!l)&&!t&&"rsk-connect-channel__input--error"}`,tabIndex:-1,id:"slack-channel-search",type:"text",placeholder:l||d||a("slackChannelId"),onChange:u=>c(u.target.value),value:t||""}),n.default.createElement("button",{className:"rsk-connect-channel__button",onClick:f},h?n.default.createElement(m.Spinner,{size:"15px",thickness:3}):n.default.createElement(q.SlackIcon,{height:"16px",width:"16px"}),a("slackConnectChannel")),n.default.createElement(_,null))};module.exports=S;
//# sourceMappingURL=SlackAddChannelInput.js.map
