import n from "react";
import { useTranslations as o, useKnockMsTeamsClient as s } from "@knocklabs/react-core";
import t from "./MsTeamsErrorMessage.mjs";
const i = () => {
  const {
    t: r
  } = o(), {
    connectionStatus: e
  } = s();
  return e === "disconnected" || e === "error" ? /* @__PURE__ */ n.createElement(t, { message: r(e === "disconnected" ? "msTeamsConnectionErrorOccurred" : "msTeamsConnectionErrorExists") }) : null;
};
export {
  i as default
};
//# sourceMappingURL=MsTeamsConnectionError.mjs.map
