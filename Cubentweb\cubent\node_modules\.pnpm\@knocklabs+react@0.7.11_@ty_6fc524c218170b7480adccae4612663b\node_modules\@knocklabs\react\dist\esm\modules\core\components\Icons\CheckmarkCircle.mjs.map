{"version": 3, "file": "CheckmarkCircle.mjs", "sources": ["../../../../../../src/modules/core/components/Icons/CheckmarkCircle.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype CheckmarkCircleProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst CheckmarkCircle: FunctionComponent<CheckmarkCircleProps> = ({\n  width = 16,\n  height = 16,\n  \"aria-hidden\": aria<PERSON>id<PERSON>,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M14 8.00012C14 4.68762 11.3125 2.00012 7.99997 2.00012C4.68747 2.00012 1.99997 4.68762 1.99997 8.00012C1.99997 11.3126 4.68747 14.0001 7.99997 14.0001C11.3125 14.0001 14 11.3126 14 8.00012Z\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeMiterlimit=\"10\"\n    />\n    <path\n      d=\"M10.9999 5.5004L6.79994 10.5004L4.99994 8.5004\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { CheckmarkCircle };\n"], "names": ["CheckmarkCircle", "width", "height", "ariaHidden", "React"], "mappings": ";AAQA,MAAMA,IAA2DA,CAAC;AAAA,EAChEC,OAAAA,IAAQ;AAAA,EACRC,QAAAA,IAAS;AAAA,EACT,eAAeC;AACjB,MACGC,gBAAAA,EAAA,cAAA,OAAA,EACC,OAAAH,GACA,QAAAC,GACA,SAAQ,aACR,MAAK,QACL,OAAM,8BACN,eAAaC,EAAAA,GAEZC,gBAAAA,EAAA,cAAA,QAAA,EACC,GAAE,iMACF,QAAO,gBACP,aAAY,OACZ,kBAAiB,KAAA,CAAI,GAEvBA,gBAAAA,EAAA,cAAC,UACC,GAAE,kDACF,QAAO,gBACP,aAAY,OACZ,eAAc,SACd,gBAAe,SAAO,CAE1B;"}