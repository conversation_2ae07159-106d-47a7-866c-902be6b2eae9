/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "m8 14 4-4 4 4", key: "fy2ptz" }]
];
const CircleChevronUp = createLucideIcon("circle-chevron-up", __iconNode);

export { __iconNode, CircleChevronUp as default };
//# sourceMappingURL=circle-chevron-up.js.map
