{"version": 3, "file": "SlackConnectionError.js", "sources": ["../../../../../../src/modules/slack/components/SlackChannelCombobox/SlackConnectionError.tsx"], "sourcesContent": ["import { useKnockSlackClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport SlackErrorMessage from \"./SlackErrorMessage\";\n\nconst SlackConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockSlackClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <SlackErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"slackConnectionErrorOccurred\")\n            : t(\"slackConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default SlackConnectionError;\n"], "names": ["SlackConnectionError", "t", "useTranslations", "connectionStatus", "useKnockSlackClient", "React", "SlackErrorMessage"], "mappings": "4KAKMA,EAA0CA,IAAM,CAC9C,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxB,CAAEC,iBAAAA,GAAqBC,sBAAoB,EAE7CD,OAAAA,IAAqB,gBAAkBA,IAAqB,QAE5DE,EAAA,QAAA,cAACC,EACC,CAAA,QAEML,EADJE,IAAqB,eACf,+BACA,4BAD8B,CAGtC,CAAA,EAIC,IACT"}