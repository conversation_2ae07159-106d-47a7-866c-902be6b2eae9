import { monthsInQuarter } from "./constants.js";

/**
 * @name quartersToMonths
 * @category Conversion Helpers
 * @summary Convert number of quarters to months.
 *
 * @description
 * Convert a number of quarters to a full number of months.
 *
 * @param quarters - The number of quarters to be converted
 *
 * @returns The number of quarters converted in months
 *
 * @example
 * // Convert 2 quarters to months
 * const result = quartersToMonths(2)
 * //=> 6
 */
export function quartersToMonths(quarters) {
  return Math.trunc(quarters * monthsInQuarter);
}

// Fallback for modularized imports:
export default quartersToMonths;
