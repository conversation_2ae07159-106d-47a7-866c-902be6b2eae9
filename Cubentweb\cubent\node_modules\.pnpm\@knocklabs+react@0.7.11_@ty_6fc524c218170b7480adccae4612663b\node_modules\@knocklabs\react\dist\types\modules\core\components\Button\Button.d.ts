import { default as React, PropsWithChildren, FunctionComponent } from 'react';
export type ButtonProps = {
    variant: "primary" | "secondary";
    loadingText?: string;
    isLoading?: boolean;
    isDisabled?: boolean;
    isFullWidth?: boolean;
    onClick: (e: React.MouseEvent) => void;
};
export declare const Button: FunctionComponent<PropsWithChildren<ButtonProps>>;
//# sourceMappingURL=Button.d.ts.map