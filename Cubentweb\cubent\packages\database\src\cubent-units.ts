/**
 * Cubent Units Calculation Service
 * 
 * This service calculates Cubent units based on the model used and message content.
 * Cubent units provide a unified way to measure AI usage across different models.
 */

export interface ModelPricing {
  cubentUnits: number;
  available: boolean;
  supportsImages: boolean;
}

/**
 * Model pricing based on models-and-pricing.mdx
 * Each model has a specific Cubent unit cost per message
 */
export const MODEL_PRICING: Record<string, ModelPricing> = {
  // Anthropic Claude Models
  'claude-3-7-sonnet': { cubentUnits: 1.1, available: true, supportsImages: true },
  'claude-3-7-sonnet-thinking': { cubentUnits: 1.35, available: true, supportsImages: true },
  'claude-3-5-sonnet': { cubentUnits: 0.95, available: true, supportsImages: true },
  'claude-3-5-haiku': { cubentUnits: 0.55, available: true, supportsImages: false },
  'claude-3-haiku': { cubentUnits: 0.45, available: true, supportsImages: false },

  // OpenAI Models
  'gpt-4o': { cubentUnits: 1.1, available: true, supportsImages: true },
  'gpt-4-5-preview': { cubentUnits: 1.2, available: true, supportsImages: true },
  'gpt-4o-mini': { cubentUnits: 0.65, available: true, supportsImages: false },
  'o3-mini': { cubentUnits: 1.0, available: true, supportsImages: false },
  'o3-mini-high-reasoning': { cubentUnits: 1.1, available: true, supportsImages: true },

  // Google Gemini Models
  'gemini-2-5-flash': { cubentUnits: 0.3, available: true, supportsImages: true },
  'gemini-2-5-flash-thinking': { cubentUnits: 0.4, available: true, supportsImages: true },
  'gemini-2-5-pro': { cubentUnits: 0.85, available: true, supportsImages: true },
  'gemini-2-0-flash': { cubentUnits: 0.45, available: true, supportsImages: true },
  'gemini-2-0-pro': { cubentUnits: 0.70, available: true, supportsImages: true },

  // DeepSeek Models
  'deepseek-v3': { cubentUnits: 0.25, available: true, supportsImages: false },
  'deepseek-r1': { cubentUnits: 0.35, available: true, supportsImages: false },

  // xAI Grok Models
  'grok-3': { cubentUnits: 1.1, available: true, supportsImages: false },
  'grok-3-mini': { cubentUnits: 0.30, available: true, supportsImages: false },
  'grok-2-vision': { cubentUnits: 0.70, available: true, supportsImages: true },
};

/**
 * Default pricing for unknown models
 */
const DEFAULT_PRICING: ModelPricing = {
  cubentUnits: 1.0,
  available: true,
  supportsImages: false,
};

/**
 * Calculate Cubent units for a message
 */
export function calculateCubentUnits(
  modelId: string,
  messageContent: string,
  hasImages: boolean = false
): number {
  // Normalize model ID (remove provider prefixes, convert to lowercase)
  const normalizedModelId = normalizeModelId(modelId);
  
  // Get model pricing
  const pricing = MODEL_PRICING[normalizedModelId] || DEFAULT_PRICING;
  
  // Base units for the model
  let units = pricing.cubentUnits;
  
  // Add extra units for images if the model supports them
  if (hasImages && pricing.supportsImages) {
    units += 0.1; // Small additional cost for image processing
  }
  
  // Add units based on message length (very basic scaling)
  const messageLength = messageContent.length;
  if (messageLength > 10000) {
    units += 0.2; // Extra units for very long messages
  } else if (messageLength > 5000) {
    units += 0.1; // Small extra for long messages
  }
  
  return Math.round(units * 100) / 100; // Round to 2 decimal places
}

/**
 * Normalize model ID to match our pricing table
 */
function normalizeModelId(modelId: string): string {
  // Remove common provider prefixes
  let normalized = modelId
    .replace(/^(anthropic\/|openai\/|google\/|deepseek\/|xai\/)/i, '')
    .toLowerCase()
    .replace(/[_\s]/g, '-');
  
  // Handle common model name variations
  const modelMappings: Record<string, string> = {
    'claude-3.7-sonnet': 'claude-3-7-sonnet',
    'claude-3.5-sonnet': 'claude-3-5-sonnet',
    'claude-3.5-haiku': 'claude-3-5-haiku',
    'claude-3-haiku': 'claude-3-haiku',
    'gpt-4o': 'gpt-4o',
    'gpt-4.5-preview': 'gpt-4-5-preview',
    'gpt-4o-mini': 'gpt-4o-mini',
    'gemini-2.5-flash': 'gemini-2-5-flash',
    'gemini-2.5-pro': 'gemini-2-5-pro',
    'gemini-2.0-flash': 'gemini-2-0-flash',
    'gemini-2.0-pro': 'gemini-2-0-pro',
    'deepseek-v3': 'deepseek-v3',
    'deepseek-r1': 'deepseek-r1',
    'grok-3': 'grok-3',
    'grok-3-mini': 'grok-3-mini',
    'grok-2-vision': 'grok-2-vision',
  };
  
  return modelMappings[normalized] || normalized;
}

/**
 * Get model pricing information
 */
export function getModelPricing(modelId: string): ModelPricing {
  const normalizedModelId = normalizeModelId(modelId);
  return MODEL_PRICING[normalizedModelId] || DEFAULT_PRICING;
}

/**
 * Check if a user has enough units for a message
 */
export function canSendMessage(
  currentUnits: number,
  unitLimit: number,
  modelId: string,
  messageContent: string,
  hasImages: boolean = false
): boolean {
  const requiredUnits = calculateCubentUnits(modelId, messageContent, hasImages);
  return (currentUnits + requiredUnits) <= unitLimit;
}

/**
 * Get remaining units
 */
export function getRemainingUnits(currentUnits: number, unitLimit: number): number {
  return Math.max(0, unitLimit - currentUnits);
}

/**
 * Calculate usage percentage
 */
export function getUsagePercentage(currentUnits: number, unitLimit: number): number {
  if (unitLimit === 0) return 0;
  return Math.min(100, (currentUnits / unitLimit) * 100);
}
