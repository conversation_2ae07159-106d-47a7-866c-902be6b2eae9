"use strict";
exports.lt = void 0;
var _index = require("./lt/_lib/formatDistance.cjs");
var _index2 = require("./lt/_lib/formatLong.cjs");
var _index3 = require("./lt/_lib/formatRelative.cjs");
var _index4 = require("./lt/_lib/localize.cjs");
var _index5 = require("./lt/_lib/match.cjs");

/**
 * @category Locales
 * @summary Lithuanian locale.
 * @language Lithuanian
 * @iso-639-2 lit
 * <AUTHOR> [@pshpak](https://github.com/pshpak)
 * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)
 */
const lt = (exports.lt = {
  code: "lt",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
