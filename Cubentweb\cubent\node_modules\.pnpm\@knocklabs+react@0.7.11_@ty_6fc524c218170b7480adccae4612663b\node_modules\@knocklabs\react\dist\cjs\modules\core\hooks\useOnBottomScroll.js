"use strict";const a=require("lodash.debounce"),n=require("react"),i=t=>t&&typeof t=="object"&&"default"in t?t:{default:t},f=i(a),d=()=>{};function b(t){const l=t.callback??d,o=t.ref,s=t.offset??0,r=n.useMemo(()=>f.default(l,200),[l]),c=n.useCallback(()=>{if(o.current){const e=o.current,u=Math.round(e.scrollTop+e.clientHeight);Math.round(e.scrollHeight-s)<=u&&r()}},[r]);n.useEffect(()=>{let e;return o.current&&(e=o.current,o.current.addEventListener("scroll",c)),()=>{e&&e.removeEventListener("scroll",c)}},[c])}module.exports=b;
//# sourceMappingURL=useOnBottomScroll.js.map
