import n, { useState as p, useRef as f, use<PERSON><PERSON>back as m, useEffect as d } from "react";
import { useKnockFeed as v, useTranslations as b } from "@knocklabs/react-core";
import { createPopper as h } from "@popperjs/core";
import { CloseCircle as C } from "../../../core/components/Icons/CloseCircle.mjs";
const g = ({
  item: i
}) => {
  const {
    colorMode: a,
    feedClient: l
  } = v(), {
    t: c
  } = b(), [t, s] = p(!1), o = f(null), r = f(null), u = m(
    (e) => {
      e.preventDefault(), e.stopPropagation(), l.markAsArchived(i);
    },
    // TODO: Check if we can remove this disable
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [i]
  );
  return d(() => {
    if (o.current && r.current && t) {
      const e = h(o.current, r.current, {
        placement: "top-end",
        modifiers: [{
          name: "offset",
          options: {
            offset: [0, 8]
          }
        }]
      });
      return () => {
        e.destroy();
      };
    }
  }, [t]), /* @__PURE__ */ n.createElement("button", { ref: o, onClick: u, onMouseEnter: () => s(!0), onMouseLeave: () => s(!1), type: "button", "aria-label": c("archiveNotification"), className: `rnf-archive-notification-btn rnf-archive-notification-btn--${a}` }, /* @__PURE__ */ n.createElement(C, { "aria-hidden": !0 }), t && /* @__PURE__ */ n.createElement("div", { ref: r, className: `rnf-tooltip rnf-tooltip--${a}` }, c("archiveNotification")));
};
export {
  g as ArchiveButton
};
//# sourceMappingURL=ArchiveButton.mjs.map
