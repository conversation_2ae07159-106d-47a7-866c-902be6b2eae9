{"version": 3, "file": "ButtonGroup.mjs", "sources": ["../../../../../../src/modules/core/components/Button/ButtonGroup.tsx"], "sourcesContent": ["import { FunctionComponent, ReactNode } from \"react\";\n\nimport \"./styles.css\";\n\nexport const ButtonGroup: FunctionComponent<{\n  children?: ReactNode | undefined;\n}> = ({ children }) => <div className=\"rnf-button-group\">{children}</div>;\n"], "names": ["ButtonGroup", "children", "React"], "mappings": ";;AAIO,MAAMA,IAERA,CAAC;AAAA,EAAEC,UAAAA;AAAS,MAAOC,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,mBAAA,GAAoBD,CAAS;"}