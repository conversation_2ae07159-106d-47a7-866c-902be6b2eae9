{"version": 3, "file": "NotificationFeedPopover.js", "sources": ["../../../../../../src/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.tsx"], "sourcesContent": ["import { Feed, FeedStoreState } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { Placement, createPopper } from \"@popperjs/core\";\nimport React, { RefObject, useEffect } from \"react\";\n\nimport useComponentVisible from \"../../../core/hooks/useComponentVisible\";\nimport { NotificationFeed, NotificationFeedProps } from \"../NotificationFeed\";\n\nimport \"./styles.css\";\n\ntype OnOpenOptions = {\n  store: FeedStoreState;\n  feedClient: Feed;\n};\n\nconst defaultOnOpen = ({ store, feedClient }: OnOpenOptions) => {\n  if (store.metadata.unseen_count > 0) {\n    feedClient.markAllAsSeen();\n  }\n};\n\nexport interface NotificationFeedPopoverProps extends NotificationFeedProps {\n  isVisible: boolean;\n  onOpen?: (arg: OnOpenOptions) => void;\n  onClose: (e: Event) => void;\n  buttonRef: RefObject<HTMLElement | null>;\n  closeOnClickOutside?: boolean;\n  placement?: Placement;\n}\n\nexport const NotificationFeedPopover: React.FC<\n  NotificationFeedPopoverProps\n> = ({\n  isVisible,\n  onOpen = defaultOnOpen,\n  onClose,\n  buttonRef,\n  closeOnClickOutside = true,\n  placement = \"bottom-end\",\n  ...feedProps\n}) => {\n  const { t } = useTranslations();\n  const { colorMode, feedClient, useFeedStore } = useKnockFeed();\n  const store = useFeedStore();\n\n  const { ref: popperRef } = useComponentVisible(isVisible, onClose, {\n    closeOnClickOutside,\n  });\n\n  useEffect(() => {\n    // Whenever the feed is opened, we want to invoke the `onOpen` callback\n    // function to handle any side effects.\n    if (isVisible && onOpen) {\n      onOpen({ store, feedClient });\n    }\n  }, [isVisible, onOpen, store, feedClient]);\n\n  useEffect(() => {\n    if (buttonRef.current && popperRef.current) {\n      const popperInstance = createPopper(\n        buttonRef.current,\n        popperRef.current,\n        {\n          strategy: \"fixed\",\n          placement,\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      // Cleanup\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [buttonRef, popperRef, placement]);\n\n  return (\n    <div\n      className={`rnf-notification-feed-popover rnf-notification-feed-popover--${colorMode}`}\n      style={{\n        visibility: isVisible ? \"visible\" : \"hidden\",\n        opacity: isVisible ? 1 : 0,\n      }}\n      ref={popperRef}\n      role=\"dialog\"\n      aria-label={t(\"notifications\")}\n      tabIndex={-1}\n    >\n      <div className=\"rnf-notification-feed-popover__inner\">\n        <NotificationFeed {...feedProps} />\n      </div>\n    </div>\n  );\n};\n"], "names": ["defaultOnOpen", "store", "feedClient", "metadata", "unseen_count", "markAllAsSeen", "NotificationFeedPopover", "isVisible", "onOpen", "onClose", "buttonRef", "closeOnClickOutside", "placement", "feedProps", "t", "useTranslations", "colorMode", "useFeedStore", "useKnockFeed", "ref", "popperRef", "useComponentVisible", "useEffect", "current", "popperInstance", "createPopper", "strategy", "modifiers", "name", "options", "offset", "destroy", "visibility", "opacity", "React", "NotificationFeed"], "mappings": "2dAeMA,EAAgBA,CAAC,CAAEC,MAAAA,EAAOC,WAAAA,CAA0B,IAAM,CAC1DD,EAAME,SAASC,aAAe,GAChCF,EAAWG,cAAc,CAE7B,EAWaC,EAETA,CAAC,CACHC,UAAAA,EACAC,OAAAA,EAASR,EACTS,QAAAA,EACAC,UAAAA,EACAC,oBAAAA,EAAsB,GACtBC,UAAAA,EAAY,aACZ,GAAGC,CACL,IAAM,CACE,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxB,CAAEC,UAAAA,EAAWd,WAAAA,EAAYe,aAAAA,GAAiBC,eAAa,EACvDjB,EAAQgB,EAAa,EAErB,CAAEE,IAAKC,CAAAA,EAAcC,EAAoBd,EAAWE,EAAS,CACjEE,oBAAAA,CAAAA,CACD,EAEDW,OAAAA,EAAAA,UAAU,IAAM,CAGVf,GAAaC,GACRA,EAAA,CAAEP,MAAAA,EAAOC,WAAAA,CAAAA,CAAY,GAE7B,CAACK,EAAWC,EAAQP,EAAOC,CAAU,CAAC,EAEzCoB,EAAAA,UAAU,IAAM,CACVZ,GAAAA,EAAUa,SAAWH,EAAUG,QAAS,CAC1C,MAAMC,EAAiBC,EAAAA,aACrBf,EAAUa,QACVH,EAAUG,QACV,CACEG,SAAU,QACVd,UAAAA,EACAe,UAAW,CACT,CACEC,KAAM,SACNC,QAAS,CACPC,OAAQ,CAAC,EAAG,CAAC,CAAA,CAEhB,CAAA,CAAA,CAGP,EAGA,MAAO,IAAM,CACXN,EAAeO,QAAQ,CACzB,CAAA,CAED,EAAA,CAACrB,EAAWU,EAAWR,CAAS,CAAC,0BAGjC,MACC,CAAA,UAAW,gEAAgEI,CAAS,GACpF,MAAO,CACLgB,WAAYzB,EAAY,UAAY,SACpC0B,QAAS1B,EAAY,EAAI,CAC3B,EACA,IAAKa,EACL,KAAK,SACL,aAAYN,EAAE,eAAe,EAC7B,SAAU,IAEToB,EAAAA,QAAA,cAAA,MAAA,CAAI,UAAU,wCACbA,EAAAA,QAAA,cAACC,oBAAiB,GAAItB,CAAAA,CAAU,CAClC,CACF,CAEJ"}