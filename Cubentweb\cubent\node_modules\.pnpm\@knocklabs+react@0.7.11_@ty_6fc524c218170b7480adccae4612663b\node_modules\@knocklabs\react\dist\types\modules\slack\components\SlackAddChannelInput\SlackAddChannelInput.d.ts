import { SlackChannelConnection } from '@knocklabs/client';
import { FunctionComponent } from 'react';
export interface SlackAddChannelInputProps {
    inErrorState: boolean;
    connectedChannels: SlackChannelConnection[];
    updateConnectedChannels: (channels: SlackChannelConnection[]) => void;
    connectedChannelsError: string | null;
    connectedChannelsUpdating: boolean;
}
declare const SlackAddChannelInput: FunctionComponent<SlackAddChannelInputProps>;
export default SlackAddChannelInput;
//# sourceMappingURL=SlackAddChannelInput.d.ts.map