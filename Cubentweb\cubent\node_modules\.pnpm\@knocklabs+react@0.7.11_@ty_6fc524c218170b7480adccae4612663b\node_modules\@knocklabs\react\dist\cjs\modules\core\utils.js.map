{"version": 3, "file": "utils.js", "sources": ["../../../../src/modules/core/utils.ts"], "sourcesContent": ["export const openPopupWindow = (url: string) => {\n  const width = 600;\n  const height = 800;\n  // Calculate the position to center the window\n  const screenLeft = window.screenLeft ?? window.screenX;\n  const screenTop = window.screenTop ?? window.screenY;\n\n  const innerWidth =\n    window.innerWidth ?? document.documentElement.clientWidth ?? screen.width;\n  const innerHeight =\n    window.innerHeight ??\n    document.documentElement.clientHeight ??\n    screen.height;\n\n  const left = innerWidth / 2 - width / 2 + screenLeft;\n  const top = innerHeight / 2 - height / 2 + screenTop;\n\n  // Window features\n  const features = `width=${width},height=${height},top=${top},left=${left}`;\n\n  window.open(url, \"_blank\", features);\n};\n"], "names": ["openPopupWindow", "url", "screenLeft", "window", "screenX", "screenTop", "screenY", "innerWidth", "document", "documentElement", "clientWidth", "screen", "width", "innerHeight", "clientHeight", "height", "left", "features", "open"], "mappings": "gFAAaA,MAAAA,EAAmBC,GAAgB,CAIxCC,MAAAA,EAAaC,OAAOD,YAAcC,OAAOC,QACzCC,EAAYF,OAAOE,WAAaF,OAAOG,QAEvCC,EACJJ,OAAOI,YAAcC,SAASC,gBAAgBC,aAAeC,OAAOC,MAChEC,EACJV,OAAOU,aACPL,SAASC,gBAAgBK,cACzBH,OAAOI,OAEHC,EAAOT,EAAa,EAAIK,IAAQ,EAAIV,EAIpCe,EAAW,4BAHLJ,EAAc,EAAIE,IAAS,EAAIV,CAGgB,SAASW,CAAI,GAEjEE,OAAAA,KAAKjB,EAAK,SAAUgB,CAAQ,CACrC"}