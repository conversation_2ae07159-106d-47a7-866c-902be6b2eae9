"use strict";const n=require("react"),f=require("@knocklabs/react-core"),s=require("@telegraph/icon"),l=require("@telegraph/layout"),c=require("@telegraph/typography");;/* empty css               */const g=require("./MsTeamsChannelInTeamCombobox.js"),T=require("./MsTeamsConnectionError.js"),b=require("./MsTeamsTeamCombobox.js");;/* empty css            */const h=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=h(n),q=({msTeamsChannelsRecipientObject:e,teamQueryOptions:m,channelQueryOptions:u})=>{const[a,i]=n.useState(null),{data:o}=f.useConnectedMsTeamsChannels({msTeamsChannelsRecipientObject:e}),d=n.useCallback(C=>(o==null?void 0:o.filter(r=>r.ms_teams_team_id===C&&!!r.ms_teams_channel_id).length)??0,[o]);return t.default.createElement(l.Stack,{className:"tgph rtk-combobox__grid",gap:"3"},t.default.createElement(c.Text,{color:"gray",size:"2",as:"div"},"Team"),t.default.createElement(b.MsTeamsTeamCombobox,{team:a,onTeamChange:i,getChannelCount:d,queryOptions:m}),t.default.createElement(l.Stack,{alignItems:"center",gap:"3",minHeight:"8",style:{alignSelf:"start"}},t.default.createElement(s.Icon,{color:"gray",size:"1",icon:s.Lucide.CornerDownRight,"aria-hidden":!0}),t.default.createElement(c.Text,{color:"gray",size:"2",as:"div"},"Channels")),t.default.createElement(g.MsTeamsChannelInTeamCombobox,{teamId:a==null?void 0:a.id,msTeamsChannelsRecipientObject:e,queryOptions:u}),t.default.createElement(T,null))};module.exports=q;
//# sourceMappingURL=MsTeamsChannelCombobox.js.map
