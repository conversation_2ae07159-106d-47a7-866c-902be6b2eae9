{"version": 3, "file": "Banner.js", "sources": ["../../../../../../src/modules/guide/components/Banner/Banner.tsx"], "sourcesContent": ["import { ColorMode, useGuide } from \"@knocklabs/react-core\";\nimport clsx from \"clsx\";\nimport React from \"react\";\n\nimport { maybeNavigateToUrlWithDelay } from \"../helpers\";\nimport { <PERSON>tonContent, TargetButton, TargetButtonWithGuide } from \"../types\";\n\nimport \"./styles.css\";\n\nconst MESSAGE_TYPE = \"banner\";\n\nconst Root: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nRoot.displayName = \"BannerView.Root\";\n\nconst Content: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__message\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nContent.displayName = \"BannerView.Content\";\n\nconst Title: React.FC<\n  { title: string } & React.ComponentPropsWithRef<\"div\">\n> = ({ title, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__title\", className)} {...props}>\n      {title}\n    </div>\n  );\n};\nTitle.displayName = \"BannerView.Title\";\n\nconst Body: React.FC<{ body: string } & React.ComponentPropsWithRef<\"div\">> = ({\n  body,\n  className,\n  ...props\n}) => {\n  return (\n    <div\n      className={clsx(\"knock-guide-banner__body\", className)}\n      dangerouslySetInnerHTML={{ __html: body }}\n      {...props}\n    />\n  );\n};\nBody.displayName = \"BannerView.Body\";\n\nconst Actions: React.FC<\n  React.PropsWithChildren<React.ComponentPropsWithRef<\"div\">>\n> = ({ children, className, ...props }) => {\n  return (\n    <div className={clsx(\"knock-guide-banner__actions\", className)} {...props}>\n      {children}\n    </div>\n  );\n};\nActions.displayName = \"BannerView.Actions\";\n\nconst PrimaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\"knock-guide-banner__action\", className)}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nPrimaryButton.displayName = \"BannerView.PrimaryButton\";\n\nconst SecondaryButton: React.FC<\n  ButtonContent & React.ComponentPropsWithRef<\"button\">\n> = ({ text, action, className, ...props }) => {\n  return (\n    <button\n      className={clsx(\n        \"knock-guide-banner__action knock-guide-banner__action--secondary\",\n        className,\n      )}\n      {...props}\n    >\n      {text}\n    </button>\n  );\n};\nSecondaryButton.displayName = \"BannerView.SecondaryButton\";\n\nconst DismissButton: React.FC<React.ComponentPropsWithRef<\"button\">> = ({\n  className,\n  ...props\n}) => {\n  return (\n    <button className={clsx(\"knock-guide-banner__close\", className)} {...props}>\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"18\"\n        height=\"18\"\n        fill=\"none\"\n      >\n        <g fill=\"#60646C\" fillRule=\"evenodd\" clipRule=\"evenodd\">\n          <path d=\"M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z\" />\n          <path d=\"M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z\" />\n        </g>\n      </svg>\n    </button>\n  );\n};\nDismissButton.displayName = \"BannerView.DismissButton\";\n\ntype BannerContent = {\n  title: string;\n  body: string;\n  primary_button?: ButtonContent;\n  secondary_button?: ButtonContent;\n  dismissible?: boolean;\n};\n\nconst DefaultView: React.FC<{\n  content: BannerContent;\n  colorMode?: ColorMode;\n  onDismiss?: () => void;\n  onButtonClick?: (e: React.MouseEvent, button: TargetButton) => void;\n}> = ({ content, colorMode = \"light\", onDismiss, onButtonClick }) => {\n  return (\n    <Root data-knock-color-mode={colorMode}>\n      <Content>\n        <Title title={content.title} />\n        <Body body={content.body} />\n      </Content>\n      <Actions>\n        {content.secondary_button && (\n          <SecondaryButton\n            text={content.secondary_button.text}\n            action={content.secondary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.secondary_button!;\n                onButtonClick(e, { name: \"secondary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.primary_button && (\n          <PrimaryButton\n            text={content.primary_button.text}\n            action={content.primary_button.action}\n            onClick={(e) => {\n              if (onButtonClick) {\n                const { text, action } = content.primary_button!;\n                onButtonClick(e, { name: \"primary_button\", text, action });\n              }\n            }}\n          />\n        )}\n\n        {content.dismissible && <DismissButton onClick={onDismiss} />}\n      </Actions>\n    </Root>\n  );\n};\nDefaultView.displayName = \"BannerView.Default\";\n\ntype BannerProps = {\n  guideKey?: string;\n  onButtonClick?: (e: React.MouseEvent, target: TargetButtonWithGuide) => void;\n};\n\nexport const Banner: React.FC<BannerProps> = ({ guideKey, onButtonClick }) => {\n  const { guide, step, colorMode } = useGuide({\n    key: guideKey,\n    type: MESSAGE_TYPE,\n  });\n\n  React.useEffect(() => {\n    if (step) step.markAsSeen();\n  }, [step]);\n\n  if (!guide || !step) return null;\n\n  return (\n    <DefaultView\n      content={step.content as BannerContent}\n      colorMode={colorMode}\n      onDismiss={() => step.markAsArchived()}\n      onButtonClick={(e, button) => {\n        const metadata = { ...button, type: \"button_click\" };\n        step.markAsInteracted({ metadata });\n\n        return onButtonClick\n          ? onButtonClick(e, { button, step, guide })\n          : maybeNavigateToUrlWithDelay(button.action);\n      }}\n    />\n  );\n};\nBanner.displayName = \"Banner\";\n\nexport const BannerView = {} as {\n  Default: typeof DefaultView;\n  Root: typeof Root;\n  Content: typeof Content;\n  Title: typeof Title;\n  Body: typeof Body;\n  Actions: typeof Actions;\n  PrimaryButton: typeof PrimaryButton;\n  SecondaryButton: typeof SecondaryButton;\n  DismissButton: typeof DismissButton;\n};\n\nObject.assign(BannerView, {\n  Default: DefaultView,\n  Root,\n  Content,\n  Title,\n  Body,\n  Actions,\n  PrimaryButton,\n  SecondaryButton,\n  DismissButton,\n});\n"], "names": ["MESSAGE_TYPE", "Root", "children", "className", "props", "React", "clsx", "displayName", "Content", "Title", "title", "Body", "body", "__html", "Actions", "PrimaryButton", "text", "action", "SecondaryButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "colorMode", "on<PERSON><PERSON><PERSON>", "onButtonClick", "secondary_button", "e", "name", "primary_button", "dismissible", "Banner", "<PERSON><PERSON><PERSON>", "guide", "step", "useGuide", "key", "type", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "markAsArchived", "button", "metadata", "markAsInteracted", "maybeNavigateToUrlWithDelay", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "<PERSON><PERSON><PERSON>"], "mappings": "gSASMA,EAAe,SAEfC,EAEFA,CAAC,CAAEC,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,qBAAsBH,CAAS,EAAG,GAAIC,CAAAA,EACxDF,CACH,EAGJD,EAAKM,YAAc,kBAEnB,MAAMC,EAEFA,CAAC,CAAEN,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,8BAA+BH,CAAS,EAAG,GAAIC,CAAAA,EACjEF,CACH,EAGJM,EAAQD,YAAc,qBAEtB,MAAME,EAEFA,CAAC,CAAEC,MAAAA,EAAOP,UAAAA,EAAW,GAAGC,CAAM,IAE9BC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,4BAA6BH,CAAS,EAAG,GAAIC,CAAAA,EAC/DM,CACH,EAGJD,EAAMF,YAAc,mBAEpB,MAAMI,EAAwEA,CAAC,CAC7EC,KAAAA,EACAT,UAAAA,EACA,GAAGC,CACL,4BAEK,MACC,CAAA,UAAWE,EAAAA,QAAK,2BAA4BH,CAAS,EACrD,wBAAyB,CAAEU,OAAQD,CAAAA,EAC/BR,GAAAA,CACJ,CAAA,EAGNO,EAAKJ,YAAc,kBAEnB,MAAMO,EAEFA,CAAC,CAAEZ,SAAAA,EAAUC,UAAAA,EAAW,GAAGC,CAAM,IAEjCC,UAAA,cAAC,OAAI,UAAWC,EAAAA,QAAK,8BAA+BH,CAAS,EAAG,GAAIC,CAAAA,EACjEF,CACH,EAGJY,EAAQP,YAAc,qBAEtB,MAAMQ,EAEFA,CAAC,CAAEC,KAAAA,EAAMC,OAAAA,EAAQd,UAAAA,EAAW,GAAGC,CAAM,IAErCC,UAAA,cAAC,UACC,UAAWC,EAAAA,QAAK,6BAA8BH,CAAS,EACvD,GAAIC,CAAAA,EAEHY,CACH,EAGJD,EAAcR,YAAc,2BAE5B,MAAMW,EAEFA,CAAC,CAAEF,KAAAA,EAAMC,OAAAA,EAAQd,UAAAA,EAAW,GAAGC,CAAM,IAErCC,UAAA,cAAC,UACC,UAAWC,EAAAA,QACT,mEACAH,CACF,EACA,GAAIC,CAAAA,EAEHY,CACH,EAGJE,EAAgBX,YAAc,6BAE9B,MAAMY,EAAiEA,CAAC,CACtEhB,UAAAA,EACA,GAAGC,CACL,4BAEK,SAAO,CAAA,UAAWE,UAAK,4BAA6BH,CAAS,EAAG,GAAIC,GACnEC,EAAAA,QAAA,cAAC,OACC,MAAM,6BACN,MAAM,KACN,OAAO,KACP,KAAK,QAEJA,EAAAA,QAAA,cAAA,IAAA,CAAE,KAAK,UAAU,SAAS,UAAU,SAAS,WAC3CA,EAAA,QAAA,cAAA,OAAA,CAAK,EAAE,sFAAA,CAAsF,EAC7FA,EAAAA,QAAA,cAAA,OAAA,CAAK,EAAE,sFAAqF,CAC/F,CACF,CACF,EAGJc,EAAcZ,YAAc,2BAU5B,MAAMa,EAKDA,CAAC,CAAEC,QAAAA,EAASC,UAAAA,EAAY,QAASC,UAAAA,EAAWC,cAAAA,CAAc,IAE1DnB,EAAA,QAAA,cAAAJ,EAAA,CAAK,wBAAuBqB,CAAAA,0BAC1Bd,EACC,KAAAH,UAAA,cAACI,EAAM,CAAA,MAAOY,EAAQX,MAAM,EAC3BL,UAAA,cAAAM,EAAA,CAAK,KAAMU,EAAQT,KAAK,CAC3B,EACCP,EAAA,QAAA,cAAAS,EAAA,KACEO,EAAQI,0CACNP,EACC,CAAA,KAAMG,EAAQI,iBAAiBT,KAC/B,OAAQK,EAAQI,iBAAiBR,OACjC,QAAgBS,GAAA,CACd,GAAIF,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQI,iBACjCD,EAAcE,EAAG,CAAEC,KAAM,mBAAoBX,KAAAA,EAAMC,OAAAA,CAAAA,CAAQ,CAAA,CAC7D,CAGL,CAAA,EAEAI,EAAQO,wCACNb,EACC,CAAA,KAAMM,EAAQO,eAAeZ,KAC7B,OAAQK,EAAQO,eAAeX,OAC/B,QAAgBS,GAAA,CACd,GAAIF,EAAe,CACX,KAAA,CAAER,KAAAA,EAAMC,OAAAA,GAAWI,EAAQO,eACjCJ,EAAcE,EAAG,CAAEC,KAAM,iBAAkBX,KAAAA,EAAMC,OAAAA,CAAAA,CAAQ,CAAA,CAE7D,CAAA,CAEH,EAEAI,EAAQQ,qCAAgBV,EAAc,CAAA,QAASI,CAAa,CAAA,CAC/D,CACF,EAGJH,EAAYb,YAAc,qBAOnB,MAAMuB,EAAgCA,CAAC,CAAEC,SAAAA,EAAUP,cAAAA,CAAc,IAAM,CACtE,KAAA,CAAEQ,MAAAA,EAAOC,KAAAA,EAAMX,UAAAA,GAAcY,WAAS,CAC1CC,IAAKJ,EACLK,KAAMpC,CAAAA,CACP,EAMD,OAJAK,EAAAA,QAAMgC,UAAU,IAAM,CAChBJ,KAAWK,WAAW,CAAA,EACzB,CAACL,CAAI,CAAC,EAEL,CAACD,GAAS,CAACC,EAAa,KAGzB5B,EAAAA,QAAA,cAAAe,EAAA,CACC,QAASa,EAAKZ,QACd,UAAAC,EACA,UAAW,IAAMW,EAAKM,eAAe,EACrC,cAAe,CAACb,EAAGc,IAAW,CAC5B,MAAMC,EAAW,CAAE,GAAGD,EAAQJ,KAAM,cAAe,EACnDH,OAAAA,EAAKS,iBAAiB,CAAED,SAAAA,CAAAA,CAAU,EAE3BjB,EACHA,EAAcE,EAAG,CAAEc,OAAAA,EAAQP,KAAAA,EAAMD,MAAAA,CAAAA,CAAO,EACxCW,EAAAA,4BAA4BH,EAAOvB,MAAM,CAAA,EAE/C,CAEN,EACAa,EAAOvB,YAAc,SAEd,MAAMqC,EAAa,CAAA,EAY1BC,OAAOC,OAAOF,EAAY,CACxBG,QAAS3B,EACTnB,KAAAA,EACAO,QAAAA,EACAC,MAAAA,EACAE,KAAAA,EACAG,QAAAA,EACAC,cAAAA,EACAG,gBAAAA,EACAC,cAAAA,CACF,CAAC"}