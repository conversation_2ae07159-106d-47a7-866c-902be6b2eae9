{"version": 3, "file": "ChevronDown.js", "sources": ["../../../../../../src/modules/core/components/Icons/ChevronDown.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype ChevronDownProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst ChevronDown: FunctionComponent<ChevronDownProps> = ({\n  width = 8,\n  height = 6,\n  \"aria-hidden\": ariaHidden,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 8 6\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M1.74994 1.87512L3.99994 4.12512L6.24994 1.87512\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { ChevronDown };\n"], "names": ["ChevronDown", "width", "height", "ariaHidden", "React"], "mappings": "wKAQMA,EAAmDA,CAAC,CACxDC,MAAAA,EAAQ,EACRC,OAAAA,EAAS,EACT,cAAeC,CACjB,IACEC,EAAA,QAAA,cAAC,MACC,CAAA,MAAAH,EACA,OAAAC,EACA,QAAQ,UACR,KAAK,OACL,MAAM,6BACN,cAAaC,CAAAA,0BAEZ,OACC,CAAA,EAAE,mDACF,OAAO,eACP,YAAY,IACZ,cAAc,QACd,eAAe,OAAA,CAAO,CAE1B"}