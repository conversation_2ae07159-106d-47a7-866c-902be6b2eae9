{"version": 3, "file": "NotificationFeedPopover.mjs", "sources": ["../../../../../../src/modules/feed/components/NotificationFeedPopover/NotificationFeedPopover.tsx"], "sourcesContent": ["import { Feed, FeedStoreState } from \"@knocklabs/client\";\nimport { useKnockFeed, useTranslations } from \"@knocklabs/react-core\";\nimport { Placement, createPopper } from \"@popperjs/core\";\nimport React, { RefObject, useEffect } from \"react\";\n\nimport useComponentVisible from \"../../../core/hooks/useComponentVisible\";\nimport { NotificationFeed, NotificationFeedProps } from \"../NotificationFeed\";\n\nimport \"./styles.css\";\n\ntype OnOpenOptions = {\n  store: FeedStoreState;\n  feedClient: Feed;\n};\n\nconst defaultOnOpen = ({ store, feedClient }: OnOpenOptions) => {\n  if (store.metadata.unseen_count > 0) {\n    feedClient.markAllAsSeen();\n  }\n};\n\nexport interface NotificationFeedPopoverProps extends NotificationFeedProps {\n  isVisible: boolean;\n  onOpen?: (arg: OnOpenOptions) => void;\n  onClose: (e: Event) => void;\n  buttonRef: RefObject<HTMLElement | null>;\n  closeOnClickOutside?: boolean;\n  placement?: Placement;\n}\n\nexport const NotificationFeedPopover: React.FC<\n  NotificationFeedPopoverProps\n> = ({\n  isVisible,\n  onOpen = defaultOnOpen,\n  onClose,\n  buttonRef,\n  closeOnClickOutside = true,\n  placement = \"bottom-end\",\n  ...feedProps\n}) => {\n  const { t } = useTranslations();\n  const { colorMode, feedClient, useFeedStore } = useKnockFeed();\n  const store = useFeedStore();\n\n  const { ref: popperRef } = useComponentVisible(isVisible, onClose, {\n    closeOnClickOutside,\n  });\n\n  useEffect(() => {\n    // Whenever the feed is opened, we want to invoke the `onOpen` callback\n    // function to handle any side effects.\n    if (isVisible && onOpen) {\n      onOpen({ store, feedClient });\n    }\n  }, [isVisible, onOpen, store, feedClient]);\n\n  useEffect(() => {\n    if (buttonRef.current && popperRef.current) {\n      const popperInstance = createPopper(\n        buttonRef.current,\n        popperRef.current,\n        {\n          strategy: \"fixed\",\n          placement,\n          modifiers: [\n            {\n              name: \"offset\",\n              options: {\n                offset: [0, 8],\n              },\n            },\n          ],\n        },\n      );\n\n      // Cleanup\n      return () => {\n        popperInstance.destroy();\n      };\n    }\n  }, [buttonRef, popperRef, placement]);\n\n  return (\n    <div\n      className={`rnf-notification-feed-popover rnf-notification-feed-popover--${colorMode}`}\n      style={{\n        visibility: isVisible ? \"visible\" : \"hidden\",\n        opacity: isVisible ? 1 : 0,\n      }}\n      ref={popperRef}\n      role=\"dialog\"\n      aria-label={t(\"notifications\")}\n      tabIndex={-1}\n    >\n      <div className=\"rnf-notification-feed-popover__inner\">\n        <NotificationFeed {...feedProps} />\n      </div>\n    </div>\n  );\n};\n"], "names": ["defaultOnOpen", "store", "feedClient", "metadata", "unseen_count", "markAllAsSeen", "NotificationFeedPopover", "isVisible", "onOpen", "onClose", "buttonRef", "closeOnClickOutside", "placement", "feedProps", "t", "useTranslations", "colorMode", "useFeedStore", "useKnockFeed", "ref", "popperRef", "useComponentVisible", "useEffect", "current", "popperInstance", "createPopper", "strategy", "modifiers", "name", "options", "offset", "destroy", "visibility", "opacity", "React", "NotificationFeed"], "mappings": ";;;;;;;;AAeA,MAAMA,IAAgBA,CAAC;AAAA,EAAEC,OAAAA;AAAAA,EAAOC,YAAAA;AAA0B,MAAM;AAC1DD,EAAAA,EAAME,SAASC,eAAe,KAChCF,EAAWG,cAAc;AAE7B,GAWaC,IAETA,CAAC;AAAA,EACHC,WAAAA;AAAAA,EACAC,QAAAA,IAASR;AAAAA,EACTS,SAAAA;AAAAA,EACAC,WAAAA;AAAAA,EACAC,qBAAAA,IAAsB;AAAA,EACtBC,WAAAA,IAAY;AAAA,EACZ,GAAGC;AACL,MAAM;AACE,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GACxB;AAAA,IAAEC,WAAAA;AAAAA,IAAWd,YAAAA;AAAAA,IAAYe,cAAAA;AAAAA,MAAiBC,EAAa,GACvDjB,IAAQgB,EAAa,GAErB;AAAA,IAAEE,KAAKC;AAAAA,EAAAA,IAAcC,EAAoBd,GAAWE,GAAS;AAAA,IACjEE,qBAAAA;AAAAA,EAAAA,CACD;AAEDW,SAAAA,EAAU,MAAM;AAGd,IAAIf,KAAaC,KACRA,EAAA;AAAA,MAAEP,OAAAA;AAAAA,MAAOC,YAAAA;AAAAA,IAAAA,CAAY;AAAA,KAE7B,CAACK,GAAWC,GAAQP,GAAOC,CAAU,CAAC,GAEzCoB,EAAU,MAAM;AACVZ,QAAAA,EAAUa,WAAWH,EAAUG,SAAS;AAC1C,YAAMC,IAAiBC,EACrBf,EAAUa,SACVH,EAAUG,SACV;AAAA,QACEG,UAAU;AAAA,QACVd,WAAAA;AAAAA,QACAe,WAAW,CACT;AAAA,UACEC,MAAM;AAAA,UACNC,SAAS;AAAA,YACPC,QAAQ,CAAC,GAAG,CAAC;AAAA,UAAA;AAAA,QAEhB,CAAA;AAAA,MAAA,CAGP;AAGA,aAAO,MAAM;AACXN,QAAAA,EAAeO,QAAQ;AAAA,MACzB;AAAA,IAAA;AAAA,EAED,GAAA,CAACrB,GAAWU,GAAWR,CAAS,CAAC,mCAGjC,OACC,EAAA,WAAW,gEAAgEI,CAAS,IACpF,OAAO;AAAA,IACLgB,YAAYzB,IAAY,YAAY;AAAA,IACpC0B,SAAS1B,IAAY,IAAI;AAAA,EAC3B,GACA,KAAKa,GACL,MAAK,UACL,cAAYN,EAAE,eAAe,GAC7B,UAAU,MAEToB,gBAAAA,EAAA,cAAA,OAAA,EAAI,WAAU,0CACbA,gBAAAA,EAAA,cAACC,KAAiB,GAAItB,EAAAA,CAAU,CAClC,CACF;AAEJ;"}