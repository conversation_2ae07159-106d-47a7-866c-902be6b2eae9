{"version": 3, "file": "Avatar.js", "sources": ["../../../../../../src/modules/feed/components/NotificationCell/Avatar.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport \"./styles.css\";\n\nexport interface AvatarProps {\n  name: string;\n  src?: string | null;\n}\n\nexport const Avatar: React.FC<AvatarProps> = ({ name, src }) => {\n  function getInitials(name: string) {\n    const [firstName, lastName] = name.split(\" \");\n    return firstName && lastName\n      ? `${firstName.charAt(0)}${lastName.charAt(0)}`\n      : firstName\n        ? firstName.charAt(0)\n        : \"\";\n  }\n\n  return (\n    <div className=\"rnf-avatar\">\n      {src ? (\n        <img src={src} alt={name} className=\"rnf-avatar__image\" />\n      ) : (\n        <span className=\"rnf-avatar__initials\">{getInitials(name)}</span>\n      )}\n    </div>\n  );\n};\n"], "names": ["Avatar", "name", "src", "getInitials", "firstName", "lastName", "split", "char<PERSON>t", "React"], "mappings": "yMASaA,EAAgCA,CAAC,CAAEC,KAAAA,EAAMC,IAAAA,CAAI,IAAM,CAC9D,SAASC,EAAYF,EAAc,CACjC,KAAM,CAACG,EAAWC,CAAQ,EAAIJ,EAAKK,MAAM,GAAG,EAC5C,OAAOF,GAAaC,EAChB,GAAGD,EAAUG,OAAO,CAAC,CAAC,GAAGF,EAASE,OAAO,CAAC,CAAC,GAC3CH,EACEA,EAAUG,OAAO,CAAC,EAClB,EAAA,CAIN,OAAAC,UAAA,cAAC,OAAI,UAAU,cACZN,EACEM,EAAAA,QAAA,cAAA,MAAA,CAAI,IAAAN,EAAU,IAAKD,EAAM,UAAU,8CAEnC,OAAK,CAAA,UAAU,wBAAwBE,EAAYF,CAAI,CAAE,CAE9D,CAEJ"}