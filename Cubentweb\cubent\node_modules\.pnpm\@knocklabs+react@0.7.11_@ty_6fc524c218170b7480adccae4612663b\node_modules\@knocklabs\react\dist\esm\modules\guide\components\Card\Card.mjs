import { useGuide as C } from "@knocklabs/react-core";
import c from "clsx";
import a from "react";
import { isValidHttpUrl as h, maybeNavigateToUrlWithDelay as V } from "../helpers.mjs";
/* empty css            */
const v = "card", o = ({
  children: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card", t), ...r }, e);
o.displayName = "CardView.Root";
const u = ({
  children: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__message", t), ...r }, e);
u.displayName = "CardView.Content";
const f = ({
  children: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__header", t), ...r }, e);
f.displayName = "CardView.Header";
const w = ({
  headline: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__headline", t), ...r }, e);
w.displayName = "CardView.Headline";
const y = ({
  title: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__title", t), ...r }, e);
y.displayName = "CardView.Title";
const _ = ({
  body: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__body", t), dangerouslySetInnerHTML: {
  __html: e
}, ...r });
_.displayName = "CardView.Body";
const k = ({
  children: e,
  className: t,
  alt: r,
  ...i
}) => /* @__PURE__ */ a.createElement("img", { className: c("knock-guide-card__img", t), alt: r || "", ...i }, e);
k.displayName = "CardView.Img";
const p = ({
  children: e,
  className: t,
  ...r
}) => /* @__PURE__ */ a.createElement("div", { className: c("knock-guide-card__actions", t), ...r }, e);
p.displayName = "CardView.Actions";
const g = ({
  text: e,
  action: t,
  className: r,
  ...i
}) => /* @__PURE__ */ a.createElement("button", { className: c("knock-guide-card__action", r), ...i }, e);
g.displayName = "CardView.PrimaryButton";
const E = ({
  text: e,
  action: t,
  className: r,
  ...i
}) => /* @__PURE__ */ a.createElement("button", { className: c("knock-guide-card__action knock-guide-card__action--secondary", r), ...i }, e);
E.displayName = "CardView.SecondaryButton";
const N = ({
  className: e,
  ...t
}) => /* @__PURE__ */ a.createElement("button", { className: c("knock-guide-card__close", e), ...t }, /* @__PURE__ */ a.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "18", height: "18", fill: "none" }, /* @__PURE__ */ a.createElement("g", { fill: "#60646C", fillRule: "evenodd", clipRule: "evenodd" }, /* @__PURE__ */ a.createElement("path", { d: "M14.03 3.97a.75.75 0 0 1 0 1.06l-9 9a.75.75 0 0 1-1.06-1.06l9-9a.75.75 0 0 1 1.06 0Z" }), /* @__PURE__ */ a.createElement("path", { d: "M3.97 3.97a.75.75 0 0 1 1.06 0l9 9a.75.75 0 1 1-1.06 1.06l-9-9a.75.75 0 0 1 0-1.06Z" }))));
N.displayName = "CardView.DismissButton";
const b = ({
  content: e,
  colorMode: t = "light",
  onDismiss: r,
  onButtonClick: i,
  onImageClick: n
}) => /* @__PURE__ */ a.createElement(o, { "data-knock-color-mode": t }, /* @__PURE__ */ a.createElement(u, null, /* @__PURE__ */ a.createElement(f, null, /* @__PURE__ */ a.createElement(w, { headline: e.headline }), e.dismissible && /* @__PURE__ */ a.createElement(N, { onClick: r })), /* @__PURE__ */ a.createElement(y, { title: e.title }), /* @__PURE__ */ a.createElement(_, { body: e.body })), e.image && /* @__PURE__ */ a.createElement("a", { href: h(e.image.action) ? e.image.action : void 0, target: "_blank" }, /* @__PURE__ */ a.createElement(k, { src: e.image.url, alt: e.image.alt, onClick: (d) => {
  n && n(d, e.image);
} })), (e.primary_button || e.secondary_button) && /* @__PURE__ */ a.createElement(p, null, e.primary_button && /* @__PURE__ */ a.createElement(g, { text: e.primary_button.text, action: e.primary_button.action, onClick: (d) => {
  if (i) {
    const {
      text: s,
      action: l
    } = e.primary_button;
    i(d, {
      name: "primary_button",
      text: s,
      action: l
    });
  }
} }), e.secondary_button && /* @__PURE__ */ a.createElement(E, { text: e.secondary_button.text, action: e.secondary_button.action, onClick: (d) => {
  if (i) {
    const {
      text: s,
      action: l
    } = e.secondary_button;
    i(d, {
      name: "secondary_button",
      text: s,
      action: l
    });
  }
} })));
b.displayName = "CardView.Default";
const x = ({
  guideKey: e,
  onButtonClick: t,
  onImageClick: r
}) => {
  const {
    guide: i,
    step: n,
    colorMode: d
  } = C({
    key: e,
    type: v
  });
  return a.useEffect(() => {
    n && n.markAsSeen();
  }, [n]), !i || !n ? null : /* @__PURE__ */ a.createElement(b, { content: n.content, colorMode: d, onDismiss: () => n.markAsArchived(), onButtonClick: (s, l) => {
    const m = {
      ...l,
      type: "button_click"
    };
    return n.markAsInteracted({
      metadata: m
    }), t ? t(s, {
      button: l,
      step: n,
      guide: i
    }) : V(l.action);
  }, onImageClick: (s, l) => {
    const m = {
      ...l,
      type: "image_click"
    };
    if (n.markAsInteracted({
      metadata: m
    }), r)
      return r(s, {
        image: l,
        step: n,
        guide: i
      });
  } });
};
x.displayName = "Card";
const A = {};
Object.assign(A, {
  Default: b,
  Root: o,
  Content: u,
  Title: y,
  Body: _,
  Img: k,
  Actions: p,
  PrimaryButton: g,
  SecondaryButton: E,
  DismissButton: N
});
export {
  x as Card,
  A as CardView
};
//# sourceMappingURL=Card.mjs.map
