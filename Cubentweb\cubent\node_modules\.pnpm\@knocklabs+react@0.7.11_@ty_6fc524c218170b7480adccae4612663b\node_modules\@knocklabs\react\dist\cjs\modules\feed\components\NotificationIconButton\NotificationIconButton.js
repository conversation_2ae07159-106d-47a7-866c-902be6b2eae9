"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("@knocklabs/react-core"),i=require("react"),r=require("../../../core/components/Icons/Bell.js"),u=require("../UnseenBadge/UnseenBadge.js");;/* empty css            */const l=e=>e&&typeof e=="object"&&"default"in e?e:{default:e},t=l(i),f=t.default.forwardRef(({onClick:e,badgeCountType:n},o)=>{const{colorMode:a}=c.useKnockFeed();return t.default.createElement("button",{className:`rnf-notification-icon-button rnf-notification-icon-button--${a}`,"aria-label":"Open notification feed",ref:o,onClick:e},t.default.createElement(r.<PERSON>,{"aria-hidden":!0}),t.default.createElement(u.UnseenBadge,{badgeCountType:n}))});exports.NotificationIconButton=f;
//# sourceMappingURL=NotificationIconButton.js.map
