import { useKnockFeed as i, useTranslations as u } from "@knocklabs/react-core";
import * as t from "react";
import { CheckmarkCircle as k } from "../../../core/components/Icons/CheckmarkCircle.mjs";
/* empty css            */
const C = ({
  onClick: a
}) => {
  const {
    useFeedStore: r,
    feedClient: n,
    colorMode: s
  } = i(), {
    t: l
  } = u(), o = r((e) => e.items.filter((c) => !c.read_at)), d = r((e) => e.metadata.unread_count), m = t.useCallback((e) => {
    n.markAllAsRead(), a && a(e, o);
  }, [n, o, a]);
  return /* @__PURE__ */ t.createElement("button", { className: `rnf-mark-all-as-read rnf-mark-all-as-read--${s}`, disabled: d === 0, onClick: m, type: "button" }, l("markAllAsRead"), /* @__PURE__ */ t.createElement(k, { "aria-hidden": !0 }));
};
export {
  C as MarkAsRead
};
//# sourceMappingURL=MarkAsRead.mjs.map
