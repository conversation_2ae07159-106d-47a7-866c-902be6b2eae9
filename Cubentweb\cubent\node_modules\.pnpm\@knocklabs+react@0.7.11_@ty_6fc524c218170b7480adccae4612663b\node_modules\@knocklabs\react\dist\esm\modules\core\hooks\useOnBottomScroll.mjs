import i from "lodash.debounce";
import { use<PERSON>emo as f, use<PERSON>allback as u, useEffect as a } from "react";
const d = () => {
};
function k(t) {
  const r = t.callback ?? d, e = t.ref, l = t.offset ?? 0, n = f(() => i(r, 200), [r]), c = u(() => {
    if (e.current) {
      const o = e.current, s = Math.round(o.scrollTop + o.clientHeight);
      Math.round(o.scrollHeight - l) <= s && n();
    }
  }, [n]);
  a(() => {
    let o;
    return e.current && (o = e.current, e.current.addEventListener("scroll", c)), () => {
      o && o.removeEventListener("scroll", c);
    };
  }, [c]);
}
export {
  k as default
};
//# sourceMappingURL=useOnBottomScroll.mjs.map
