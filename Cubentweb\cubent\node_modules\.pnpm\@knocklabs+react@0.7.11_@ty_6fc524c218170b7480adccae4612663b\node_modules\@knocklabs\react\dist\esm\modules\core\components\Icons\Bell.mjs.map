{"version": 3, "file": "Bell.mjs", "sources": ["../../../../../../src/modules/core/components/Icons/Bell.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype BellIconProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst BellIcon: FunctionComponent<BellIconProps> = ({\n  width = 24,\n  height = 24,\n  \"aria-hidden\": aria<PERSON><PERSON><PERSON>,\n}) => {\n  return (\n    <svg\n      width={width}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      height={height}\n      aria-hidden={ariaHidden}\n    >\n      <path\n        d=\"M20.0474 16.4728C18.8436 14.9996 17.9938 14.2496 17.9938 10.1879C17.9938 6.46832 16.0944 5.14317 14.5311 4.49957C14.3235 4.41426 14.128 4.21832 14.0647 4.00504C13.7905 3.07176 13.0217 2.24957 11.9999 2.24957C10.978 2.24957 10.2088 3.07223 9.93736 4.00598C9.87408 4.2216 9.67861 4.41426 9.47096 4.49957C7.9058 5.1441 6.0083 6.46457 6.0083 10.1879C6.00596 14.2496 5.15611 14.9996 3.95237 16.4728C3.45362 17.0832 3.89049 17.9996 4.76283 17.9996H19.2416C20.1092 17.9996 20.5433 17.0803 20.0474 16.4728Z\"\n        stroke=\"currentColor\"\n        strokeWidth=\"1.5\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M14.9999 17.9988V18.7488C14.9999 19.5445 14.6838 20.3075 14.1212 20.8701C13.5586 21.4327 12.7955 21.7488 11.9999 21.7488C11.2042 21.7488 10.4412 21.4327 9.87856 20.8701C9.31595 20.3075 8.99988 19.5445 8.99988 18.7488V17.9988\"\n        stroke=\"currentColor\"\n        strokeWidth=\"1.5\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n};\n\nexport { BellIcon };\n"], "names": ["BellIcon", "width", "height", "ariaHidden", "React"], "mappings": ";AAQA,MAAMA,IAA6CA,CAAC;AAAA,EAClDC,OAAAA,IAAQ;AAAA,EACRC,QAAAA,IAAS;AAAA,EACT,eAAeC;AACjB,MAEKC,gBAAAA,EAAA,cAAA,OAAA,EACC,OAAAH,GACA,SAAQ,aACR,MAAK,QACL,QAAAC,GACA,eAAaC,KAEZC,gBAAAA,EAAA,cAAA,QAAA,EACC,GAAE,sfACF,QAAO,gBACP,aAAY,OACZ,eAAc,SACd,gBAAe,QAAO,CAAA,GAExBA,gBAAAA,EAAA,cAAC,UACC,GAAE,oOACF,QAAO,gBACP,aAAY,OACZ,eAAc,SACd,gBAAe,SAAO,CAE1B;"}