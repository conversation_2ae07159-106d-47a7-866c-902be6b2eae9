import { EventEmitter } from "events"
import { ApiStreamUsageChunk } from "../../api/transform/stream"

/**
 * Cubent Units Service
 * 
 * This service handles Cubent unit tracking for authenticated users.
 * It integrates with the webapp API to track and enforce unit limits.
 */
export class CubentUnitsService extends EventEmitter {
	private authToken: string | null = null
	private apiBaseUrl: string = "https://cubent.dev"

	constructor() {
		super()
	}

	/**
	 * Set authentication token for API calls
	 */
	public setAuthToken(token: string | null): void {
		this.authToken = token
	}

	/**
	 * Set API base URL (for development/testing)
	 */
	public setApiBaseUrl(url: string): void {
		this.apiBaseUrl = url
	}

	/**
	 * Track Cubent units usage for a message
	 */
	public async trackUnitsUsage(
		modelId: string,
		messageContent: string,
		hasImages: boolean = false,
		sessionId?: string
	): Promise<{
		success: boolean
		unitsUsed?: number
		totalUnitsUsed?: number
		unitsRemaining?: number
		unitsLimit?: number
		usagePercentage?: number
		error?: string
	}> {
		if (!this.authToken) {
			console.warn("[CubentUnits] No auth token available")
			return { success: false, error: "Not authenticated" }
		}

		try {
			const response = await fetch(`${this.apiBaseUrl}/api/extension/units/track`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${this.authToken}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					modelId,
					messageContent,
					hasImages,
					sessionId,
				}),
			})

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}))
				
				if (response.status === 402) {
					// Insufficient units
					this.emit('insufficient-units', {
						unitsRequired: errorData.unitsRequired,
						unitsRemaining: errorData.unitsRemaining,
						message: errorData.message,
					})
					return { 
						success: false, 
						error: errorData.message || 'Insufficient Cubent units',
						...errorData
					}
				}

				throw new Error(`HTTP ${response.status}: ${errorData.message || 'Failed to track units'}`)
			}

			const data = await response.json()
			
			// Emit usage update event
			this.emit('units-updated', {
				unitsUsed: data.unitsUsed,
				totalUnitsUsed: data.totalUnitsUsed,
				unitsRemaining: data.unitsRemaining,
				unitsLimit: data.unitsLimit,
				usagePercentage: data.usagePercentage,
			})

			// Emit warnings if usage is high
			if (data.usagePercentage >= 90) {
				this.emit('usage-critical', data)
			} else if (data.usagePercentage >= 75) {
				this.emit('usage-warning', data)
			}

			return { success: true, ...data }

		} catch (error) {
			console.error("[CubentUnits] Error tracking units:", error)
			return { 
				success: false, 
				error: error instanceof Error ? error.message : 'Unknown error' 
			}
		}
	}

	/**
	 * Get current units usage
	 */
	public async getUnitsUsage(): Promise<{
		success: boolean
		unitsUsed?: number
		unitsLimit?: number
		unitsRemaining?: number
		usagePercentage?: number
		subscriptionTier?: string
		error?: string
	}> {
		if (!this.authToken) {
			console.warn("[CubentUnits] No auth token available")
			return { success: false, error: "Not authenticated" }
		}

		try {
			const response = await fetch(`${this.apiBaseUrl}/api/extension/units/usage`, {
				headers: {
					'Authorization': `Bearer ${this.authToken}`,
					'Content-Type': 'application/json',
				},
			})

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}))
				throw new Error(`HTTP ${response.status}: ${errorData.message || 'Failed to get usage'}`)
			}

			const data = await response.json()
			return { success: true, ...data }

		} catch (error) {
			console.error("[CubentUnits] Error getting usage:", error)
			return { 
				success: false, 
				error: error instanceof Error ? error.message : 'Unknown error' 
			}
		}
	}

	/**
	 * Check if user can send a message (has enough units)
	 */
	public async canSendMessage(
		modelId: string,
		messageContent: string,
		hasImages: boolean = false
	): Promise<{
		canSend: boolean
		unitsRequired?: number
		unitsRemaining?: number
		reason?: string
	}> {
		if (!this.authToken) {
			return { 
				canSend: false, 
				reason: "Not authenticated" 
			}
		}

		try {
			// Get current usage
			const usage = await this.getUnitsUsage()
			if (!usage.success) {
				return { 
					canSend: false, 
					reason: usage.error || "Failed to check usage" 
				}
			}

			// For now, we'll do a simple check based on current usage
			// In a more sophisticated implementation, we could calculate the exact units needed
			const unitsRemaining = usage.unitsRemaining || 0
			
			if (unitsRemaining <= 0) {
				return {
					canSend: false,
					unitsRequired: 1, // Minimum required
					unitsRemaining,
					reason: "Insufficient Cubent units"
				}
			}

			return { canSend: true, unitsRemaining }

		} catch (error) {
			console.error("[CubentUnits] Error checking if can send message:", error)
			return { 
				canSend: false, 
				reason: error instanceof Error ? error.message : 'Unknown error' 
			}
		}
	}

	/**
	 * Show units usage notification
	 */
	public showUsageNotification(type: 'warning' | 'critical' | 'info', message: string): void {
		// This would integrate with VS Code's notification system
		// For now, we'll just emit an event that the extension can listen to
		this.emit('show-notification', { type, message })
	}

	/**
	 * Handle insufficient units scenario
	 */
	public handleInsufficientUnits(data: {
		unitsRequired: number
		unitsRemaining: number
		message: string
	}): void {
		this.showUsageNotification('critical', data.message)
		this.emit('insufficient-units', data)
	}

	/**
	 * Clean up resources
	 */
	public dispose(): void {
		this.removeAllListeners()
		this.authToken = null
	}
}
