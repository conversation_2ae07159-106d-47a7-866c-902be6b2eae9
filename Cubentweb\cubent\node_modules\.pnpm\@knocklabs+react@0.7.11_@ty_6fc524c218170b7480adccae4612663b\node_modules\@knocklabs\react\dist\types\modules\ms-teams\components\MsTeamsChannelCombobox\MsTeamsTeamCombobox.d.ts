import { MsTeamsTeam } from '@knocklabs/client';
import { MsTeamsTeamQueryOptions } from '@knocklabs/react-core';
import { FunctionComponent } from 'react';
interface MsTeamsTeamComboboxProps {
    team: MsTeamsTeam | null;
    onTeamChange: (team: MsTeamsTeam) => void;
    getChannelCount: (teamId: string) => number;
    queryOptions?: MsTeamsTeamQueryOptions;
}
export declare const MsTeamsTeamCombobox: FunctionComponent<MsTeamsTeamComboboxProps>;
export {};
//# sourceMappingURL=MsTeamsTeamCombobox.d.ts.map