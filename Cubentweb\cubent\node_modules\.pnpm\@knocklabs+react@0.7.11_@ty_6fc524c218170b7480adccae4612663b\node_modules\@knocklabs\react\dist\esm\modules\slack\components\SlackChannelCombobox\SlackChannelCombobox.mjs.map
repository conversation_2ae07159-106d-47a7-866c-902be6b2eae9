{"version": 3, "file": "SlackChannelCombobox.mjs", "sources": ["../../../../../../src/modules/slack/components/SlackChannelCombobox/SlackChannelCombobox.tsx"], "sourcesContent": ["import { SlackChannelConnection } from \"@knocklabs/client\";\nimport {\n  RecipientObject,\n  SlackChannelQueryOptions,\n  useConnectedSlackChannels,\n  useKnockSlackClient,\n  useSlackChannels,\n  useTranslations,\n} from \"@knocklabs/react-core\";\nimport { Combobox } from \"@telegraph/combobox\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { useMemo } from \"react\";\nimport { FunctionComponent } from \"react\";\n\nimport \"../../theme.css\";\nimport { sortSlackChannelsAlphabetically } from \"../../utils\";\nimport SlackAddChannelInput from \"../SlackAddChannelInput/SlackAddChannelInput\";\n\nimport SlackConnectionError from \"./SlackConnectionError\";\nimport SlackErrorMessage from \"./SlackErrorMessage\";\nimport \"./styles.css\";\n\nconst MAX_ALLOWED_CHANNELS = 1000;\n\nexport type SlackChannelComboboxInputMessages = {\n  disconnected: string;\n  error: string;\n  noChannelsConnected: string;\n  noSlackChannelsFound: string;\n};\n\nexport interface SlackChannelComboboxProps {\n  slackChannelsRecipientObject: RecipientObject;\n  queryOptions?: SlackChannelQueryOptions;\n  inputMessages?: SlackChannelComboboxInputMessages;\n}\n\nexport const SlackChannelCombobox: FunctionComponent<\n  SlackChannelComboboxProps\n> = ({ slackChannelsRecipientObject, queryOptions, inputMessages }) => {\n  const { t } = useTranslations();\n\n  // Gather API data\n  const { connectionStatus, errorLabel: connectionErrorLabel } =\n    useKnockSlackClient();\n\n  const { data: unsortedSlackChannels, isLoading: slackChannelsLoading } =\n    useSlackChannels({ queryOptions });\n\n  const slackChannels = useMemo(\n    () => sortSlackChannelsAlphabetically(unsortedSlackChannels),\n    [unsortedSlackChannels],\n  );\n\n  const {\n    data: connectedChannels,\n    updateConnectedChannels,\n    error: connectedChannelsError,\n    updating: connectedChannelsUpdating,\n  } = useConnectedSlackChannels({ slackChannelsRecipientObject });\n\n  const currentConnectedChannels = useMemo<SlackChannelConnection[]>(() => {\n    // Used to make sure we're only showing currently available channels to select from.\n    // There are cases where a channel is \"connected\" in Knock, but it wouldn't be\n    // posting to it if the channel is private and the Slackbot doesn't belong to it,\n    // so the channel won't show up here and it won't be posted to.\n    const slackChannelsMap = new Map(\n      slackChannels.map((channel) => [channel.id, channel]),\n    );\n\n    return (\n      connectedChannels?.filter((connectedChannel) => {\n        return slackChannelsMap.has(connectedChannel.channel_id || \"\");\n      }) || []\n    );\n  }, [connectedChannels, slackChannels]);\n\n  const inErrorState = useMemo(\n    () =>\n      connectionStatus === \"disconnected\" ||\n      connectionStatus === \"error\" ||\n      !!connectedChannelsError,\n    [connectedChannelsError, connectionStatus],\n  );\n\n  const inLoadingState = useMemo(\n    () =>\n      connectionStatus === \"connecting\" ||\n      connectionStatus === \"disconnecting\" ||\n      slackChannelsLoading,\n\n    [connectionStatus, slackChannelsLoading],\n  );\n\n  // Construct placeholder text\n  const searchPlaceholder = useMemo(() => {\n    const DEFAULT_INPUT_MESSAGES = {\n      disconnected: t(\"slackSearchbarDisconnected\"),\n      noChannelsConnected: t(\"slackSearchbarNoChannelsConnected\"),\n      noSlackChannelsFound: t(\"slackSearchbarNoChannelsFound\"),\n      channelsError: t(\"slackSearchbarChannelsError\"),\n    };\n\n    // Connection status message\n    if (connectionStatus === \"disconnected\") {\n      return inputMessages?.disconnected || DEFAULT_INPUT_MESSAGES.disconnected;\n    }\n\n    if (connectionStatus === \"error\") {\n      return inputMessages?.error || connectionErrorLabel;\n    }\n\n    // Channels status messages\n    if (!inLoadingState && slackChannels.length === 0) {\n      return (\n        inputMessages?.noSlackChannelsFound ||\n        DEFAULT_INPUT_MESSAGES.noSlackChannelsFound\n      );\n    }\n\n    const numberConnectedChannels = currentConnectedChannels?.length || 0;\n\n    if (currentConnectedChannels && numberConnectedChannels === 0) {\n      return (\n        inputMessages?.noChannelsConnected ||\n        DEFAULT_INPUT_MESSAGES.noChannelsConnected\n      );\n    }\n\n    return \"\";\n  }, [\n    connectionStatus,\n    inLoadingState,\n    slackChannels,\n    currentConnectedChannels,\n    inputMessages,\n    connectionErrorLabel,\n    t,\n  ]);\n\n  const comboboxValue = useMemo(\n    () => currentConnectedChannels.map((connection) => connection.channel_id),\n    [currentConnectedChannels],\n  );\n\n  if (slackChannels.length > MAX_ALLOWED_CHANNELS) {\n    return (\n      <SlackAddChannelInput\n        inErrorState={inErrorState}\n        connectedChannels={currentConnectedChannels || []}\n        updateConnectedChannels={updateConnectedChannels}\n        connectedChannelsError={connectedChannelsError}\n        connectedChannelsUpdating={connectedChannelsUpdating}\n      />\n    );\n  }\n\n  return (\n    <Stack className=\"tgph rsk-combobox__grid\" gap=\"3\">\n      <Text\n        color=\"gray\"\n        size=\"2\"\n        as=\"div\"\n        minHeight=\"8\"\n        className=\"rsk-combobox__label\"\n      >\n        Channels\n      </Text>\n      <Combobox.Root\n        value={comboboxValue}\n        onValueChange={(channelIds) => {\n          const updatedConnections = channelIds.map<SlackChannelConnection>(\n            (channelId) => ({\n              channel_id: channelId,\n            }),\n          );\n\n          updateConnectedChannels(updatedConnections).catch(console.error);\n        }}\n        placeholder={searchPlaceholder ?? \"\"}\n        disabled={inErrorState || slackChannels.length === 0}\n        errored={inErrorState}\n        closeOnSelect={false}\n        layout=\"wrap\"\n        modal={\n          // Modal comboboxes cause page layout to shift when body has padding. See KNO-7854.\n          false\n        }\n      >\n        <Combobox.Trigger />\n        <Combobox.Content>\n          <Combobox.Search\n            label={t(\"slackSearchChannels\")}\n            className=\"rsk-combobox__search\"\n          />\n          <Combobox.Options maxHeight=\"36\">\n            {slackChannels.map((channel) => (\n              <Combobox.Option key={channel.id} value={channel.id}>\n                <Stack align=\"center\" gap=\"1\">\n                  <Icon\n                    icon={channel.is_private ? Lucide.Lock : Lucide.Hash}\n                    size=\"0\"\n                    aria-hidden\n                  />\n                  {channel.name}\n                </Stack>\n              </Combobox.Option>\n            ))}\n          </Combobox.Options>\n          <Combobox.Empty />\n        </Combobox.Content>\n      </Combobox.Root>\n      <SlackConnectionError />\n      {!!connectedChannelsError && (\n        <SlackErrorMessage message={connectedChannelsError} />\n      )}\n    </Stack>\n  );\n};\n"], "names": ["MAX_ALLOWED_CHANNELS", "SlackChannelCombobox", "slackChannelsRecipientObject", "queryOptions", "inputMessages", "t", "useTranslations", "connectionStatus", "error<PERSON><PERSON><PERSON>", "connectionErrorLabel", "useKnockSlackClient", "data", "unsortedSlackChannels", "isLoading", "slackChannelsLoading", "useSlackChannels", "slackChannels", "useMemo", "sortSlackChannelsAlphabetically", "connectedChannels", "updateConnectedChannels", "error", "connectedChannelsError", "updating", "connectedChannelsUpdating", "useConnectedSlackChannels", "currentConnectedChannels", "slackChannelsMap", "Map", "map", "channel", "id", "filter", "connectedChannel", "has", "channel_id", "inErrorState", "inLoadingState", "searchPlaceholder", "DEFAULT_INPUT_MESSAGES", "disconnected", "noChannelsConnected", "noSlackChannelsFound", "channelsError", "length", "numberConnectedChannels", "comboboxValue", "connection", "React", "SlackAddChannelInput", "<PERSON><PERSON>", "Text", "Combobox", "channelIds", "updatedConnections", "channelId", "catch", "console", "Icon", "is_private", "Lucide", "Lock", "Hash", "name", "SlackConnectionError", "SlackErrorMessage"], "mappings": ";;;;;;;;;;;;AAwBA,MAAMA,IAAuB,KAehBC,IAETA,CAAC;AAAA,EAAEC,8BAAAA;AAAAA,EAA8BC,cAAAA;AAAAA,EAAcC,eAAAA;AAAc,MAAM;AAC/D,QAAA;AAAA,IAAEC,GAAAA;AAAAA,MAAMC,EAAgB,GAGxB;AAAA,IAAEC,kBAAAA;AAAAA,IAAkBC,YAAYC;AAAAA,MACpCC,EAAoB,GAEhB;AAAA,IAAEC,MAAMC;AAAAA,IAAuBC,WAAWC;AAAAA,MAC9CC,EAAiB;AAAA,IAAEZ,cAAAA;AAAAA,EAAAA,CAAc,GAE7Ba,IAAgBC,EACpB,MAAMC,EAAgCN,CAAqB,GAC3D,CAACA,CAAqB,CACxB,GAEM;AAAA,IACJD,MAAMQ;AAAAA,IACNC,yBAAAA;AAAAA,IACAC,OAAOC;AAAAA,IACPC,UAAUC;AAAAA,MACRC,EAA0B;AAAA,IAAEvB,8BAAAA;AAAAA,EAAAA,CAA8B,GAExDwB,IAA2BT,EAAkC,MAAM;AAKjEU,UAAAA,IAAmB,IAAIC,IAC3BZ,EAAca,IAAKC,CAAYA,MAAA,CAACA,EAAQC,IAAID,CAAO,CAAC,CACtD;AAGEX,YAAAA,KAAAA,gBAAAA,EAAmBa,OAAQC,CAAqBA,MACvCN,EAAiBO,IAAID,EAAiBE,cAAc,EAAE,OACzD,CAAE;AAAA,EAAA,GAET,CAAChB,GAAmBH,CAAa,CAAC,GAE/BoB,IAAenB,EACnB,MACEV,MAAqB,kBACrBA,MAAqB,WACrB,CAAC,CAACe,GACJ,CAACA,GAAwBf,CAAgB,CAC3C,GAEM8B,IAAiBpB,EACrB,MACEV,MAAqB,gBACrBA,MAAqB,mBACrBO,GAEF,CAACP,GAAkBO,CAAoB,CACzC,GAGMwB,IAAoBrB,EAAQ,MAAM;AACtC,UAAMsB,IAAyB;AAAA,MAC7BC,cAAcnC,EAAE,4BAA4B;AAAA,MAC5CoC,qBAAqBpC,EAAE,mCAAmC;AAAA,MAC1DqC,sBAAsBrC,EAAE,+BAA+B;AAAA,MACvDsC,eAAetC,EAAE,6BAA6B;AAAA,IAChD;AAGA,QAAIE,MAAqB;AAChBH,cAAAA,KAAAA,gBAAAA,EAAeoC,iBAAgBD,EAAuBC;AAG/D,QAAIjC,MAAqB;AACvB,cAAOH,KAAAA,gBAAAA,EAAeiB,UAASZ;AAIjC,QAAI,CAAC4B,KAAkBrB,EAAc4B,WAAW;AAE5CxC,cAAAA,KAAAA,gBAAAA,EAAesC,yBACfH,EAAuBG;AAIrBG,UAAAA,KAA0BnB,KAAAA,gBAAAA,EAA0BkB,WAAU;AAEhElB,WAAAA,KAA4BmB,MAA4B,KAExDzC,KAAAA,gBAAAA,EAAeqC,wBACfF,EAAuBE,sBAIpB;AAAA,EAAA,GACN,CACDlC,GACA8B,GACArB,GACAU,GACAtB,GACAK,GACAJ,CAAC,CACF,GAEKyC,IAAgB7B,EACpB,MAAMS,EAAyBG,IAAKkB,CAAeA,MAAAA,EAAWZ,UAAU,GACxE,CAACT,CAAwB,CAC3B;AAEIV,SAAAA,EAAc4B,SAAS5C,IAEvBgD,gBAAAA,EAAA,cAACC,KACC,cAAAb,GACA,mBAAmBV,KAA4B,IAC/C,yBAAAN,GACA,wBAAAE,GACA,2BAAAE,EACA,CAAA,IAKJwB,gBAAAA,EAAA,cAACE,GAAM,EAAA,WAAU,2BAA0B,KAAI,IAC7C,GAAAF,gBAAAA,EAAA,cAACG,GACC,EAAA,OAAM,QACN,MAAK,KACL,IAAG,OACH,WAAU,KACV,WAAU,sBAAqB,GAAA,UAGjC,GACAH,gBAAAA,EAAA,cAACI,EAAS,MAAT,EACC,OAAON,GACP,eAAgBO,CAAeA,MAAA;AACvBC,UAAAA,IAAqBD,EAAWxB,IACnC0B,CAAeA,OAAA;AAAA,MACdpB,YAAYoB;AAAAA,IAAAA,EAEhB;AAEAnC,IAAAA,EAAwBkC,CAAkB,EAAEE,MAAMC,QAAQpC,KAAK;AAAA,EAAA,GAEjE,aAAaiB,KAAqB,IAClC,UAAUF,KAAgBpB,EAAc4B,WAAW,GACnD,SAASR,GACT,eAAe,IACf,QAAO,QACP;AAAA;AAAA,IAEE;AAAA,uCAGDgB,EAAS,SAAT,IAAgB,mCAChBA,EAAS,SAAT,MACCJ,gBAAAA,EAAA,cAACI,EAAS,QAAT,EACC,OAAO/C,EAAE,qBAAqB,GAC9B,WAAU,uBAAsB,CAAA,GAElC2C,gBAAAA,EAAA,cAACI,EAAS,SAAT,EAAiB,WAAU,KACzBpC,GAAAA,EAAca,IAAKC,CAClBA,MAAAkB,gBAAAA,EAAA,cAACI,EAAS,QAAT,EAAgB,KAAKtB,EAAQC,IAAI,OAAOD,EAAQC,MAC9CiB,gBAAAA,EAAA,cAAAE,GAAA,EAAM,OAAM,UAAS,KAAI,IACxB,GAAAF,gBAAAA,EAAA,cAACU,GACC,EAAA,MAAM5B,EAAQ6B,aAAaC,EAAOC,OAAOD,EAAOE,MAChD,MAAK,KACL,eAAW,GAAA,CAAA,GAEZhC,EAAQiC,IACX,CACF,CACD,CACH,GACCf,gBAAAA,EAAA,cAAAI,EAAS,OAAT,IAAc,CACjB,CACF,GACAJ,gBAAAA,EAAA,cAACgB,GAAoB,IAAA,GACpB,CAAC,CAAC1C,qCACA2C,GAAkB,EAAA,SAAS3C,GAC7B,CACH;AAEJ;"}