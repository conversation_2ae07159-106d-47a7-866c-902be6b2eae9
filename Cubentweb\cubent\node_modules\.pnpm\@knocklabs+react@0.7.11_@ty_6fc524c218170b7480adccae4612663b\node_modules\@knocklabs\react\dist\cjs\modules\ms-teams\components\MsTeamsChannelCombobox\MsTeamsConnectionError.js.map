{"version": 3, "file": "MsTeamsConnectionError.js", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsConnectionError.tsx"], "sourcesContent": ["import { useKnockMsTeamsClient, useTranslations } from \"@knocklabs/react-core\";\nimport { FunctionComponent } from \"react\";\n\nimport MsTeamsErrorMessage from \"./MsTeamsErrorMessage\";\n\nconst MsTeamsConnectionError: FunctionComponent = () => {\n  const { t } = useTranslations();\n  const { connectionStatus } = useKnockMsTeamsClient();\n\n  if (connectionStatus === \"disconnected\" || connectionStatus === \"error\") {\n    return (\n      <MsTeamsErrorMessage\n        message={\n          connectionStatus === \"disconnected\"\n            ? t(\"msTeamsConnectionErrorOccurred\")\n            : t(\"msTeamsConnectionErrorExists\")\n        }\n      />\n    );\n  }\n\n  return null;\n};\n\nexport default MsTeamsConnectionError;\n"], "names": ["MsTeamsConnectionError", "t", "useTranslations", "connectionStatus", "useKnockMsTeamsClient", "React", "MsTeamsErrorMessage"], "mappings": "8KAKMA,EAA4CA,IAAM,CAChD,KAAA,CAAEC,EAAAA,GAAMC,kBAAgB,EACxB,CAAEC,iBAAAA,GAAqBC,wBAAsB,EAE/CD,OAAAA,IAAqB,gBAAkBA,IAAqB,QAE5DE,EAAA,QAAA,cAACC,EACC,CAAA,QAEML,EADJE,IAAqB,eACf,iCACA,8BADgC,CAGxC,CAAA,EAIC,IACT"}