{"version": 3, "file": "MsTeamsChannelCombobox.mjs", "sources": ["../../../../../../src/modules/ms-teams/components/MsTeamsChannelCombobox/MsTeamsChannelCombobox.tsx"], "sourcesContent": ["import { MsTea<PERSON>Team } from \"@knocklabs/client\";\nimport {\n  MsTeamsChannelQueryOptions,\n  MsTeamsTeamQueryOptions,\n  RecipientObject,\n  useConnectedMsTeamsChannels,\n} from \"@knocklabs/react-core\";\nimport { Icon, Lucide } from \"@telegraph/icon\";\nimport { Stack } from \"@telegraph/layout\";\nimport { Text } from \"@telegraph/typography\";\nimport { FunctionComponent, useCallback, useState } from \"react\";\n\nimport \"../../theme.css\";\n\nimport { MsTeamsChannelInTeamCombobox } from \"./MsTeamsChannelInTeamCombobox\";\nimport MsTeamsConnectionError from \"./MsTeamsConnectionError\";\nimport { MsTeamsTeamCombobox } from \"./MsTeamsTeamCombobox\";\nimport \"./styles.css\";\n\ninterface Props {\n  msTeamsChannelsRecipientObject: RecipientObject;\n  teamQueryOptions?: MsTeamsTeamQueryOptions;\n  channelQueryOptions?: MsTeamsChannelQueryOptions;\n}\n\nconst MsTeamsChannelCombobox: FunctionComponent<Props> = ({\n  msTeamsChannelsRecipientObject,\n  teamQueryOptions,\n  channelQueryOptions,\n}) => {\n  const [selectedTeam, setSelectedTeam] = useState<MsTeamsTeam | null>(null);\n\n  const { data: currentConnections } = useConnectedMsTeamsChannels({\n    msTeamsChannelsRecipientObject,\n  });\n\n  const getChannelCount = useCallback(\n    (teamId: string) =>\n      currentConnections?.filter(\n        (connection) =>\n          connection.ms_teams_team_id === teamId &&\n          !!connection.ms_teams_channel_id,\n      ).length ?? 0,\n    [currentConnections],\n  );\n\n  return (\n    <Stack className=\"tgph rtk-combobox__grid\" gap=\"3\">\n      <Text color=\"gray\" size=\"2\" as=\"div\">\n        Team\n      </Text>\n      <MsTeamsTeamCombobox\n        team={selectedTeam}\n        onTeamChange={setSelectedTeam}\n        getChannelCount={getChannelCount}\n        queryOptions={teamQueryOptions}\n      />\n      <Stack\n        alignItems=\"center\"\n        gap=\"3\"\n        minHeight=\"8\"\n        style={{ alignSelf: \"start\" }}\n      >\n        <Icon color=\"gray\" size=\"1\" icon={Lucide.CornerDownRight} aria-hidden />\n        <Text color=\"gray\" size=\"2\" as=\"div\">\n          Channels\n        </Text>\n      </Stack>\n      <MsTeamsChannelInTeamCombobox\n        teamId={selectedTeam?.id}\n        msTeamsChannelsRecipientObject={msTeamsChannelsRecipientObject}\n        queryOptions={channelQueryOptions}\n      />\n      <MsTeamsConnectionError />\n    </Stack>\n  );\n};\n\nexport default MsTeamsChannelCombobox;\n"], "names": ["MsTeamsChannelCombobox", "msTeamsChannelsRecipientObject", "teamQueryOptions", "channelQueryOptions", "selectedTeam", "setSelectedTeam", "useState", "data", "currentConnections", "useConnectedMsTeamsChannels", "getChannelCount", "useCallback", "teamId", "filter", "connection", "ms_teams_team_id", "ms_teams_channel_id", "length", "React", "<PERSON><PERSON>", "Text", "MsTeamsTeamCombobox", "alignSelf", "Icon", "Lucide", "CornerDownRight", "MsTeamsChannelInTeamCombobox", "id", "MsTeamsConnectionError"], "mappings": ";;;;;;;;;;AAyBA,MAAMA,IAAmDA,CAAC;AAAA,EACxDC,gCAAAA;AAAAA,EACAC,kBAAAA;AAAAA,EACAC,qBAAAA;AACF,MAAM;AACJ,QAAM,CAACC,GAAcC,CAAe,IAAIC,EAA6B,IAAI,GAEnE;AAAA,IAAEC,MAAMC;AAAAA,MAAuBC,EAA4B;AAAA,IAC/DR,gCAAAA;AAAAA,EAAAA,CACD,GAEKS,IAAkBC,EACtB,CAACC,OACCJ,KAAAA,gBAAAA,EAAoBK,OACjBC,OACCA,EAAWC,qBAAqBH,KAChC,CAAC,CAACE,EAAWE,qBACfC,WAAU,GACd,CAACT,CAAkB,CACrB;AAEA,SACGU,gBAAAA,EAAA,cAAAC,GAAA,EAAM,WAAU,2BAA0B,KAAI,IAC7C,GAAAD,gBAAAA,EAAA,cAACE,GAAK,EAAA,OAAM,QAAO,MAAK,KAAI,IAAG,SAAK,MAEpC,GACAF,gBAAAA,EAAA,cAACG,GACC,EAAA,MAAMjB,GACN,cAAcC,GACd,iBAAAK,GACA,cAAcR,GAAiB,GAEhCgB,gBAAAA,EAAA,cAAAC,GAAA,EACC,YAAW,UACX,KAAI,KACJ,WAAU,KACV,OAAO;AAAA,IAAEG,WAAW;AAAA,EAAA,KAEpBJ,gBAAAA,EAAA,cAACK,GAAK,EAAA,OAAM,QAAO,MAAK,KAAI,MAAMC,EAAOC,iBAAiB,eAAW,GAAA,CAAA,GACrEP,gBAAAA,EAAA,cAACE,KAAK,OAAM,QAAO,MAAK,KAAI,IAAG,MAAK,GAAA,UAEpC,CACF,mCACCM,GACC,EAAA,QAAQtB,KAAAA,gBAAAA,EAAcuB,IACtB,gCAAA1B,GACA,cAAcE,EAAoB,CAAA,GAEpCe,gBAAAA,EAAA,cAACU,OAAsB,CACzB;AAEJ;"}