"use strict";
exports.ar = void 0;
var _index = require("./ar/_lib/formatDistance.cjs");
var _index2 = require("./ar/_lib/formatLong.cjs");
var _index3 = require("./ar/_lib/formatRelative.cjs");
var _index4 = require("./ar/_lib/localize.cjs");
var _index5 = require("./ar/_lib/match.cjs");

/**
 * @category Locales
 * @summary Arabic locale (Modern Standard Arabic - Al-fussha).
 * @language Modern Standard Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@AbdallahAHO](https://github.com/AbdallahAHO)
 * <AUTHOR> [@essana3](https://github.com/essana3)
 */
const ar = (exports.ar = {
  code: "ar",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 6 /* Saturday */,
    firstWeekContainsDate: 1,
  },
});
