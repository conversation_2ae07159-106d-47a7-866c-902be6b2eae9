import { RecipientObject, SlackChannelQueryOptions } from '@knocklabs/react-core';
import { FunctionComponent } from 'react';
export type SlackChannelComboboxInputMessages = {
    disconnected: string;
    error: string;
    noChannelsConnected: string;
    noSlackChannelsFound: string;
};
export interface SlackChannelComboboxProps {
    slackChannelsRecipientObject: RecipientObject;
    queryOptions?: SlackChannelQueryOptions;
    inputMessages?: SlackChannelComboboxInputMessages;
}
export declare const SlackChannelCombobox: FunctionComponent<SlackChannelComboboxProps>;
//# sourceMappingURL=SlackChannelCombobox.d.ts.map