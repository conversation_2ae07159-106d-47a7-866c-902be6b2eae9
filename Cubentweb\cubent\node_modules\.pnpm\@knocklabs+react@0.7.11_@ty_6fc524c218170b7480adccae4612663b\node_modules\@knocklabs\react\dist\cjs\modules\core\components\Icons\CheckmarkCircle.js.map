{"version": 3, "file": "CheckmarkCircle.js", "sources": ["../../../../../../src/modules/core/components/Icons/CheckmarkCircle.tsx"], "sourcesContent": ["import { FunctionComponent } from \"react\";\n\ntype CheckmarkCircleProps = {\n  width?: number;\n  height?: number;\n  \"aria-hidden\"?: boolean;\n};\n\nconst CheckmarkCircle: FunctionComponent<CheckmarkCircleProps> = ({\n  width = 16,\n  height = 16,\n  \"aria-hidden\": aria<PERSON>id<PERSON>,\n}) => (\n  <svg\n    width={width}\n    height={height}\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    aria-hidden={ariaHidden}\n  >\n    <path\n      d=\"M14 8.00012C14 4.68762 11.3125 2.00012 7.99997 2.00012C4.68747 2.00012 1.99997 4.68762 1.99997 8.00012C1.99997 11.3126 4.68747 14.0001 7.99997 14.0001C11.3125 14.0001 14 11.3126 14 8.00012Z\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeMiterlimit=\"10\"\n    />\n    <path\n      d=\"M10.9999 5.5004L6.79994 10.5004L4.99994 8.5004\"\n      stroke=\"currentColor\"\n      strokeWidth=\"1.5\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    />\n  </svg>\n);\n\nexport { CheckmarkCircle };\n"], "names": ["CheckmarkCircle", "width", "height", "ariaHidden", "React"], "mappings": "wKAQMA,EAA2DA,CAAC,CAChEC,MAAAA,EAAQ,GACRC,OAAAA,EAAS,GACT,cAAeC,CACjB,IACGC,EAAA,QAAA,cAAA,MAAA,CACC,MAAAH,EACA,OAAAC,EACA,QAAQ,YACR,KAAK,OACL,MAAM,6BACN,cAAaC,CAAAA,EAEZC,EAAA,QAAA,cAAA,OAAA,CACC,EAAE,gMACF,OAAO,eACP,YAAY,MACZ,iBAAiB,IAAA,CAAI,EAEvBA,EAAAA,QAAA,cAAC,QACC,EAAE,iDACF,OAAO,eACP,YAAY,MACZ,cAAc,QACd,eAAe,QAAO,CAE1B"}