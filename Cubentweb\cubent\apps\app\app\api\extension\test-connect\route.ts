import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { NextResponse } from 'next/server';

/**
 * Test endpoint to manually create an extension session
 * This simulates what the extension should do when it connects to Cubent Cloud
 */
export async function POST() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - Please make sure you are logged into the webapp' },
        { status: 401 }
      );
    }

    // Get user from database
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Create a test extension session
    const sessionId = `test_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session = await database.extensionSession.create({
      data: {
        userId: dbUser.id,
        sessionId,
        isActive: true,
        lastActiveAt: new Date(),
        extensionVersion: 'test-connection',
        vscodeVersion: 'unknown',
        platform: 'test',
        tokensUsed: 0,
        requestsMade: 0,
      },
    });

    // Update user's last extension sync
    await database.user.update({
      where: { id: dbUser.id },
      data: {
        lastExtensionSync: new Date(),
        lastActiveAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Test extension session created successfully!',
      session: {
        id: session.id,
        sessionId: session.sessionId,
        isActive: session.isActive,
        lastActiveAt: session.lastActiveAt,
      },
      user: {
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
      },
    });
  } catch (error) {
    console.error('Test connect error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * Get current connection status
 */
export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database with sessions
    const dbUser = await database.user.findUnique({
      where: { clerkId: userId },
      include: {
        extensionSessions: {
          where: { isActive: true },
          orderBy: { lastActiveAt: 'desc' },
        },
      },
    });

    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      connected: dbUser.extensionSessions.length > 0,
      activeSessions: dbUser.extensionSessions.length,
      lastSync: dbUser.lastExtensionSync,
      sessions: dbUser.extensionSessions.map(session => ({
        id: session.id,
        sessionId: session.sessionId,
        lastActiveAt: session.lastActiveAt,
        extensionVersion: session.extensionVersion,
        platform: session.platform,
      })),
    });
  } catch (error) {
    console.error('Test connect status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
