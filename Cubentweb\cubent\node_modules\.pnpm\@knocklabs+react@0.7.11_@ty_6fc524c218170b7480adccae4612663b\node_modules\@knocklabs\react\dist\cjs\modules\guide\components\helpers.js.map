{"version": 3, "file": "helpers.js", "sources": ["../../../../../src/modules/guide/components/helpers.ts"], "sourcesContent": ["export const isValidHttpUrl = (input: string) => {\n  let url;\n\n  try {\n    url = new URL(input);\n  } catch (_) {\n    return false;\n  }\n\n  return url.protocol === \"http:\" || url.protocol === \"https:\";\n};\n\nexport const maybeNavigateToUrlWithDelay = (\n  url: string,\n  delay: number = 200,\n) => {\n  if (!window?.location) return;\n  if (!isValidHttpUrl(url)) return;\n\n  setTimeout(() => window.location.assign(url), delay);\n};\n"], "names": ["isValidHttpUrl", "input", "url", "URL", "protocol", "maybeNavigateToUrlWithDelay", "delay", "window", "location", "setTimeout", "assign"], "mappings": "gFAAaA,MAAAA,EAAkBC,GAAkB,CAC3CC,IAAAA,EAEA,GAAA,CACIA,EAAA,IAAIC,IAAIF,CAAK,OACT,CACH,MAAA,EAAA,CAGT,OAAOC,EAAIE,WAAa,SAAWF,EAAIE,WAAa,QACtD,EAEaC,EAA8BA,CACzCH,EACAI,EAAgB,MACb,CACEC,qBAAQC,UACRR,EAAeE,CAAG,GAEvBO,WAAW,IAAMF,OAAOC,SAASE,OAAOR,CAAG,EAAGI,CAAK,CACrD"}